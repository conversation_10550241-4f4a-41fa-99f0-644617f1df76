<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柴管家 - 多平台聚合智能客服系统</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    


    <style>
        :root {
            --md-sys-color-primary: #1976d2;
            --md-sys-color-on-primary: #ffffff;
            --md-sys-color-primary-container: #e3f2fd;
            --md-sys-color-on-primary-container: #0d47a1;
            --md-sys-color-secondary: #03dac6;
            --md-sys-color-on-secondary: #000000;
            --md-sys-color-surface: #ffffff;
            --md-sys-color-on-surface: #1c1b1f;
            --md-sys-color-surface-variant: #f5f5f5;
            --md-sys-color-on-surface-variant: #49454f;
            --md-sys-color-error: #ba1a1a;
            --md-sys-color-on-error: #ffffff;
            --md-sys-color-outline: #79747e;
            --md-sys-color-outline-variant: #cac4d0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: var(--md-sys-color-surface, #ffffff);
            color: var(--md-sys-color-on-surface, #1c1b1f);
            line-height: 1.5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
            width: 100%;
        }

        /* 页面容器 */
        .page {
            display: none;
            width: 100%;
            min-height: 100vh;
            position: relative;
        }

        .page.active {
            display: flex;
            flex-direction: column;
        }

        /* 登录页面样式 */
        .login-page {
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .login-card {
            background: var(--md-sys-color-surface);
            border-radius: 28px;
            padding: 48px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: var(--md-sys-color-primary);
            border-radius: 20px;
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-primary);
            font-size: 32px;
            font-weight: 700;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 24px;
            margin-top: 32px;
        }

        .form-field {
            position: relative;
        }

        .form-field md-filled-text-field {
            width: 100%;
        }

        .login-tabs {
            display: flex;
            background: var(--md-sys-color-surface-variant);
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 24px;
        }

        .login-tab {
            flex: 1;
            padding: 12px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .login-tab.active {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .verification-row {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .verification-row .input-container {
            flex: 1;
        }

        .verification-row .material-button {
            height: 56px;
            border-radius: 4px;
        }

        .countdown-btn {
            min-width: 120px;
        }

        /* Material Design 输入框样式 */
        .input-container {
            position: relative;
            margin-bottom: 8px;
        }

        .material-input {
            width: 100%;
            height: 56px;
            padding: 16px 16px 8px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 16px;
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            transition: all 0.2s ease;
            outline: none;
            box-sizing: border-box;
        }

        .material-input:focus {
            border-color: #1976d2;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
        }

        .input-label {
            position: absolute;
            left: 16px;
            top: 8px;
            font-size: 12px;
            color: #666;
            font-family: 'Roboto', sans-serif;
            pointer-events: none;
            transition: all 0.2s ease;
        }

        .material-input:focus + .input-label {
            color: #1976d2;
        }

        /* Material Design 按钮样式 */
        .material-button {
            padding: 0 24px;
            height: 40px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            font-family: 'Roboto', sans-serif;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .material-button.primary {
            background-color: #1976d2;
            color: white;
        }

        .material-button.primary:hover {
            background-color: #1565c0;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
        }

        .material-button.primary:active {
            background-color: #0d47a1;
        }

        .material-button.outlined {
            background-color: transparent;
            color: #1976d2;
            border: 1px solid #1976d2;
        }

        .material-button.outlined:hover {
            background-color: rgba(25, 118, 210, 0.08);
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
        }

        .material-button.outlined:active {
            background-color: rgba(25, 118, 210, 0.16);
        }

        /* 聊天输入框样式 */
        .chat-input-container {
            flex: 1;
            position: relative;
        }

        .chat-input-field {
            width: 100%;
            height: 40px;
            padding: 8px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 14px;
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            transition: all 0.2s ease;
            outline: none;
            box-sizing: border-box;
        }

        .chat-input-field:focus {
            border-color: #1976d2;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
        }

        /* 图标按钮样式 */
        .icon-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background-color: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            outline: none;
        }

        .icon-button:hover {
            background-color: rgba(25, 118, 210, 0.08);
        }

        .icon-button .material-icons {
            font-size: 20px;
            color: #666;
        }

        /* FAB按钮样式 */
        .fab-button {
            width: 56px;
            height: 56px;
            border: none;
            border-radius: 50%;
            background-color: #1976d2;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            outline: none;
            box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 6px 10px 0 rgba(0,0,0,.14), 0 1px 18px 0 rgba(0,0,0,.12);
        }

        .fab-button:hover {
            background-color: #1565c0;
            box-shadow: 0 5px 5px -3px rgba(0,0,0,.2), 0 8px 10px 1px rgba(0,0,0,.14), 0 3px 14px 2px rgba(0,0,0,.12);
        }

        .fab-button .material-icons {
            font-size: 24px;
        }

        /* 搜索输入框样式 */
        .search-input-container {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 8px 16px 8px 48px;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 14px;
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            transition: all 0.2s ease;
            outline: none;
        }

        .search-input:focus {
            border-color: #1976d2;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            color: #666;
            font-size: 20px;
            z-index: 1;
        }

        /* 图标按钮样式 */
        .icon-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 20px;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            color: #666;
        }

        .icon-button:hover {
            background-color: rgba(25, 118, 210, 0.08);
            color: #1976d2;
        }

        .icon-button .material-icons {
            font-size: 20px;
        }

        /* 过滤芯片样式 */
        .filter-chip {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 16px;
            background: #fff;
            color: #666;
            font-size: 12px;
            font-family: 'Roboto', sans-serif;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
        }

        .filter-chip:hover {
            background: #f5f5f5;
            border-color: #1976d2;
        }

        .filter-chip.active {
            background: #1976d2;
            color: #fff;
            border-color: #1976d2;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #1976d2;
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 文本按钮样式 */
        .text-button {
            background: none;
            border: none;
            color: #1976d2;
            font-size: 14px;
            font-family: 'Roboto', sans-serif;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.2s ease;
            outline: none;
        }

        .text-button:hover {
            background-color: rgba(25, 118, 210, 0.08);
        }

        /* 主应用布局 */
        .main-app {
            display: flex;
            flex-direction: row;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        .sidebar {
            width: 280px;
            min-width: 280px;
            background: var(--md-sys-color-surface-variant, #f5f5f5);
            border-right: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .sidebar-logo {
            width: 40px;
            height: 40px;
            background: var(--md-sys-color-primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-primary);
            font-weight: 700;
        }

        .nav-menu {
            flex: 1;
            padding: 16px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px 24px;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            color: var(--md-sys-color-on-surface-variant);
        }

        .nav-item:hover {
            background: rgba(25, 118, 210, 0.08);
        }

        .nav-item.active {
            background: var(--md-sys-color-primary-container);
            color: var(--md-sys-color-on-primary-container);
            border-right: 3px solid var(--md-sys-color-primary);
        }

        .nav-item .material-icons {
            font-size: 24px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            min-width: 0;
        }

        .top-bar {
            height: 64px;
            min-height: 64px;
            background: var(--md-sys-color-surface, #ffffff);
            border-bottom: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            flex-shrink: 0;
        }

        .search-bar {
            flex: 1;
            max-width: 400px;
            margin: 0 24px;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .content-area {
            flex: 1;
            overflow: hidden;
            position: relative;
            min-height: 0;
        }

        .content-view {
            display: none;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        .content-view.active {
            display: flex;
            flex-direction: column;
        }

        /* 工作台样式 */
        .workspace {
            display: flex;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        .conversation-list {
            width: 350px;
            min-width: 350px;
            border-right: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }

        .conversation-header {
            padding: 16px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .conversation-scroll {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .conversation-item {
            padding: 16px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            gap: 12px;
        }

        .conversation-item:hover {
            background: var(--md-sys-color-surface-variant);
        }

        .conversation-item.active {
            background: var(--md-sys-color-primary-container);
        }

        .conversation-avatar {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            background: var(--md-sys-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-secondary);
            font-weight: 500;
            flex-shrink: 0;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-name {
            font-weight: 500;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .platform-badge {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .conversation-preview {
            color: var(--md-sys-color-on-surface-variant);
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .conversation-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 4px;
        }

        .conversation-time {
            font-size: 12px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .unread-badge {
            background: var(--md-sys-color-error);
            color: var(--md-sys-color-on-error);
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 12px;
            font-weight: 500;
            min-width: 20px;
            text-align: center;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            min-width: 0;
            overflow: hidden;
        }

        .chat-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .ai-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .ai-status {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .ai-status.manual {
            background: var(--md-sys-color-surface-variant);
            color: var(--md-sys-color-on-surface-variant);
        }

        .ai-status.auto {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .ai-status.pending {
            background: #ff9800;
            color: white;
        }

        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            gap: 16px;
            min-height: 0;
            background: var(--md-sys-color-surface, #ffffff);
        }

        .message {
            display: flex;
            gap: 12px;
            max-width: 70%;
        }

        .message.sent {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: var(--md-sys-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-secondary);
            font-size: 14px;
            flex-shrink: 0;
        }

        .message-content {
            background: var(--md-sys-color-surface-variant);
            padding: 12px 16px;
            border-radius: 16px;
            position: relative;
        }

        .message.sent .message-content {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .message-time {
            font-size: 12px;
            color: var(--md-sys-color-on-surface-variant);
            margin-top: 4px;
        }

        .ai-assistant {
            width: 300px;
            min-width: 300px;
            border-left: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            background: var(--md-sys-color-surface-variant, #f5f5f5);
            padding: 16px;
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .ai-analysis {
            background: var(--md-sys-color-surface);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .ai-suggestions {
            background: var(--md-sys-color-surface);
            border-radius: 12px;
            padding: 16px;
        }

        .suggestion-item {
            background: var(--md-sys-color-primary-container);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .suggestion-item:hover {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .chat-input {
            padding: 16px 24px;
            border-top: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            display: flex;
            gap: 12px;
            align-items: flex-end;
            background: var(--md-sys-color-surface, #ffffff);
            flex-shrink: 0;
            min-height: 80px;
        }

        .input-field {
            flex: 1;
        }

        /* 渠道管理样式 */
        .channel-grid {
            padding: 24px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
        }

        .channel-card {
            background: var(--md-sys-color-surface);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid var(--md-sys-color-outline-variant);
        }

        .channel-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .channel-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: var(--md-sys-color-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-primary);
            font-size: 24px;
        }

        .channel-info h3 {
            margin-bottom: 4px;
        }

        .channel-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-dot.online {
            background: #4caf50;
        }

        .status-dot.offline {
            background: #f44336;
        }

        .channel-stats {
            margin: 16px 0;
            padding: 16px;
            background: var(--md-sys-color-surface-variant);
            border-radius: 8px;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .channel-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        /* 添加渠道FAB */
        .add-channel-fab {
            position: fixed;
            bottom: 24px;
            right: 24px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                order: 2;
            }

            .main-content {
                order: 1;
            }

            .workspace {
                flex-direction: column;
            }

            .conversation-list {
                width: 100%;
                height: 300px;
            }

            .ai-assistant {
                width: 100%;
                height: 200px;
            }

            .channel-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.3s ease-in-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }

        /* 隐藏类 */
        .hidden {
            display: none !important;
        }



        /* 错误提示样式 */
        .error-message {
            color: var(--md-sys-color-error);
            font-size: 14px;
            margin-top: 8px;
        }

        /* 成功提示样式 */
        .success-message {
            color: #4caf50;
            font-size: 14px;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="page login-page active">
        <div class="login-card fade-in">
            <div class="logo">柴</div>
            <h1 class="md-typescale-headline-medium">柴管家</h1>
            <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant); margin-top: 8px;">
                多平台聚合智能客服系统
            </p>
            
            <div class="login-form">
                <div class="login-tabs">
                    <button class="login-tab active" onclick="switchLoginTab('sms')">验证码登录</button>
                    <button class="login-tab" onclick="switchLoginTab('password')">密码登录</button>
                </div>

                <div class="form-field">
                    <div class="input-container">
                        <input 
                            id="phoneInput"
                            type="tel"
                            maxlength="11"
                            placeholder="请输入手机号"
                            class="material-input">
                        <label for="phoneInput" class="input-label">手机号</label>
                    </div>
                    <div id="phoneError" class="error-message hidden"></div>
                </div>

                <!-- 验证码登录表单 -->
                <div id="smsForm">
                    <div class="form-field">
                        <div class="verification-row">
                            <div class="input-container">
                                <input 
                                    id="smsCodeInput"
                                    type="text"
                                    maxlength="6"
                                    placeholder="请输入验证码"
                                    class="material-input">
                                <label for="smsCodeInput" class="input-label">验证码</label>
                            </div>
                            <button 
                                id="getSmsBtn"
                                class="material-button primary countdown-btn"
                                onclick="getSmsCode()">
                                获取验证码
                            </button>
                        </div>
                        <div id="smsError" class="error-message hidden"></div>
                    </div>
                </div>

                <!-- 密码登录表单 -->
                <div id="passwordForm" class="hidden">
                    <div class="form-field">
                        <div class="input-container">
                            <input 
                                id="passwordInput"
                                type="password"
                                placeholder="请输入密码"
                                class="material-input">
                            <label for="passwordInput" class="input-label">密码</label>
                        </div>
                        <div id="passwordError" class="error-message hidden"></div>
                    </div>
                </div>

                <button onclick="login()" class="material-button primary" style="width: 100%; height: 48px;">
                    登录
                </button>

                <div style="text-align: center; margin-top: 16px;">
                    <button class="text-button" onclick="showRegister()">立即注册</button>
                    <span style="margin: 0 8px; color: var(--md-sys-color-outline);">|</span>
                    <button class="text-button" onclick="showForgotPassword()">忘记密码</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主应用页面 -->
    <div id="mainApp" class="page main-app">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">柴</div>
                <div>
                    <h3 class="md-typescale-title-medium">柴管家</h3>
                    <p class="md-typescale-body-small" style="color: var(--md-sys-color-on-surface-variant);">
                        智能客服系统
                    </p>
                </div>
            </div>
            
            <nav class="nav-menu">
                <button class="nav-item active" onclick="switchView('workspace')">
                    <span class="material-icons">dashboard</span>
                    <span>工作台</span>
                </button>
                <button class="nav-item" onclick="switchView('channels')">
                    <span class="material-icons">hub</span>
                    <span>渠道管理</span>
                </button>
                <button class="nav-item" onclick="switchView('knowledge')">
                    <span class="material-icons">psychology</span>
                    <span>知识库</span>
                </button>
                <button class="nav-item" onclick="switchView('analytics')">
                    <span class="material-icons">analytics</span>
                    <span>数据分析</span>
                </button>
                <button class="nav-item" onclick="switchView('settings')">
                    <span class="material-icons">settings</span>
                    <span>系统设置</span>
                </button>
            </nav>

            <div style="padding: 24px; border-top: 1px solid var(--md-sys-color-outline-variant);">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="width: 32px; height: 32px; border-radius: 16px; background: var(--md-sys-color-primary); display: flex; align-items: center; justify-content: center; color: var(--md-sys-color-on-primary); font-weight: 500;">
                        思
                    </div>
                    <div>
                        <div class="md-typescale-body-medium">思思</div>
                        <div class="md-typescale-body-small" style="color: var(--md-sys-color-on-surface-variant);">
                            知识IP主理人
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="top-bar">
                <h2 id="pageTitle" class="md-typescale-headline-small">工作台</h2>
                
                <div class="search-bar">
                    <div class="search-input-container">
                        <span class="material-icons search-icon">search</span>
                        <input 
                            type="search"
                            placeholder="搜索消息和联系人"
                            class="search-input">
                    </div>
                </div>

                <div class="user-menu">
                    <button class="icon-button" onclick="showNotifications()">
                        <span class="material-icons">notifications</span>
                    </button>
                    <button class="icon-button" onclick="showUserMenu()">
                        <span class="material-icons">account_circle</span>
                    </button>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 工作台视图 -->
                <div id="workspaceView" class="content-view active">
                    <div class="workspace">
                        <!-- 会话列表 -->
                        <div class="conversation-list">
                            <div class="conversation-header">
                                <h3 class="md-typescale-title-medium">消息中心</h3>
                                <div style="display: flex; gap: 8px; margin-top: 8px;">
                                    <button class="filter-chip active">全部</button>
                                    <button class="filter-chip">未读</button>
                                    <button class="filter-chip">微信</button>
                                    <button class="filter-chip">抖音</button>
                                </div>
                            </div>
                            
                            <div class="conversation-scroll">
                                <div class="conversation-item active" onclick="selectConversation(this, '张三')">
                                    <div class="conversation-avatar">张</div>
                                    <div class="conversation-info">
                                        <div class="conversation-name">
                                            张三
                                            <span class="platform-badge">微信</span>
                                        </div>
                                        <div class="conversation-preview">你好，请问有什么产品？</div>
                                        <div class="conversation-meta">
                                            <span class="conversation-time">15:30</span>
                                            <span class="unread-badge">2</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="conversation-item" onclick="selectConversation(this, '李四')">
                                    <div class="conversation-avatar">李</div>
                                    <div class="conversation-info">
                                        <div class="conversation-name">
                                            李四
                                            <span class="platform-badge" style="background: #ff6b35;">抖音</span>
                                        </div>
                                        <div class="conversation-preview">价格怎么样？</div>
                                        <div class="conversation-meta">
                                            <span class="conversation-time">14:20</span>
                                            <span class="unread-badge">1</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="conversation-item" onclick="selectConversation(this, '王五')">
                                    <div class="conversation-avatar">王</div>
                                    <div class="conversation-info">
                                        <div class="conversation-name">
                                            王五
                                            <span class="platform-badge" style="background: #1890ff;">钉钉</span>
                                        </div>
                                        <div class="conversation-preview">什么时候发货？</div>
                                        <div class="conversation-meta">
                                            <span class="conversation-time">13:10</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 聊天区域 -->
                        <div class="chat-area">
                            <div class="chat-header">
                                <div class="chat-title">
                                    <div class="conversation-avatar" style="width: 40px; height: 40px;">张</div>
                                    <div>
                                        <h3 class="md-typescale-title-medium">张三</h3>
                                        <p class="md-typescale-body-small" style="color: var(--md-sys-color-on-surface-variant);">
                                            微信 - 主店铺
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="ai-toggle">
                                    <span class="ai-status manual" id="aiStatus">人工模式</span>
                                    <label class="switch">
                                        <input type="checkbox" id="aiSwitch" onclick="toggleAIMode()">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="chat-messages" id="chatMessages">
                                <div class="message">
                                    <div class="message-avatar">张</div>
                                    <div class="message-content">
                                        <div>你好，请问有什么产品？</div>
                                        <div class="message-time">15:28</div>
                                    </div>
                                </div>

                                <div class="message sent">
                                    <div class="message-avatar">思</div>
                                    <div class="message-content">
                                        <div>您好！感谢您的咨询。我们主要有A产品和B产品两个系列，都是针对不同需求设计的。请问您比较关注哪个方面呢？</div>
                                        <div class="message-time">15:30</div>
                                    </div>
                                </div>

                                <div class="message">
                                    <div class="message-avatar">张</div>
                                    <div class="message-content">
                                        <div>价格大概是多少？</div>
                                        <div class="message-time">15:32</div>
                                    </div>
                                </div>
                            </div>

                            <div class="chat-input">
                                <div class="chat-input-container">
                                    <input 
                                        id="messageInput"
                                        class="chat-input-field"
                                        type="text"
                                        placeholder="输入消息..."
                                        onkeypress="handleEnterKey(event)">
                                </div>
                                <button class="icon-button" onclick="attachFile()">
                                    <span class="material-icons">attach_file</span>
                                </button>
                                <button class="material-button primary" onclick="sendMessage()">
                                    <span class="material-icons">send</span>
                                </button>
                            </div>
                        </div>

                        <!-- AI助手面板 -->
                        <div class="ai-assistant">
                            <div class="ai-analysis">
                                <h4 class="md-typescale-title-small" style="margin-bottom: 12px;">
                                    <span class="material-icons" style="vertical-align: middle; margin-right: 8px;">psychology</span>
                                    AI分析
                                </h4>
                                <div style="background: var(--md-sys-color-primary-container); padding: 12px; border-radius: 8px; margin-bottom: 8px;">
                                    <strong>意图识别：</strong>咨询产品价格
                                </div>
                                <div style="background: var(--md-sys-color-secondary-container); padding: 12px; border-radius: 8px;">
                                    <strong>置信度：</strong>85%
                                </div>
                            </div>

                            <div class="ai-suggestions">
                                <h4 class="md-typescale-title-small" style="margin-bottom: 12px;">
                                    <span class="material-icons" style="vertical-align: middle; margin-right: 8px;">lightbulb</span>
                                    回复建议
                                </h4>
                                
                                <div class="suggestion-item" onclick="useSuggestion(this)">
                                    我们的A产品价格在299-599元之间，B产品价格在199-399元之间。具体价格会根据您选择的配置有所不同，我可以为您详细介绍一下吗？
                                </div>
                                
                                <div class="suggestion-item" onclick="useSuggestion(this)">
                                    感谢您对价格的关注！我们有不同价位的产品可以满足您的需求。请问您的预算大概在什么范围呢？这样我可以为您推荐最合适的产品。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 渠道管理视图 -->
                <div id="channelsView" class="content-view">
                    <div class="channel-grid">
                        <div class="channel-card fade-in">
                            <div class="channel-header">
                                <div class="channel-icon">📱</div>
                                <div class="channel-info">
                                    <h3 class="md-typescale-title-medium">微信 - 主店铺</h3>
                                    <div class="channel-status">
                                        <div class="status-dot online"></div>
                                        <span>在线</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="channel-stats">
                                <div class="stat-row">
                                    <span>今日消息</span>
                                    <strong>25条</strong>
                                </div>
                                <div class="stat-row">
                                    <span>最后活跃</span>
                                    <span>15:30</span>
                                </div>
                                <div class="stat-row">
                                    <span>连接时间</span>
                                    <span>2025-01-27 10:30</span>
                                </div>
                            </div>
                            
                            <div class="channel-actions">
                                <button class="material-button outlined" onclick="editChannel('wechat1')">编辑</button>
                                <button class="material-button outlined" onclick="pauseChannel('wechat1')">暂停</button>
                            </div>
                        </div>

                        <div class="channel-card fade-in">
                            <div class="channel-header">
                                <div class="channel-icon" style="background: #ff6b35;">📺</div>
                                <div class="channel-info">
                                    <h3 class="md-typescale-title-medium">抖音 - 官方号</h3>
                                    <div class="channel-status">
                                        <div class="status-dot online"></div>
                                        <span>在线</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="channel-stats">
                                <div class="stat-row">
                                    <span>今日消息</span>
                                    <strong>18条</strong>
                                </div>
                                <div class="stat-row">
                                    <span>最后活跃</span>
                                    <span>14:20</span>
                                </div>
                                <div class="stat-row">
                                    <span>连接时间</span>
                                    <span>2025-01-27 09:15</span>
                                </div>
                            </div>
                            
                            <div class="channel-actions">
                                <button class="material-button outlined" onclick="editChannel('douyin1')">编辑</button>
                                <button class="material-button outlined" onclick="pauseChannel('douyin1')">暂停</button>
                            </div>
                        </div>

                        <div class="channel-card fade-in">
                            <div class="channel-header">
                                <div class="channel-icon" style="background: #1890ff;">💼</div>
                                <div class="channel-info">
                                    <h3 class="md-typescale-title-medium">钉钉 - 客服群</h3>
                                    <div class="channel-status">
                                        <div class="status-dot online"></div>
                                        <span>在线</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="channel-stats">
                                <div class="stat-row">
                                    <span>今日消息</span>
                                    <strong>12条</strong>
                                </div>
                                <div class="stat-row">
                                    <span>最后活跃</span>
                                    <span>13:10</span>
                                </div>
                                <div class="stat-row">
                                    <span>连接时间</span>
                                    <span>2025-01-27 08:45</span>
                                </div>
                            </div>
                            
                            <div class="channel-actions">
                                <button class="material-button outlined" onclick="editChannel('dingtalk1')">编辑</button>
                                <button class="material-button outlined" onclick="pauseChannel('dingtalk1')">暂停</button>
                            </div>
                        </div>
                    </div>

                    <!-- 添加渠道FAB -->
                    <button class="fab-button add-channel-fab" onclick="addChannel()">
                        <span class="material-icons">add</span>
                    </button>
                </div>

                <!-- 知识库视图 -->
                <div id="knowledgeView" class="content-view">
                    <div style="padding: 24px;">
                        <h2 class="md-typescale-headline-medium">知识库管理</h2>
                        <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant); margin-top: 8px;">
                            管理AI回复的知识库内容，提升回复质量和准确性
                        </p>
                        
                        <div style="margin-top: 32px; text-align: center; padding: 64px;">
                            <span class="material-icons" style="font-size: 64px; color: var(--md-sys-color-outline);">psychology</span>
                            <h3 class="md-typescale-title-large" style="margin: 16px 0;">知识库功能开发中</h3>
                            <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant);">
                                即将支持FAQ管理、智能挖掘、效果分析等功能
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 数据分析视图 -->
                <div id="analyticsView" class="content-view">
                    <div style="padding: 24px;">
                        <h2 class="md-typescale-headline-medium">数据分析</h2>
                        <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant); margin-top: 8px;">
                            查看消息统计、AI性能和用户洞察数据
                        </p>
                        
                        <div style="margin-top: 32px; text-align: center; padding: 64px;">
                            <span class="material-icons" style="font-size: 64px; color: var(--md-sys-color-outline);">analytics</span>
                            <h3 class="md-typescale-title-large" style="margin: 16px 0;">数据分析功能开发中</h3>
                            <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant);">
                                即将支持消息统计、AI性能分析、用户洞察等功能
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 系统设置视图 -->
                <div id="settingsView" class="content-view">
                    <div style="padding: 24px;">
                        <h2 class="md-typescale-headline-medium">系统设置</h2>
                        <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant); margin-top: 8px;">
                            配置账户信息、通知设置和安全选项
                        </p>
                        
                        <div style="margin-top: 32px; text-align: center; padding: 64px;">
                            <span class="material-icons" style="font-size: 64px; color: var(--md-sys-color-outline);">settings</span>
                            <h3 class="md-typescale-title-large" style="margin: 16px 0;">系统设置功能开发中</h3>
                            <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant);">
                                即将支持账户设置、通知配置、安全设置等功能
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态管理
        const AppState = {
            currentUser: null,
            currentView: 'workspace',
            currentConversation: null,
            aiMode: false,
            loginMode: 'sms', // 'sms' or 'password'
            smsCountdown: 0
        };

        // 登录相关功能
        function switchLoginTab(mode) {
            AppState.loginMode = mode;
            
            // 更新标签样式
            document.querySelectorAll('.login-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 显示/隐藏对应表单
            const smsForm = document.getElementById('smsForm');
            const passwordForm = document.getElementById('passwordForm');
            
            if (mode === 'sms') {
                smsForm.classList.remove('hidden');
                passwordForm.classList.add('hidden');
            } else {
                smsForm.classList.add('hidden');
                passwordForm.classList.remove('hidden');
            }
            
            // 清除错误信息
            clearErrors();
        }

        function validatePhone(phone) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }

        function getSmsCode() {
            const phoneInput = document.getElementById('phoneInput');
            const phone = phoneInput.value.trim();
            
            // 清除之前的错误
            clearErrors();
            
            // 验证手机号
            if (!phone) {
                showError('phoneError', '请输入手机号');
                return;
            }
            
            if (!validatePhone(phone)) {
                showError('phoneError', '请输入正确的手机号格式');
                return;
            }
            
            // 模拟发送验证码
            const getSmsBtn = document.getElementById('getSmsBtn');
            getSmsBtn.disabled = true;
            AppState.smsCountdown = 60;
            
            const countdown = setInterval(() => {
                getSmsBtn.textContent = `${AppState.smsCountdown}秒后重试`;
                AppState.smsCountdown--;
                
                if (AppState.smsCountdown < 0) {
                    clearInterval(countdown);
                    getSmsBtn.disabled = false;
                    getSmsBtn.textContent = '获取验证码';
                }
            }, 1000);
            
            // 显示成功消息
            showSnackbar('验证码已发送，请查收短信');
        }

        function login() {
            const phoneInput = document.getElementById('phoneInput');
            const phone = phoneInput.value.trim();
            
            // 清除之前的错误
            clearErrors();
            
            // 验证手机号
            if (!phone) {
                showError('phoneError', '请输入手机号');
                return;
            }
            
            if (!validatePhone(phone)) {
                showError('phoneError', '请输入正确的手机号格式');
                return;
            }
            
            if (AppState.loginMode === 'sms') {
                const smsCode = document.getElementById('smsCodeInput').value.trim();
                if (!smsCode) {
                    showError('smsError', '请输入验证码');
                    return;
                }
                if (smsCode.length !== 6) {
                    showError('smsError', '验证码应为6位数字');
                    return;
                }
                
                // 模拟验证码验证
                if (smsCode !== '123456') {
                    showError('smsError', '验证码错误，请重新输入');
                    return;
                }
            } else {
                const password = document.getElementById('passwordInput').value.trim();
                if (!password) {
                    showError('passwordError', '请输入密码');
                    return;
                }
                
                // 模拟密码验证
                if (password !== 'password123') {
                    showError('passwordError', '密码错误，请重新输入');
                    return;
                }
            }
            
            // 登录成功
            AppState.currentUser = {
                phone: phone,
                name: '思思',
                role: '知识IP主理人'
            };
            
            showSnackbar('登录成功！');
            
            // 延迟跳转到主应用
            setTimeout(() => {
                switchPage('mainApp');
            }, 1000);
        }

        function showRegister() {
            showSnackbar('注册功能开发中，请使用演示账号登录');
        }

        function showForgotPassword() {
            showSnackbar('密码重置功能开发中');
        }

        // 主应用功能
        function switchView(viewName) {
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新内容视图
            document.querySelectorAll('.content-view').forEach(view => {
                view.classList.remove('active');
            });
            document.getElementById(viewName + 'View').classList.add('active');
            
            // 更新页面标题
            const titles = {
                workspace: '工作台',
                channels: '渠道管理',
                knowledge: '知识库',
                analytics: '数据分析',
                settings: '系统设置'
            };
            document.getElementById('pageTitle').textContent = titles[viewName];
            
            AppState.currentView = viewName;
        }

        function selectConversation(element, name) {
            // 更新选中状态
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            element.classList.add('active');
            
            // 清除未读标记
            const unreadBadge = element.querySelector('.unread-badge');
            if (unreadBadge) {
                unreadBadge.remove();
            }
            
            AppState.currentConversation = name;
            
            // 更新聊天标题
            const chatTitle = document.querySelector('.chat-title h3');
            chatTitle.textContent = name;
        }

        function toggleAIMode() {
            AppState.aiMode = !AppState.aiMode;
            const aiStatus = document.getElementById('aiStatus');
            
            if (AppState.aiMode) {
                aiStatus.textContent = 'AI托管中';
                aiStatus.className = 'ai-status auto';
                showSnackbar('AI托管模式已开启');
            } else {
                aiStatus.textContent = '人工模式';
                aiStatus.className = 'ai-status manual';
                showSnackbar('已切换到人工模式');
            }
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) return;
            
            // 添加消息到聊天区域
            const chatMessages = document.getElementById('chatMessages');
            const messageElement = createMessageElement(message, true);
            chatMessages.appendChild(messageElement);
            
            // 清空输入框
            messageInput.value = '';
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // 如果是AI托管模式，模拟AI回复
            if (AppState.aiMode) {
                setTimeout(() => {
                    simulateAIReply();
                }, 1000);
            }
        }

        function createMessageElement(content, isSent = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isSent ? 'sent' : ''}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = isSent ? '思' : '张';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            
            const messageText = document.createElement('div');
            messageText.textContent = content;
            
            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.textContent = new Date().toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            
            messageContent.appendChild(messageText);
            messageContent.appendChild(messageTime);
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            return messageDiv;
        }

        function simulateAIReply() {
            const replies = [
                '感谢您的咨询！我来为您详细介绍一下我们的产品。',
                '根据您的需求，我推荐以下几个方案...',
                '这个问题很好，让我为您详细解答。'
            ];
            
            const randomReply = replies[Math.floor(Math.random() * replies.length)];
            const chatMessages = document.getElementById('chatMessages');
            const messageElement = createMessageElement(randomReply, false);
            chatMessages.appendChild(messageElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function useSuggestion(element) {
            const suggestion = element.textContent;
            const messageInput = document.getElementById('messageInput');
            messageInput.value = suggestion;
            messageInput.focus();
        }

        function handleEnterKey(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 渠道管理功能
        function addChannel() {
            showSnackbar('添加渠道功能开发中');
        }

        function editChannel(channelId) {
            showSnackbar(`编辑渠道 ${channelId} 功能开发中`);
        }

        function pauseChannel(channelId) {
            showSnackbar(`渠道 ${channelId} 已暂停`);
        }

        // 工具函数
        function switchPage(pageId) {
            console.log('切换到页面:', pageId);
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                console.log('页面切换成功:', pageId);
            } else {
                console.error('找不到页面元素:', pageId);
            }
        }

        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }

        function clearErrors() {
            document.querySelectorAll('.error-message').forEach(error => {
                error.classList.add('hidden');
                error.textContent = '';
            });
        }

        function showSnackbar(message) {
            // 创建简单的提示消息
            const snackbar = document.createElement('div');
            snackbar.style.cssText = `
                position: fixed;
                bottom: 24px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--md-sys-color-on-surface);
                color: var(--md-sys-color-surface);
                padding: 12px 24px;
                border-radius: 8px;
                z-index: 10000;
                animation: fadeIn 0.3s ease-in-out;
            `;
            snackbar.textContent = message;
            
            document.body.appendChild(snackbar);
            
            setTimeout(() => {
                snackbar.remove();
            }, 3000);
        }

        function showNotifications() {
            showSnackbar('通知功能开发中');
        }

        function showUserMenu() {
            showSnackbar('用户菜单功能开发中');
        }

        function attachFile() {
            showSnackbar('文件上传功能开发中');
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            console.log('柴管家产品原型已加载');
            
            // 设置演示数据提示
            setTimeout(() => {
                if (document.getElementById('loginPage').classList.contains('active')) {
                    showSnackbar('演示账号：任意手机号，验证码：123456，密码：password123');
                }
            }, 2000);
        });

        // 响应式处理
        function handleResize() {
            const isMobile = window.innerWidth <= 768;
            
            if (isMobile) {
                // 移动端适配逻辑
                document.body.classList.add('mobile');
            } else {
                document.body.classList.remove('mobile');
            }
        }

        window.addEventListener('resize', handleResize);
        handleResize(); // 初始化时执行一次
    </script>
</body>
</html>