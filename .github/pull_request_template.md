# Pull Request

## 📝 变更描述

<!-- 请简要描述此 PR 的变更内容 -->

### 变更类型
<!-- 请选择适用的变更类型 -->
- [ ] 🐛 Bug 修复
- [ ] ✨ 新功能
- [ ] 💄 UI/样式更新
- [ ] ♻️ 代码重构
- [ ] 📝 文档更新
- [ ] 🔧 配置变更
- [ ] 🧪 测试相关
- [ ] 🚀 性能优化
- [ ] 🔒 安全修复

### 影响范围
<!-- 请选择此变更影响的范围 -->
- [ ] 前端 (Frontend)
- [ ] 后端 (Backend)
- [ ] 数据库 (Database)
- [ ] 基础设施 (Infrastructure)
- [ ] 文档 (Documentation)
- [ ] CI/CD

## 🔗 相关 Issue

<!-- 请链接相关的 Issue -->
Closes #<!-- Issue 编号 -->

## 🧪 测试

### 测试用例
<!-- 描述添加或修改的测试用例 -->
- [ ] 单元测试已添加/更新
- [ ] 集成测试已添加/更新
- [ ] E2E 测试已添加/更新
- [ ] 手动测试已完成

### 测试覆盖率
<!-- 如果适用，请提供测试覆盖率信息 -->
- 前端覆盖率: <!-- 百分比 -->%
- 后端覆盖率: <!-- 百分比 -->%

### 测试环境
<!-- 描述在哪些环境下进行了测试 -->
- [ ] 本地开发环境
- [ ] Docker 容器环境
- [ ] CI/CD 环境

## 📋 检查清单

### 代码质量
- [ ] 代码遵循项目编码规范
- [ ] 已运行并通过所有代码质量检查 (ESLint, Black, flake8, mypy)
- [ ] 已运行并通过所有测试
- [ ] 代码已经过自我审查
- [ ] 复杂的逻辑已添加注释

### 安全性
- [ ] 没有硬编码的敏感信息 (密码、API 密钥等)
- [ ] 输入验证已正确实现
- [ ] 权限检查已正确实现
- [ ] 已考虑潜在的安全风险

### 性能
- [ ] 已考虑性能影响
- [ ] 数据库查询已优化 (如适用)
- [ ] 前端资源已优化 (如适用)
- [ ] 没有引入明显的性能回归

### 文档
- [ ] 相关文档已更新
- [ ] API 文档已更新 (如适用)
- [ ] README 已更新 (如适用)
- [ ] 变更日志已更新 (如适用)

### 兼容性
- [ ] 向后兼容性已考虑
- [ ] 数据库迁移已测试 (如适用)
- [ ] 依赖版本兼容性已检查

## 📸 截图/演示

<!-- 如果是 UI 变更，请提供截图或 GIF 演示 -->

### 变更前
<!-- 截图或描述 -->

### 变更后
<!-- 截图或描述 -->

## 🚀 部署说明

<!-- 如果需要特殊的部署步骤，请在此说明 -->

### 环境变量
<!-- 如果需要新的环境变量，请列出 -->
- `NEW_ENV_VAR`: 描述

### 数据库迁移
<!-- 如果需要数据库迁移，请说明 -->
- [ ] 需要运行数据库迁移
- [ ] 迁移脚本已测试
- [ ] 回滚方案已准备

### 配置变更
<!-- 如果需要配置变更，请说明 -->
- [ ] 需要更新配置文件
- [ ] 需要重启服务

## 🔄 回滚计划

<!-- 如果出现问题，如何回滚此变更 -->

## 📝 额外说明

<!-- 任何其他需要审查者知道的信息 -->

---

## 审查者检查清单

<!-- 审查者使用的检查清单 -->

### 代码审查
- [ ] 代码逻辑正确
- [ ] 代码风格一致
- [ ] 错误处理适当
- [ ] 性能考虑合理

### 测试审查
- [ ] 测试覆盖充分
- [ ] 测试用例有意义
- [ ] 边界条件已测试
- [ ] 错误情况已测试

### 安全审查
- [ ] 没有安全漏洞
- [ ] 输入验证充分
- [ ] 权限控制正确
- [ ] 敏感数据处理安全

### 文档审查
- [ ] 文档准确完整
- [ ] 示例代码正确
- [ ] 变更说明清晰

---

**提醒**: 
- 请确保所有 CI 检查都通过后再请求审查
- 如果是重大变更，请在团队会议中讨论
- 合并前请确保目标分支是最新的
