# 柴管家项目持续集成流水线
# 根据《柴管家项目初始化方案.md》任务3.1要求实现
# 包含代码检查、测试、构建三个主要阶段，前后端并行执行

name: CI Pipeline

on:
  push:
    branches: [main, develop, "feature/*"]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:

# 环境变量
env:
  NODE_VERSION: "18"
  PYTHON_VERSION: "3.11"
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

# 并发控制 - 同一分支只运行最新的工作流
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # ===== 阶段1：代码质量检查 =====
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    strategy:
      matrix:
        check-type: [frontend, backend]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 获取完整历史，用于代码分析

      # 前端代码质量检查
      - name: 设置 Node.js (前端)
        if: matrix.check-type == 'frontend'
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: frontend/yarn.lock

      - name: 缓存 node_modules (前端)
        if: matrix.check-type == 'frontend'
        uses: actions/cache@v3
        with:
          path: frontend/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('frontend/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: 安装前端依赖
        if: matrix.check-type == 'frontend'
        run: |
          cd frontend
          yarn install --frozen-lockfile

      - name: 前端类型检查
        if: matrix.check-type == 'frontend'
        run: |
          cd frontend
          yarn type-check

      - name: 前端代码格式检查
        if: matrix.check-type == 'frontend'
        run: |
          cd frontend
          yarn format:check

      - name: 前端代码质量检查 (ESLint)
        if: matrix.check-type == 'frontend'
        run: |
          cd frontend
          yarn lint

      # 后端代码质量检查
      - name: 设置 Python (后端)
        if: matrix.check-type == 'backend'
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: "pip"
          cache-dependency-path: backend/requirements-dev.txt

      - name: 缓存 pip 包 (后端)
        if: matrix.check-type == 'backend'
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements-dev.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: 安装后端依赖
        if: matrix.check-type == 'backend'
        run: |
          cd backend
          pip install -r requirements-dev.txt

      - name: 后端代码格式检查 (Black)
        if: matrix.check-type == 'backend'
        run: |
          cd backend
          black --check --diff .

      - name: 后端导入排序检查 (isort)
        if: matrix.check-type == 'backend'
        run: |
          cd backend
          isort --check-only --diff .

      - name: 后端代码质量检查 (flake8)
        if: matrix.check-type == 'backend'
        run: |
          cd backend
          flake8 .

      - name: 后端类型检查 (mypy)
        if: matrix.check-type == 'backend'
        run: |
          cd backend
          mypy .

      - name: 后端安全检查 (bandit)
        if: matrix.check-type == 'backend'
        run: |
          cd backend
          bandit -r . -c pyproject.toml

  # ===== 阶段2：自动化测试 =====
  tests:
    name: 自动化测试
    runs-on: ubuntu-latest
    needs: code-quality
    strategy:
      matrix:
        test-type: [frontend, backend]

    services:
      # 后端测试需要的服务
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_chaiguanjia
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      # 前端测试
      - name: 设置 Node.js (前端测试)
        if: matrix.test-type == 'frontend'
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: frontend/yarn.lock

      - name: 缓存 node_modules (前端测试)
        if: matrix.test-type == 'frontend'
        uses: actions/cache@v3
        with:
          path: frontend/node_modules
          key: ${{ runner.os }}-node-test-${{ hashFiles('frontend/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-node-test-
            ${{ runner.os }}-node-

      - name: 安装前端依赖
        if: matrix.test-type == 'frontend'
        run: |
          cd frontend
          yarn install --frozen-lockfile

      - name: 运行前端测试
        if: matrix.test-type == 'frontend'
        run: |
          cd frontend
          yarn test:coverage

      - name: 上传前端覆盖率报告
        if: matrix.test-type == 'frontend'
        uses: codecov/codecov-action@v3
        with:
          file: ./frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

      # 后端测试
      - name: 设置 Python (后端测试)
        if: matrix.test-type == 'backend'
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: "pip"
          cache-dependency-path: backend/requirements-dev.txt

      - name: 缓存 pip 包 (后端测试)
        if: matrix.test-type == 'backend'
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-test-${{ hashFiles('backend/requirements-dev.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-test-
            ${{ runner.os }}-pip-

      - name: 安装后端依赖
        if: matrix.test-type == 'backend'
        run: |
          cd backend
          pip install -r requirements-dev.txt

      - name: 运行后端测试
        if: matrix.test-type == 'backend'
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_chaiguanjia
          REDIS_URL: redis://localhost:6379/0
          TESTING: true
        run: |
          cd backend
          pytest --cov=app --cov-report=xml --cov-report=html

      - name: 上传后端覆盖率报告
        if: matrix.test-type == 'backend'
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage.xml
          flags: backend
          name: backend-coverage

  # ===== 阶段3：构建和打包 =====
  build:
    name: 构建和打包
    runs-on: ubuntu-latest
    needs: tests
    strategy:
      matrix:
        build-type: [frontend, backend]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      # 前端构建
      - name: 设置 Node.js (前端构建)
        if: matrix.build-type == 'frontend'
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: frontend/yarn.lock

      - name: 缓存 node_modules (前端构建)
        if: matrix.build-type == 'frontend'
        uses: actions/cache@v3
        with:
          path: frontend/node_modules
          key: ${{ runner.os }}-node-build-${{ hashFiles('frontend/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-node-build-
            ${{ runner.os }}-node-test-
            ${{ runner.os }}-node-

      - name: 安装前端依赖
        if: matrix.build-type == 'frontend'
        run: |
          cd frontend
          yarn install --frozen-lockfile

      - name: 构建前端应用
        if: matrix.build-type == 'frontend'
        run: |
          cd frontend
          yarn build

      - name: 上传前端构建产物
        if: matrix.build-type == 'frontend'
        uses: actions/upload-artifact@v3
        with:
          name: frontend-dist
          path: frontend/dist/
          retention-days: 7

      # 后端Docker镜像构建
      - name: 设置 Docker Buildx (后端构建)
        if: matrix.build-type == 'backend'
        uses: docker/setup-buildx-action@v3

      - name: 登录到容器注册表
        if: matrix.build-type == 'backend' && github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        if: matrix.build-type == 'backend'
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-

      - name: 构建并推送后端Docker镜像
        if: matrix.build-type == 'backend'
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # ===== 构建状态汇总 =====
  ci-success:
    name: CI 流水线成功
    runs-on: ubuntu-latest
    needs: [code-quality, tests, build]
    if: always()

    steps:
      - name: 检查所有任务状态
        run: |
          if [[ "${{ needs.code-quality.result }}" == "success" && \
                "${{ needs.tests.result }}" == "success" && \
                "${{ needs.build.result }}" == "success" ]]; then
            echo "✅ 所有CI检查通过！"
            exit 0
          else
            echo "❌ CI检查失败："
            echo "代码质量检查: ${{ needs.code-quality.result }}"
            echo "自动化测试: ${{ needs.tests.result }}"
            echo "构建打包: ${{ needs.build.result }}"
            exit 1
          fi
