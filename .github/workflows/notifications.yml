# 柴管家项目通知和错误报告工作流
# 处理 CI/CD 失败通知和错误报告

name: Notifications

on:
  workflow_run:
    workflows: ["CI Pipeline", "Security Scan", "Coverage Report"]
    types:
      - completed
  workflow_dispatch:

jobs:
  # ===== CI 失败通知 =====
  ci-failure-notification:
    name: CI 失败通知
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'failure' }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 获取失败工作流信息
        id: workflow-info
        run: |
          echo "workflow_name=${{ github.event.workflow_run.name }}" >> $GITHUB_OUTPUT
          echo "workflow_url=${{ github.event.workflow_run.html_url }}" >> $GITHUB_OUTPUT
          echo "commit_sha=${{ github.event.workflow_run.head_sha }}" >> $GITHUB_OUTPUT
          echo "branch=${{ github.event.workflow_run.head_branch }}" >> $GITHUB_OUTPUT
          echo "actor=${{ github.event.workflow_run.actor.login }}" >> $GITHUB_OUTPUT

      - name: 获取提交信息
        id: commit-info
        run: |
          COMMIT_MESSAGE=$(git log --format=%B -n 1 ${{ steps.workflow-info.outputs.commit_sha }} | head -1)
          echo "commit_message=$COMMIT_MESSAGE" >> $GITHUB_OUTPUT
          
          COMMIT_AUTHOR=$(git log --format=%an -n 1 ${{ steps.workflow-info.outputs.commit_sha }})
          echo "commit_author=$COMMIT_AUTHOR" >> $GITHUB_OUTPUT

      - name: 创建失败报告 Issue
        uses: actions/github-script@v6
        with:
          script: |
            const workflowName = '${{ steps.workflow-info.outputs.workflow_name }}';
            const workflowUrl = '${{ steps.workflow-info.outputs.workflow_url }}';
            const commitSha = '${{ steps.workflow-info.outputs.commit_sha }}';
            const branch = '${{ steps.workflow-info.outputs.branch }}';
            const actor = '${{ steps.workflow-info.outputs.actor }}';
            const commitMessage = '${{ steps.commit-info.outputs.commit_message }}';
            const commitAuthor = '${{ steps.commit-info.outputs.commit_author }}';
            
            const title = `🚨 ${workflowName} 失败 - ${branch}`;
            const body = `
            ## 🚨 CI/CD 流水线失败报告
            
            **工作流**: ${workflowName}
            **状态**: ❌ 失败
            **分支**: \`${branch}\`
            **触发者**: @${actor}
            **时间**: ${new Date().toISOString()}
            
            ### 📝 提交信息
            - **提交哈希**: \`${commitSha.substring(0, 8)}\`
            - **提交作者**: ${commitAuthor}
            - **提交消息**: ${commitMessage}
            
            ### 🔗 相关链接
            - [失败的工作流运行](${workflowUrl})
            - [提交详情](${{ github.server_url }}/${{ github.repository }}/commit/${commitSha})
            
            ### 🛠️ 排查建议
            1. 查看工作流日志中的错误信息
            2. 检查最近的代码变更是否引入了问题
            3. 验证依赖是否正确安装
            4. 检查测试用例是否需要更新
            5. 确认环境配置是否正确
            
            ### 📋 处理清单
            - [ ] 分析失败原因
            - [ ] 修复相关问题
            - [ ] 验证修复效果
            - [ ] 更新相关文档（如需要）
            - [ ] 关闭此 Issue
            
            ---
            *此 Issue 由 CI/CD 系统自动创建*
            `;
            
            // 检查是否已存在相同的 Issue
            const existingIssues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              labels: 'ci-failure',
              per_page: 10
            });
            
            const duplicateIssue = existingIssues.data.find(issue => 
              issue.title.includes(workflowName) && 
              issue.title.includes(branch) &&
              issue.body.includes(commitSha.substring(0, 8))
            );
            
            if (!duplicateIssue) {
              const issue = await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: title,
                body: body,
                labels: ['ci-failure', 'bug', 'high-priority', workflowName.toLowerCase().replace(/\s+/g, '-')]
              });
              
              console.log(`Created issue: ${issue.data.html_url}`);
              
              // 如果是主分支失败，添加 critical 标签
              if (branch === 'main' || branch === 'master') {
                await github.rest.issues.addLabels({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.data.number,
                  labels: ['critical']
                });
              }
            } else {
              console.log('Duplicate issue found, skipping creation');
            }

      - name: 发送 Slack 通知 (如果配置了)
        if: env.SLACK_WEBHOOK_URL != ''
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data '{
              "text": "🚨 CI/CD 失败通知",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "🚨 CI/CD 流水线失败"
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*工作流:* ${{ steps.workflow-info.outputs.workflow_name }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*分支:* `${{ steps.workflow-info.outputs.branch }}`"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*触发者:* ${{ steps.workflow-info.outputs.actor }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*提交:* `${{ steps.workflow-info.outputs.commit_sha }}`"
                    }
                  ]
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*提交消息:* ${{ steps.commit-info.outputs.commit_message }}"
                  }
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "查看工作流"
                      },
                      "url": "${{ steps.workflow-info.outputs.workflow_url }}"
                    },
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "查看提交"
                      },
                      "url": "${{ github.server_url }}/${{ github.repository }}/commit/${{ steps.workflow-info.outputs.commit_sha }}"
                    }
                  ]
                }
              ]
            }' \
            $SLACK_WEBHOOK_URL

  # ===== CI 成功通知 (仅主分支) =====
  ci-success-notification:
    name: CI 成功通知
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' && (github.event.workflow_run.head_branch == 'main' || github.event.workflow_run.head_branch == 'master') }}

    steps:
      - name: 发送成功通知
        uses: actions/github-script@v6
        with:
          script: |
            const workflowName = '${{ github.event.workflow_run.name }}';
            const branch = '${{ github.event.workflow_run.head_branch }}';
            const commitSha = '${{ github.event.workflow_run.head_sha }}';
            
            console.log(`✅ ${workflowName} 在 ${branch} 分支成功完成`);
            console.log(`提交: ${commitSha.substring(0, 8)}`);

      - name: 关闭相关的失败 Issue
        uses: actions/github-script@v6
        with:
          script: |
            const workflowName = '${{ github.event.workflow_run.name }}';
            const branch = '${{ github.event.workflow_run.head_branch }}';
            
            // 查找相关的失败 Issue
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              labels: 'ci-failure',
              per_page: 50
            });
            
            for (const issue of issues.data) {
              if (issue.title.includes(workflowName) && issue.title.includes(branch)) {
                await github.rest.issues.createComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  body: `✅ 此问题已修复，${workflowName} 在 \`${branch}\` 分支上成功运行。\n\n自动关闭此 Issue。`
                });
                
                await github.rest.issues.update({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  state: 'closed',
                  labels: [...issue.labels.map(l => l.name), 'resolved']
                });
                
                console.log(`Closed issue #${issue.number}: ${issue.title}`);
              }
            }

  # ===== 错误统计和趋势分析 =====
  error-analytics:
    name: 错误统计和趋势分析
    runs-on: ubuntu-latest
    if: always()

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 生成错误统计报告
        uses: actions/github-script@v6
        with:
          script: |
            const { owner, repo } = context.repo;
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            
            // 获取最近30天的工作流运行
            const workflowRuns = await github.rest.actions.listWorkflowRunsForRepo({
              owner,
              repo,
              per_page: 100,
              created: `>=${thirtyDaysAgo.toISOString()}`
            });
            
            const stats = {
              total: workflowRuns.data.total_count,
              success: 0,
              failure: 0,
              cancelled: 0,
              workflows: {}
            };
            
            workflowRuns.data.workflow_runs.forEach(run => {
              stats[run.conclusion] = (stats[run.conclusion] || 0) + 1;
              
              if (!stats.workflows[run.name]) {
                stats.workflows[run.name] = { success: 0, failure: 0, cancelled: 0 };
              }
              stats.workflows[run.name][run.conclusion] = (stats.workflows[run.name][run.conclusion] || 0) + 1;
            });
            
            const successRate = ((stats.success / stats.total) * 100).toFixed(1);
            
            console.log('## 📊 CI/CD 统计报告 (最近30天)');
            console.log('');
            console.log(`- 总运行次数: ${stats.total}`);
            console.log(`- 成功次数: ${stats.success}`);
            console.log(`- 失败次数: ${stats.failure}`);
            console.log(`- 取消次数: ${stats.cancelled}`);
            console.log(`- 成功率: ${successRate}%`);
            console.log('');
            console.log('### 各工作流统计:');
            Object.entries(stats.workflows).forEach(([name, data]) => {
              const total = data.success + data.failure + data.cancelled;
              const rate = ((data.success / total) * 100).toFixed(1);
              console.log(`- ${name}: ${rate}% (${data.success}/${total})`);
            });
            
            // 如果成功率低于90%，创建警告
            if (parseFloat(successRate) < 90) {
              console.log('');
              console.log('⚠️ 警告: CI/CD 成功率低于90%，建议检查和优化流水线');
            }
