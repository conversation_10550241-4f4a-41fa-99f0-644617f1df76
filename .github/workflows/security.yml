# 柴管家项目安全扫描流水线
# 根据《柴管家项目初始化方案.md》任务3.2要求实现
# 集成CodeQL、依赖漏洞扫描、容器镜像安全扫描

name: Security Scan

on:
  push:
    branches: [main, develop, "feature/*"]
  pull_request:
    branches: [main, develop]
  schedule:
    # 每周一凌晨2点运行安全扫描
    - cron: "0 2 * * 1"
  workflow_dispatch:

# 并发控制
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # ===== CodeQL 代码安全分析 =====
  codeql:
    name: CodeQL 安全分析
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: ["javascript", "python"]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 初始化 CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}
          queries: security-extended,security-and-quality

      # JavaScript/TypeScript 构建
      - name: 设置 Node.js
        if: matrix.language == 'javascript'
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: frontend/package-lock.json

      - name: 安装前端依赖
        if: matrix.language == 'javascript'
        run: |
          cd frontend
          npm ci --prefer-offline --no-audit

      # Python 构建
      - name: 设置 Python
        if: matrix.language == 'python'
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
          cache: "pip"
          cache-dependency-path: backend/requirements.txt

      - name: 安装后端依赖
        if: matrix.language == 'python'
        run: |
          cd backend
          pip install -r requirements.txt

      - name: 执行 CodeQL 分析
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:${{matrix.language}}"

  # ===== 依赖漏洞扫描 =====
  dependency-scan:
    name: 依赖漏洞扫描
    runs-on: ubuntu-latest
    strategy:
      matrix:
        scan-type: [frontend, backend]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      # 前端依赖扫描
      - name: 设置 Node.js (前端)
        if: matrix.scan-type == 'frontend'
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: frontend/package-lock.json

      - name: 安装前端依赖
        if: matrix.scan-type == 'frontend'
        run: |
          cd frontend
          npm ci --prefer-offline --no-audit

      - name: 前端安全审计 (npm audit)
        if: matrix.scan-type == 'frontend'
        run: |
          cd frontend
          npm audit --audit-level=moderate

      - name: 前端依赖漏洞扫描 (Snyk)
        if: matrix.scan-type == 'frontend'
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --file=frontend/package.json
        continue-on-error: true

      # 后端依赖扫描
      - name: 设置 Python (后端)
        if: matrix.scan-type == 'backend'
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
          cache: "pip"
          cache-dependency-path: backend/requirements.txt

      - name: 安装后端依赖
        if: matrix.scan-type == 'backend'
        run: |
          cd backend
          pip install -r requirements-dev.txt

      - name: 后端安全检查 (Safety)
        if: matrix.scan-type == 'backend'
        run: |
          cd backend
          safety check --json --output safety-report.json || true
          if [ -f safety-report.json ]; then
            echo "Safety 扫描完成，检查报告："
            cat safety-report.json
          fi

      - name: 后端依赖漏洞扫描 (Snyk)
        if: matrix.scan-type == 'backend'
        uses: snyk/actions/python@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --file=backend/requirements.txt
        continue-on-error: true

      - name: 上传 Snyk 结果到 GitHub
        if: always()
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: snyk.sarif
        continue-on-error: true

  # ===== 容器镜像安全扫描 =====
  container-scan:
    name: 容器镜像安全扫描
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 构建后端镜像用于扫描
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          load: true
          tags: chaiguanjia-backend:scan
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 运行 Trivy 容器扫描
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: "chaiguanjia-backend:scan"
          format: "sarif"
          output: "trivy-results.sarif"

      - name: 上传 Trivy 扫描结果
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: "trivy-results.sarif"

      - name: 运行 Trivy 文件系统扫描
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: "fs"
          scan-ref: "."
          format: "table"
          exit-code: "1"
          severity: "CRITICAL,HIGH"

  # ===== 密钥泄露检测 =====
  secret-scan:
    name: 密钥泄露检测
    runs-on: ubuntu-latest

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 运行 GitLeaks 扫描
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}

  # ===== 安全扫描汇总 =====
  security-summary:
    name: 安全扫描汇总
    runs-on: ubuntu-latest
    needs: [codeql, dependency-scan, container-scan, secret-scan]
    if: always()

    steps:
      - name: 检查安全扫描结果
        run: |
          echo "🔒 安全扫描结果汇总："
          echo "CodeQL 分析: ${{ needs.codeql.result }}"
          echo "依赖漏洞扫描: ${{ needs.dependency-scan.result }}"
          echo "容器镜像扫描: ${{ needs.container-scan.result }}"
          echo "密钥泄露检测: ${{ needs.secret-scan.result }}"

          # 检查是否有严重失败
          if [[ "${{ needs.codeql.result }}" == "failure" || \
                "${{ needs.secret-scan.result }}" == "failure" ]]; then
            echo "❌ 发现严重安全问题，请立即处理！"
            exit 1
          elif [[ "${{ needs.dependency-scan.result }}" == "failure" || \
                  "${{ needs.container-scan.result }}" == "failure" ]]; then
            echo "⚠️  发现安全风险，建议尽快处理"
            # 不阻止流水线，但记录警告
            exit 0
          else
            echo "✅ 安全扫描通过"
            exit 0
          fi

      - name: 创建安全报告 Issue
        if: failure()
        uses: actions/github-script@v6
        with:
          script: |
            const title = `🔒 安全扫描发现问题 - ${new Date().toISOString().split('T')[0]}`;
            const body = `
            ## 安全扫描结果

            **扫描时间**: ${new Date().toISOString()}
            **触发事件**: ${{ github.event_name }}
            **分支**: ${{ github.ref_name }}
            **提交**: ${{ github.sha }}

            ### 扫描结果
            - CodeQL 分析: ${{ needs.codeql.result }}
            - 依赖漏洞扫描: ${{ needs.dependency-scan.result }}
            - 容器镜像扫描: ${{ needs.container-scan.result }}
            - 密钥泄露检测: ${{ needs.secret-scan.result }}

            ### 处理建议
            1. 查看 Security 标签页的详细报告
            2. 优先处理 CRITICAL 和 HIGH 级别的问题
            3. 更新有漏洞的依赖包
            4. 检查并移除任何泄露的密钥

            ### 相关链接
            - [工作流运行](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
            - [安全报告](${{ github.server_url }}/${{ github.repository }}/security)
            `;

            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['security', 'bug', 'high-priority']
            });
