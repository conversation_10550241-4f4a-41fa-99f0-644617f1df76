# 柴管家项目代码覆盖率报告工作流
# 收集前后端测试覆盖率并生成统一报告

name: Coverage Report

on:
  push:
    branches: [ main, develop, 'feature/*' ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

# 环境变量
env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

# 并发控制
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # ===== 前端覆盖率收集 =====
  frontend-coverage:
    name: 前端覆盖率收集
    runs-on: ubuntu-latest

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'
          cache-dependency-path: frontend/yarn.lock

      - name: 安装前端依赖
        run: |
          cd frontend
          yarn install --frozen-lockfile

      - name: 运行前端测试并收集覆盖率
        run: |
          cd frontend
          yarn test:coverage

      - name: 上传前端覆盖率到 Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage
          fail_ci_if_error: false

      - name: 上传前端覆盖率报告
        uses: actions/upload-artifact@v3
        with:
          name: frontend-coverage-report
          path: |
            frontend/coverage/
          retention-days: 30

      - name: 生成前端覆盖率摘要
        run: |
          cd frontend
          if [ -f "coverage/coverage-summary.json" ]; then
            echo "## 📊 前端测试覆盖率" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "| 指标 | 覆盖率 |" >> $GITHUB_STEP_SUMMARY
            echo "|------|--------|" >> $GITHUB_STEP_SUMMARY

            # 解析覆盖率数据
            LINES=$(cat coverage/coverage-summary.json | jq -r '.total.lines.pct')
            FUNCTIONS=$(cat coverage/coverage-summary.json | jq -r '.total.functions.pct')
            BRANCHES=$(cat coverage/coverage-summary.json | jq -r '.total.branches.pct')
            STATEMENTS=$(cat coverage/coverage-summary.json | jq -r '.total.statements.pct')

            echo "| 行覆盖率 | ${LINES}% |" >> $GITHUB_STEP_SUMMARY
            echo "| 函数覆盖率 | ${FUNCTIONS}% |" >> $GITHUB_STEP_SUMMARY
            echo "| 分支覆盖率 | ${BRANCHES}% |" >> $GITHUB_STEP_SUMMARY
            echo "| 语句覆盖率 | ${STATEMENTS}% |" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi

  # ===== 后端覆盖率收集 =====
  backend-coverage:
    name: 后端覆盖率收集
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_chaiguanjia
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
          cache-dependency-path: backend/requirements-dev.txt

      - name: 安装后端依赖
        run: |
          cd backend
          pip install -r requirements-dev.txt

      - name: 运行后端测试并收集覆盖率
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_chaiguanjia
          REDIS_URL: redis://localhost:6379/0
          TESTING: true
        run: |
          cd backend
          pytest --cov=app --cov-report=xml --cov-report=html --cov-report=term-missing

      - name: 上传后端覆盖率到 Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage.xml
          flags: backend
          name: backend-coverage
          fail_ci_if_error: false

      - name: 上传后端覆盖率报告
        uses: actions/upload-artifact@v3
        with:
          name: backend-coverage-report
          path: |
            backend/htmlcov/
            backend/coverage.xml
          retention-days: 30

      - name: 生成后端覆盖率摘要
        run: |
          cd backend
          if [ -f "coverage.xml" ]; then
            echo "## 📊 后端测试覆盖率" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            # 从 coverage.xml 解析覆盖率
            COVERAGE=$(python -c "
import xml.etree.ElementTree as ET
tree = ET.parse('coverage.xml')
root = tree.getroot()
coverage = root.attrib.get('line-rate', '0')
print(f'{float(coverage) * 100:.1f}')
")

            echo "| 指标 | 覆盖率 |" >> $GITHUB_STEP_SUMMARY
            echo "|------|--------|" >> $GITHUB_STEP_SUMMARY
            echo "| 行覆盖率 | ${COVERAGE}% |" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi

  # ===== 覆盖率报告汇总 =====
  coverage-summary:
    name: 覆盖率报告汇总
    runs-on: ubuntu-latest
    needs: [frontend-coverage, backend-coverage]
    if: always()

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 下载前端覆盖率报告
        uses: actions/download-artifact@v3
        with:
          name: frontend-coverage-report
          path: ./coverage-reports/frontend/
        continue-on-error: true

      - name: 下载后端覆盖率报告
        uses: actions/download-artifact@v3
        with:
          name: backend-coverage-report
          path: ./coverage-reports/backend/
        continue-on-error: true

      - name: 生成覆盖率徽章
        run: |
          # 创建覆盖率徽章数据
          mkdir -p badges

          # 前端覆盖率徽章
          if [ -f "coverage-reports/frontend/coverage-summary.json" ]; then
            FRONTEND_COVERAGE=$(cat coverage-reports/frontend/coverage-summary.json | jq -r '.total.lines.pct')
            echo "前端覆盖率: ${FRONTEND_COVERAGE}%"

            # 根据覆盖率设置颜色
            if (( $(echo "$FRONTEND_COVERAGE >= 80" | bc -l) )); then
              COLOR="brightgreen"
            elif (( $(echo "$FRONTEND_COVERAGE >= 60" | bc -l) )); then
              COLOR="yellow"
            else
              COLOR="red"
            fi

            echo "{\"schemaVersion\": 1, \"label\": \"frontend coverage\", \"message\": \"${FRONTEND_COVERAGE}%\", \"color\": \"${COLOR}\"}" > badges/frontend-coverage.json
          fi

          # 后端覆盖率徽章
          if [ -f "coverage-reports/backend/coverage.xml" ]; then
            BACKEND_COVERAGE=$(python3 -c "
import xml.etree.ElementTree as ET
tree = ET.parse('coverage-reports/backend/coverage.xml')
root = tree.getroot()
coverage = root.attrib.get('line-rate', '0')
print(f'{float(coverage) * 100:.1f}')
" 2>/dev/null || echo "0")
            echo "后端覆盖率: ${BACKEND_COVERAGE}%"

            # 根据覆盖率设置颜色
            if (( $(echo "$BACKEND_COVERAGE >= 80" | bc -l) )); then
              COLOR="brightgreen"
            elif (( $(echo "$BACKEND_COVERAGE >= 60" | bc -l) )); then
              COLOR="yellow"
            else
              COLOR="red"
            fi

            echo "{\"schemaVersion\": 1, \"label\": \"backend coverage\", \"message\": \"${BACKEND_COVERAGE}%\", \"color\": \"${COLOR}\"}" > badges/backend-coverage.json
          fi

      - name: 上传覆盖率徽章
        uses: actions/upload-artifact@v3
        with:
          name: coverage-badges
          path: badges/
          retention-days: 30

      - name: 创建覆盖率报告评论 (PR)
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');

            let comment = '## 📊 代码覆盖率报告\n\n';

            // 前端覆盖率
            try {
              const frontendSummary = JSON.parse(fs.readFileSync('./coverage-reports/frontend/coverage-summary.json', 'utf8'));
              const total = frontendSummary.total;

              comment += '### 前端覆盖率\n';
              comment += '| 指标 | 覆盖率 | 阈值 | 状态 |\n';
              comment += '|------|--------|------|------|\n';
              comment += `| 行覆盖率 | ${total.lines.pct}% | 80% | ${total.lines.pct >= 80 ? '✅' : '❌'} |\n`;
              comment += `| 函数覆盖率 | ${total.functions.pct}% | 80% | ${total.functions.pct >= 80 ? '✅' : '❌'} |\n`;
              comment += `| 分支覆盖率 | ${total.branches.pct}% | 80% | ${total.branches.pct >= 80 ? '✅' : '❌'} |\n`;
              comment += `| 语句覆盖率 | ${total.statements.pct}% | 80% | ${total.statements.pct >= 80 ? '✅' : '❌'} |\n\n`;
            } catch (e) {
              comment += '### 前端覆盖率\n❌ 无法获取前端覆盖率数据\n\n';
            }

            // 后端覆盖率
            comment += '### 后端覆盖率\n';
            comment += '详细报告请查看 Artifacts 中的覆盖率报告\n\n';

            comment += '### 📈 覆盖率趋势\n';
            comment += '- 🎯 目标覆盖率: 80%\n';
            comment += '- 📊 详细报告: 查看 [Codecov](https://codecov.io)\n';
            comment += '- 📁 HTML 报告: 下载 Artifacts 查看\n';

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

      - name: 检查覆盖率阈值
        run: |
          echo "## 📊 覆盖率阈值检查" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          FAILED=false

          # 检查前端覆盖率
          if [ -f "coverage-reports/frontend/coverage-summary.json" ]; then
            FRONTEND_LINES=$(cat coverage-reports/frontend/coverage-summary.json | jq -r '.total.lines.pct')
            if (( $(echo "$FRONTEND_LINES < 80" | bc -l) )); then
              echo "❌ 前端行覆盖率 ${FRONTEND_LINES}% 低于阈值 80%" >> $GITHUB_STEP_SUMMARY
              FAILED=true
            else
              echo "✅ 前端行覆盖率 ${FRONTEND_LINES}% 达到阈值" >> $GITHUB_STEP_SUMMARY
            fi
          fi

          # 检查后端覆盖率
          if [ -f "coverage-reports/backend/coverage.xml" ]; then
            BACKEND_COVERAGE=$(python3 -c "
import xml.etree.ElementTree as ET
tree = ET.parse('coverage-reports/backend/coverage.xml')
root = tree.getroot()
coverage = root.attrib.get('line-rate', '0')
print(f'{float(coverage) * 100:.1f}')
" 2>/dev/null || echo "0")

            if (( $(echo "$BACKEND_COVERAGE < 80" | bc -l) )); then
              echo "❌ 后端覆盖率 ${BACKEND_COVERAGE}% 低于阈值 80%" >> $GITHUB_STEP_SUMMARY
              FAILED=true
            else
              echo "✅ 后端覆盖率 ${BACKEND_COVERAGE}% 达到阈值" >> $GITHUB_STEP_SUMMARY
            fi
          fi

          if [ "$FAILED" = true ]; then
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "⚠️ 部分覆盖率未达到阈值，请增加测试用例" >> $GITHUB_STEP_SUMMARY
            exit 1
          else
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "🎉 所有覆盖率都达到了阈值！" >> $GITHUB_STEP_SUMMARY
          fi
