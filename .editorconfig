# 柴管家项目编辑器配置
# EditorConfig 帮助开发者在不同的编辑器和IDE之间保持一致的编码风格
# https://editorconfig.org

# 根配置文件
root = true

# 所有文件的默认配置
[*]
# 字符集
charset = utf-8
# 换行符类型（LF for Unix/Linux/macOS）
end_of_line = lf
# 文件末尾插入新行
insert_final_newline = true
# 移除行尾空格
trim_trailing_whitespace = true
# 缩进风格（空格）
indent_style = space
# 缩进大小
indent_size = 2

# ===== Python 文件配置 =====
[*.py]
indent_size = 4
max_line_length = 88

# ===== JavaScript/TypeScript 文件配置 =====
[*.{js,jsx,ts,tsx}]
indent_size = 2
max_line_length = 80

# ===== JSON 文件配置 =====
[*.json]
indent_size = 2
max_line_length = 120

# ===== YAML 文件配置 =====
[*.{yml,yaml}]
indent_size = 2
max_line_length = 120

# ===== TOML 文件配置 =====
[*.toml]
indent_size = 2

# ===== XML 文件配置 =====
[*.xml]
indent_size = 2

# ===== HTML 文件配置 =====
[*.html]
indent_size = 2
max_line_length = 120

# ===== CSS/SCSS 文件配置 =====
[*.{css,scss,sass,less}]
indent_size = 2
max_line_length = 120

# ===== Markdown 文件配置 =====
[*.md]
# Markdown 文件不移除行尾空格（可能有语义）
trim_trailing_whitespace = false
max_line_length = 100

# ===== 配置文件 =====
[*.{ini,cfg}]
indent_size = 4

# ===== Shell 脚本 =====
[*.{sh,bash,zsh}]
indent_size = 2
end_of_line = lf

# ===== Dockerfile =====
[Dockerfile*]
indent_size = 2

# ===== Docker Compose =====
[docker-compose*.{yml,yaml}]
indent_size = 2

# ===== Makefile =====
[{Makefile,makefile,*.mk}]
# Makefile 必须使用制表符
indent_style = tab
indent_size = 4

# ===== 特殊文件 =====
# 包管理器锁文件不修改
[{package-lock.json,yarn.lock,pnpm-lock.yaml,Pipfile.lock}]
insert_final_newline = false
trim_trailing_whitespace = false

# Git 配置文件
[.gitconfig]
indent_style = tab

# 环境变量文件
[.env*]
trim_trailing_whitespace = false
