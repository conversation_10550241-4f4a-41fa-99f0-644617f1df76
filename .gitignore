# 柴管家项目 .gitignore
# 根据项目初始化方案要求，排除所有环境相关文件

# ===== 操作系统相关 =====
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ===== Python 后端相关 =====
# 字节码文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# ===== Node.js 前端相关 =====
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov

# nyc 测试覆盖率
.nyc_output

# Grunt 中间存储
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# TypeScript 缓存
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# Microbundle 缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的 REPL 历史
.node_repl_history

# 输出的 npm 包
*.tgz

# Yarn 完整性文件
.yarn-integrity

# dotenv 环境变量文件
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler 缓存
.cache
.parcel-cache

# Next.js 构建输出
.next

# Nuxt.js 构建/生成输出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Vuepress 构建输出
.vuepress/dist

# Serverless 目录
.serverless/

# FuseBox 缓存
.fusebox/

# DynamoDB Local 文件
.dynamodb/

# TernJS 端口文件
.tern-port

# ===== 数据库相关 =====
# SQLite
*.db
*.sqlite
*.sqlite3

# PostgreSQL
*.sql

# MySQL
*.sql

# ===== 容器化相关 =====
# Docker
.dockerignore

# ===== IDE 和编辑器相关 =====
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 日志文件 =====
logs
*.log

# ===== 临时文件 =====
*.tmp
*.temp
*.bak
*.backup

# ===== 配置文件（包含敏感信息） =====
# 环境配置
.env*
!.env.example
config/local.py
config/production.py
config/development.py

# 密钥文件
*.key
*.pem
*.p12
*.pfx

# 证书文件
*.crt
*.cer

# ===== 构建输出 =====
# 前端构建输出
build/
dist/

# 后端构建输出
*.pyc
*.pyo

# ===== 测试相关 =====
# 测试输出
test-results/
coverage/

# ===== 其他 =====
# 压缩文件
*.zip
*.tar.gz
*.rar

# 备份文件
*.bak
*.backup

# 系统文件
.DS_Store
Thumbs.db
