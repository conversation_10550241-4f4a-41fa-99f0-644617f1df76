# 柴管家项目 Pre-commit 配置
# 在提交前自动执行代码质量检查

repos:
  # ===== 通用检查 =====
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # 基本文件检查
      - id: trailing-whitespace
        name: 移除行尾空格
      - id: end-of-file-fixer
        name: 确保文件以换行符结尾
      - id: check-yaml
        name: 检查 YAML 文件语法
      - id: check-json
        name: 检查 JSON 文件语法
      - id: check-toml
        name: 检查 TOML 文件语法
      - id: check-xml
        name: 检查 XML 文件语法
      
      # 合并冲突检查
      - id: check-merge-conflict
        name: 检查合并冲突标记
      
      # 大文件检查
      - id: check-added-large-files
        name: 检查大文件
        args: ['--maxkb=1000']
      
      # 私钥检查
      - id: detect-private-key
        name: 检测私钥文件
      
      # 可执行文件检查
      - id: check-executables-have-shebangs
        name: 检查可执行文件有 shebang
      
      # 文件名检查
      - id: check-case-conflict
        name: 检查文件名大小写冲突

  # ===== Python 后端检查 =====
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        name: Black 代码格式化
        files: ^backend/
        language_version: python3.11

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort 导入排序
        files: ^backend/
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        name: Flake8 语法检查
        files: ^backend/
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        name: MyPy 类型检查
        files: ^backend/
        additional_dependencies:
          - types-requests
          - types-redis
          - types-python-dateutil
        args: [--config-file=backend/pyproject.toml]

  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        name: Bandit 安全检查
        files: ^backend/
        args: ["-c", "backend/pyproject.toml"]

  # ===== 前端检查 =====
  - repo: local
    hooks:
      # ESLint 检查
      - id: eslint
        name: ESLint 代码检查
        entry: bash -c 'cd frontend && npm run lint'
        language: system
        files: ^frontend/src/.*\.(ts|tsx|js|jsx)$
        pass_filenames: false
      
      # Prettier 格式化检查
      - id: prettier
        name: Prettier 格式化检查
        entry: bash -c 'cd frontend && npm run format:check'
        language: system
        files: ^frontend/src/.*\.(ts|tsx|js|jsx|json|css|scss|md)$
        pass_filenames: false
      
      # TypeScript 类型检查
      - id: typescript
        name: TypeScript 类型检查
        entry: bash -c 'cd frontend && npm run type-check'
        language: system
        files: ^frontend/src/.*\.(ts|tsx)$
        pass_filenames: false

  # ===== 提交信息检查 =====
  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v3.0.0
    hooks:
      - id: conventional-pre-commit
        name: 检查提交信息格式
        stages: [commit-msg]
        args: [--strict]

  # ===== 文档检查 =====
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.37.0
    hooks:
      - id: markdownlint
        name: Markdown 格式检查
        args: ['--fix']
        exclude: ^(CHANGELOG\.md|node_modules/)

# ===== 全局配置 =====
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: '3.0.0'

# ===== CI 配置 =====
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks
    
    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
