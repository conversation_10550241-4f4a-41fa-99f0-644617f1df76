# 柴管家数据架构设计文档

## 📋 项目概述

### 项目背景
柴管家是一个基于AI的多平台聚合智能客服系统，旨在为个人IP运营者提供一站式私域运营解决方案。系统通过聚合多平台消息和AI能力，解决多平台消息分散、重复性咨询繁重、用户关系维护困难等核心痛点。

### 业务范围
数据设计覆盖以下核心业务领域：
- **用户管理**：用户注册、登录、权限管理
- **渠道管理**：多平台账号接入、连接状态监控
- **消息处理**：消息聚合、路由、存储和同步
- **AI智能服务**：意图识别、自动回复、置信度评估
- **知识管理**：知识库构建、FAQ管理、智能匹配
- **工作流引擎**：自动化任务编排、规则引擎

### 技术环境
- **数据库**：PostgreSQL 15+ (主数据库)
- **缓存**：Redis 7+ (缓存和会话存储)
- **消息队列**：RabbitMQ 3.12+ (异步消息处理)
- **ORM框架**：SQLAlchemy 2.0+ (Python ORM)
- **部署方式**：Docker容器化部署

### 设计原则
- **模块化隔离**：每个业务模块拥有独立的数据Schema
- **数据安全**：敏感数据加密存储，严格访问控制
- **性能优化**：合理的索引设计和缓存策略
- **扩展性**：支持业务增长和功能扩展的数据架构

## 🎯 业务需求分析

### 核心业务流程

#### 1. 用户管理流程
```mermaid
flowchart TD
    A[用户注册] --> B[手机验证]
    B --> C[密码设置]
    C --> D[账户激活]
    D --> E[用户登录]
    E --> F[会话管理]
    F --> G[权限验证]
```

#### 2. 渠道接入流程
```mermaid
flowchart TD
    A[选择平台] --> B[OAuth授权]
    B --> C[获取Token]
    C --> D[连接验证]
    D --> E[设置别名]
    E --> F[状态监控]
```

#### 3. 消息处理流程
```mermaid
flowchart TD
    A[外部消息] --> B[消息接收]
    B --> C[消息聚合]
    C --> D[AI分析]
    D --> E{置信度判断}
    E -->|高置信度| F[自动回复]
    E -->|低置信度| G[人工接管]
    F --> H[消息发送]
    G --> H
```

### 数据需求概述

#### 数据实体
- **用户实体**：用户基本信息、认证信息、状态管理
- **渠道实体**：平台账号、连接配置、状态监控
- **消息实体**：消息内容、元数据、处理状态
- **对话实体**：会话管理、上下文维护、模式控制
- **知识实体**：FAQ管理、知识匹配、学习优化
- **工作流实体**：规则定义、任务执行、调度管理

#### 数据量级
- **用户数据**：预估1万+用户，增长率20%/月
- **消息数据**：预估100万+消息/月，峰值1000条/分钟
- **知识库数据**：预估1万+FAQ条目，持续增长
- **日志数据**：预估10GB+/月，需要归档策略

#### 访问模式
- **读写比例**：消息处理80%读，20%写；知识库90%读，10%写
- **查询模式**：实时消息查询、历史记录分页、知识库搜索
- **并发需求**：支持100+并发用户，1000+并发消息处理

#### 性能要求
- **响应时间**：API响应<200ms，消息处理<1s，AI回复<3s
- **并发量**：支持1000+并发连接，100+并发AI处理
- **可用性**：99.5%系统可用性，数据零丢失
- **扩展性**：支持10倍数据量增长，水平扩展能力

## 🏗️ 概念数据模型

### ER图

```mermaid
erDiagram
    %% 用户管理域
    User ||--o{ UserSession : has
    User ||--o{ UserRole : has
    Role ||--o{ UserRole : belongs_to
    Role ||--o{ RolePermission : has
    Permission ||--o{ RolePermission : belongs_to
    
    %% 渠道管理域
    User ||--o{ Channel : owns
    Channel ||--o{ ChannelConnection : has
    Channel ||--o{ Message : receives
    
    %% 消息处理域
    Channel ||--o{ Conversation : contains
    Conversation ||--o{ Message : includes
    Message ||--o{ MessageAttachment : has
    
    %% AI服务域
    Message ||--o{ AIAnalysis : analyzed_by
    AIAnalysis ||--o{ AIResponse : generates
    AIResponse ||--o{ ConfidenceScore : has
    
    %% 知识管理域
    User ||--o{ KnowledgeBase : manages
    KnowledgeBase ||--o{ KnowledgeItem : contains
    KnowledgeItem ||--o{ KnowledgeTag : tagged_with
    AIAnalysis ||--o{ KnowledgeMatch : matches
    
    %% 工作流域
    User ||--o{ WorkflowTemplate : creates
    WorkflowTemplate ||--o{ WorkflowRule : contains
    Conversation ||--o{ WorkflowExecution : triggers
    WorkflowExecution ||--o{ WorkflowTask : includes
    
    %% 实体定义
    User {
        uuid id PK
        string username UK
        string email UK
        string phone UK
        string password_hash
        string full_name
        string avatar_url
        text bio
        boolean is_active
        boolean is_verified
        boolean is_superuser
        timestamp last_login_at
        timestamp email_verified_at
        timestamp phone_verified_at
        timestamp created_at
        timestamp updated_at
        boolean is_deleted
    }
    
    Channel {
        uuid id PK
        uuid user_id FK
        string platform_type
        string platform_account_id
        string account_alias
        string access_token
        string refresh_token
        json connection_config
        string connection_status
        timestamp last_connected_at
        timestamp token_expires_at
        timestamp created_at
        timestamp updated_at
        boolean is_deleted
    }
    
    Conversation {
        uuid id PK
        uuid channel_id FK
        string conversation_type
        string external_conversation_id
        string participant_name
        string participant_avatar
        json participant_info
        string conversation_mode
        string conversation_status
        timestamp last_message_at
        timestamp created_at
        timestamp updated_at
        boolean is_deleted
    }
    
    Message {
        uuid id PK
        uuid conversation_id FK
        uuid channel_id FK
        string message_type
        string direction
        text content
        json metadata
        string processing_status
        timestamp sent_at
        timestamp received_at
        timestamp processed_at
        timestamp created_at
        timestamp updated_at
        boolean is_deleted
    }
    
    KnowledgeItem {
        uuid id PK
        uuid knowledge_base_id FK
        string item_type
        string question
        text answer
        json metadata
        integer usage_count
        float effectiveness_score
        boolean is_active
        timestamp created_at
        timestamp updated_at
        boolean is_deleted
    }
```

### 实体定义

#### 用户实体 (User)
- **业务含义**：系统的IP运营者用户，是所有业务活动的主体
- **主要属性**：用户名、邮箱、手机号、密码、个人信息、状态标识
- **业务规则**：
  - 用户名、邮箱、手机号必须唯一
  - 密码必须加密存储
  - 支持邮箱和手机号验证
  - 记录最后登录时间用于会话管理
- **数据来源**：用户注册、第三方OAuth、管理员创建

#### 渠道实体 (Channel)
- **业务含义**：用户接入的第三方平台账号，是消息来源的抽象
- **主要属性**：平台类型、账号ID、别名、访问令牌、连接配置
- **业务规则**：
  - 每个用户可以接入多个渠道
  - 同一平台可以接入多个不同账号
  - 访问令牌需要加密存储
  - 支持令牌自动刷新机制
- **数据来源**：OAuth授权流程、手动配置

#### 对话实体 (Conversation)
- **业务含义**：与特定用户或群组的对话会话，是消息的容器
- **主要属性**：对话类型、外部ID、参与者信息、模式状态
- **业务规则**：
  - 每个对话属于特定渠道
  - 支持人工模式和AI托管模式切换
  - 记录最后消息时间用于活跃度管理
  - 参与者信息支持动态更新
- **数据来源**：外部平台消息、系统自动创建

#### 消息实体 (Message)
- **业务含义**：具体的消息内容，是业务处理的核心数据
- **主要属性**：消息类型、方向、内容、元数据、处理状态
- **业务规则**：
  - 消息分为接收和发送两个方向
  - 支持文本、图片、文件等多种类型
  - 记录完整的处理时间链路
  - 元数据存储平台特定信息
- **数据来源**：外部平台推送、用户发送

#### 知识条目实体 (KnowledgeItem)
- **业务含义**：FAQ知识库的基本单元，AI回复的知识来源
- **主要属性**：问题、答案、类型、使用统计、有效性评分
- **业务规则**：
  - 支持多种知识类型（FAQ、文档、链接等）
  - 记录使用次数和有效性评分
  - 支持动态学习和优化
  - 可以设置激活状态控制使用
- **数据来源**：用户手动录入、AI自动挖掘、系统学习

### 关系定义

#### 用户-渠道关系 (User-Channel)
- **参与实体**：用户(User) - 渠道(Channel)
- **关系类型**：一对多
- **业务含义**：一个用户可以管理多个渠道账号
- **约束条件**：渠道必须属于特定用户，不能共享

#### 渠道-对话关系 (Channel-Conversation)
- **参与实体**：渠道(Channel) - 对话(Conversation)
- **关系类型**：一对多
- **业务含义**：一个渠道可以包含多个对话会话
- **约束条件**：对话必须属于特定渠道，外部ID在渠道内唯一

#### 对话-消息关系 (Conversation-Message)
- **参与实体**：对话(Conversation) - 消息(Message)
- **关系类型**：一对多
- **业务含义**：一个对话包含多条消息记录
- **约束条件**：消息必须属于特定对话，按时间顺序排列

#### 消息-AI分析关系 (Message-AIAnalysis)
- **参与实体**：消息(Message) - AI分析(AIAnalysis)
- **关系类型**：一对一
- **业务含义**：每条消息可以有一次AI分析结果
- **约束条件**：AI分析结果与消息一一对应，包含置信度评分

#### 知识库-知识条目关系 (KnowledgeBase-KnowledgeItem)
- **参与实体**：知识库(KnowledgeBase) - 知识条目(KnowledgeItem)
- **关系类型**：一对多
- **业务含义**：一个知识库包含多个知识条目
- **约束条件**：知识条目必须属于特定知识库，支持分类管理

## 📊 数据统计与监控

### 业务指标监控

#### 用户活跃度指标
- **日活跃用户数（DAU）**：每日登录用户数量
- **月活跃用户数（MAU）**：每月登录用户数量
- **用户留存率**：新用户在7天、30天的留存情况
- **会话时长**：用户平均在线时长统计

#### 消息处理指标
- **消息处理量**：每日/每小时消息处理数量
- **消息响应时间**：从接收到处理完成的平均时间
- **AI回复准确率**：AI自动回复的准确性评估
- **人工接管率**：需要人工介入的消息比例

#### 渠道连接指标
- **渠道连接成功率**：各平台连接成功的比例
- **令牌刷新成功率**：访问令牌自动刷新的成功率
- **连接稳定性**：渠道连接的持续时间统计
- **错误率分析**：各类连接错误的分布情况

#### 知识库效果指标
- **知识匹配率**：问题与知识库匹配的成功率
- **知识使用频率**：各知识条目的使用统计
- **知识有效性**：用户对AI回复的满意度评分
- **知识库覆盖率**：知识库对用户问题的覆盖程度

### 性能监控指标

#### 数据库性能
- **查询响应时间**：各类查询的平均响应时间
- **慢查询监控**：执行时间超过阈值的查询统计
- **连接池使用率**：数据库连接池的使用情况
- **锁等待时间**：数据库锁竞争的等待时间

#### 系统资源监控
- **CPU使用率**：服务器CPU使用情况
- **内存使用率**：应用内存占用统计
- **磁盘I/O**：数据库读写操作的I/O性能
- **网络带宽**：API请求的网络流量统计

#### 缓存性能
- **缓存命中率**：Redis缓存的命中率统计
- **缓存响应时间**：缓存查询的平均响应时间
- **缓存内存使用**：Redis内存使用情况
- **缓存失效率**：缓存数据失效的频率

### 监控实现方案

#### 数据采集
```sql
-- 创建监控指标表
CREATE TABLE monitoring_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_type VARCHAR(20) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    dimensions JSONB,
    collected_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建分区索引
CREATE INDEX idx_monitoring_metrics_name_time 
ON monitoring_metrics (metric_name, collected_at DESC);

CREATE INDEX idx_monitoring_metrics_type_time 
ON monitoring_metrics (metric_type, collected_at DESC);
```

#### 实时监控视图
```sql
-- 用户活跃度监控视图
CREATE VIEW v_user_activity_metrics AS
SELECT 
    DATE(created_at) as date,
    COUNT(DISTINCT user_id) as daily_active_users,
    COUNT(*) as total_sessions,
    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))/60) as avg_session_minutes
FROM user_sessions 
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 消息处理监控视图
CREATE VIEW v_message_processing_metrics AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_messages,
    COUNT(CASE WHEN direction = 'inbound' THEN 1 END) as inbound_messages,
    COUNT(CASE WHEN direction = 'outbound' THEN 1 END) as outbound_messages,
    AVG(EXTRACT(EPOCH FROM (processed_at - received_at))) as avg_processing_seconds
FROM messages 
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
AND is_deleted = false
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- AI性能监控视图
CREATE VIEW v_ai_performance_metrics AS
SELECT 
    DATE(a.created_at) as date,
    COUNT(*) as total_analyses,
    AVG(a.intent_confidence) as avg_confidence,
    COUNT(r.id) as total_responses,
    COUNT(CASE WHEN r.is_auto_sent = true THEN 1 END) as auto_sent_responses,
    AVG(r.confidence_score) as avg_response_confidence,
    AVG(a.processing_time_ms) as avg_analysis_time_ms,
    AVG(r.processing_time_ms) as avg_response_time_ms
FROM ai_analyses a
LEFT JOIN ai_responses r ON a.id = r.ai_analysis_id
WHERE a.created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(a.created_at)
ORDER BY date DESC;
```

#### 告警规则配置
```sql
-- 创建告警规则表
CREATE TABLE alert_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_name VARCHAR(100) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    condition_type VARCHAR(20) NOT NULL, -- gt, lt, eq, ne
    threshold_value DECIMAL(15,4) NOT NULL,
    severity VARCHAR(10) NOT NULL, -- low, medium, high, critical
    is_active BOOLEAN NOT NULL DEFAULT true,
    notification_channels JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认告警规则
INSERT INTO alert_rules (rule_name, metric_name, condition_type, threshold_value, severity, notification_channels) VALUES
('数据库响应时间过长', 'db_query_response_time', 'gt', 1000, 'high', '["email", "slack"]'),
('AI回复置信度过低', 'ai_response_confidence', 'lt', 0.6, 'medium', '["email"]'),
('消息处理失败率过高', 'message_processing_error_rate', 'gt', 0.05, 'high', '["email", "slack"]'),
('渠道连接失败率过高', 'channel_connection_error_rate', 'gt', 0.1, 'medium', '["email"]');
```

## 🔄 数据迁移与版本管理

### 数据库版本控制

#### 迁移脚本管理
```sql
-- 创建数据库版本管理表
CREATE TABLE schema_migrations (
    version VARCHAR(20) PRIMARY KEY,
    description TEXT,
    migration_type VARCHAR(20) NOT NULL, -- up, down
    executed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    execution_time_ms INTEGER,
    checksum VARCHAR(64)
);

-- 版本迁移示例
-- V1.0.0_001__create_users_table.sql
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- V1.0.1_002__add_user_phone_column.sql
ALTER TABLE users ADD COLUMN phone VARCHAR(20) UNIQUE;
CREATE INDEX idx_users_phone ON users(phone) WHERE phone IS NOT NULL;

-- V1.1.0_003__create_channels_table.sql
CREATE TABLE channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    platform_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 数据迁移策略
- **增量迁移**：只迁移变更的数据结构
- **向后兼容**：新版本兼容旧版本数据格式
- **回滚机制**：每个迁移都有对应的回滚脚本
- **数据验证**：迁移后进行数据完整性检查

### 数据备份与恢复

#### 备份策略
- **全量备份**：每周进行一次完整数据库备份
- **增量备份**：每日备份变更的数据
- **实时备份**：关键业务数据实时同步到备份库
- **异地备份**：备份数据存储在不同地理位置

#### 恢复方案
```bash
#!/bin/bash
# 数据库恢复脚本示例

# 1. 停止应用服务
sudo systemctl stop chaiguanjia-api

# 2. 创建当前数据库备份
pg_dump -h localhost -U postgres chaiguanjia > /backup/pre_restore_$(date +%Y%m%d_%H%M%S).sql

# 3. 恢复数据库
psql -h localhost -U postgres -d chaiguanjia < /backup/chaiguanjia_backup.sql

# 4. 验证数据完整性
psql -h localhost -U postgres -d chaiguanjia -c "SELECT COUNT(*) FROM users;"
psql -h localhost -U postgres -d chaiguanjia -c "SELECT COUNT(*) FROM messages;"

# 5. 重启应用服务
sudo systemctl start chaiguanjia-api

# 6. 健康检查
curl -f http://localhost:8000/health || exit 1

echo "数据库恢复完成"
```

### 数据清理与归档

#### 自动清理策略
```sql
-- 创建数据清理任务
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- 清理6个月前的已删除用户会话
    DELETE FROM user_sessions 
    WHERE is_deleted = true 
    AND updated_at < CURRENT_DATE - INTERVAL '6 months';
    
    -- 清理1年前的消息附件
    DELETE FROM message_attachments 
    WHERE created_at < CURRENT_DATE - INTERVAL '1 year'
    AND message_id IN (
        SELECT id FROM messages 
        WHERE created_at < CURRENT_DATE - INTERVAL '1 year'
    );
    
    -- 清理3个月前的AI分析结果（保留统计数据）
    DELETE FROM ai_analyses 
    WHERE created_at < CURRENT_DATE - INTERVAL '3 months'
    AND intent_confidence < 0.5;
    
    -- 清理过期的监控指标数据
    DELETE FROM monitoring_metrics 
    WHERE collected_at < CURRENT_DATE - INTERVAL '90 days';
    
    RAISE NOTICE '数据清理完成';
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务（需要pg_cron扩展）
SELECT cron.schedule('cleanup-old-data', '0 2 * * 0', 'SELECT cleanup_old_data();');
```

#### 数据归档流程
```sql
-- 创建归档表结构
CREATE SCHEMA IF NOT EXISTS archive;

-- 归档消息数据
CREATE TABLE archive.messages (
    LIKE messages INCLUDING ALL
);

-- 数据归档函数
CREATE OR REPLACE FUNCTION archive_old_messages(archive_date DATE)
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER;
BEGIN
    -- 将旧消息移动到归档表
    WITH moved_messages AS (
        DELETE FROM messages 
        WHERE created_at < archive_date
        AND is_deleted = false
        RETURNING *
    )
    INSERT INTO archive.messages 
    SELECT * FROM moved_messages;
    
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    
    RAISE NOTICE '归档了 % 条消息记录', archived_count;
    RETURN archived_count;
END;
$$ LANGUAGE plpgsql;
```

## 📈 扩展性设计

### 水平扩展方案

#### 数据库分片策略
- **按用户分片**：根据user_id进行数据分片
- **按时间分片**：历史数据按时间范围分片
- **按业务分片**：不同业务模块使用独立数据库
- **读写分离**：读操作分散到多个从库

#### 分片实现
```sql
-- 分片配置表
CREATE TABLE shard_config (
    id SERIAL PRIMARY KEY,
    shard_name VARCHAR(50) NOT NULL,
    shard_type VARCHAR(20) NOT NULL, -- user, time, business
    shard_key VARCHAR(50) NOT NULL,
    shard_range JSONB NOT NULL,
    database_url VARCHAR(500) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 插入分片配置
INSERT INTO shard_config (shard_name, shard_type, shard_key, shard_range, database_url) VALUES
('shard_user_0', 'user', 'user_id', '{"start": 0, "end": 999999}', '*******************************/chaiguanjia_shard_0'),
('shard_user_1', 'user', 'user_id', '{"start": 1000000, "end": 1999999}', '*******************************/chaiguanjia_shard_1'),
('shard_time_2024', 'time', 'created_at', '{"start": "2024-01-01", "end": "2024-12-31"}', '*******************************/chaiguanjia_2024'),
('shard_time_2025', 'time', 'created_at', '{"start": "2025-01-01", "end": "2025-12-31"}', '*******************************/chaiguanjia_2025');
```

### 垂直扩展方案

#### 微服务数据隔离
- **用户服务**：用户管理相关表
- **消息服务**：消息处理相关表
- **AI服务**：AI分析和回复相关表
- **知识库服务**：知识管理相关表
- **工作流服务**：工作流引擎相关表

#### 服务间数据同步
```sql
-- 创建数据同步日志表
CREATE TABLE data_sync_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_service VARCHAR(50) NOT NULL,
    target_service VARCHAR(50) NOT NULL,
    sync_type VARCHAR(20) NOT NULL, -- create, update, delete
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    sync_data JSONB,
    sync_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    error_message TEXT,
    retry_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- 数据同步触发器
CREATE OR REPLACE FUNCTION sync_user_data()
RETURNS TRIGGER AS $$
BEGIN
    -- 用户数据变更时同步到其他服务
    INSERT INTO data_sync_log (source_service, target_service, sync_type, table_name, record_id, sync_data)
    VALUES 
    ('user-service', 'message-service', TG_OP::text, TG_TABLE_NAME, NEW.id, row_to_json(NEW)),
    ('user-service', 'ai-service', TG_OP::text, TG_TABLE_NAME, NEW.id, row_to_json(NEW));
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_sync_user_data
    AFTER INSERT OR UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION sync_user_data();
```

### 性能优化扩展

#### 缓存层扩展
```python
# Redis集群配置示例
REDIS_CLUSTER_CONFIG = {
    'nodes': [
        {'host': 'redis-node-1', 'port': 6379},
        {'host': 'redis-node-2', 'port': 6379},
        {'host': 'redis-node-3', 'port': 6379},
        {'host': 'redis-node-4', 'port': 6379},
        {'host': 'redis-node-5', 'port': 6379},
        {'host': 'redis-node-6', 'port': 6379},
    ],
    'decode_responses': True,
    'skip_full_coverage_check': True,
    'max_connections_per_node': 50
}

# 缓存分层策略
CACHE_LAYERS = {
    'L1': {  # 应用内存缓存
        'type': 'memory',
        'size': '256MB',
        'ttl': 300  # 5分钟
    },
    'L2': {  # Redis缓存
        'type': 'redis',
        'cluster': True,
        'ttl': 3600  # 1小时
    },
    'L3': {  # 数据库查询结果缓存
        'type': 'database',
        'ttl': 1800  # 30分钟
    }
}
```

#### 搜索引擎集成
```sql
-- 全文搜索优化
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- 创建搜索索引
CREATE INDEX idx_knowledge_items_search 
ON knowledge_items USING gin(
    (question || ' ' || answer) gin_trgm_ops
);

-- 创建搜索视图
CREATE MATERIALIZED VIEW mv_knowledge_search AS
SELECT 
    ki.id,
    ki.knowledge_base_id,
    ki.question,
    ki.answer,
    ki.keywords,
    ki.usage_count,
    ki.effectiveness_score,
    kb.name as knowledge_base_name,
    kb.user_id,
    setweight(to_tsvector('chinese', ki.question), 'A') ||
    setweight(to_tsvector('chinese', ki.answer), 'B') ||
    setweight(to_tsvector('chinese', coalesce(ki.keywords::text, '')), 'C') as search_vector
FROM knowledge_items ki
JOIN knowledge_bases kb ON ki.knowledge_base_id = kb.id
WHERE ki.is_active = true AND ki.is_deleted = false;

-- 创建搜索索引
CREATE INDEX idx_mv_knowledge_search_vector 
ON mv_knowledge_search USING gin(search_vector);

-- 定期刷新搜索视图
CREATE OR REPLACE FUNCTION refresh_knowledge_search()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_knowledge_search;
END;
$$ LANGUAGE plpgsql;

-- 每小时刷新一次
SELECT cron.schedule('refresh-knowledge-search', '0 * * * *', 'SELECT refresh_knowledge_search();');
```

## 🔚 总结

### 设计亮点

#### 技术架构优势
1. **模块化设计**：清晰的业务域划分，便于维护和扩展
2. **数据安全**：多层次的安全防护，保障用户数据安全
3. **性能优化**：合理的索引设计和缓存策略，支持高并发访问
4. **扩展性强**：支持水平和垂直扩展，适应业务增长需求
5. **监控完善**：全面的监控指标和告警机制，保障系统稳定性

#### 业务价值体现
1. **用户体验**：快速响应和智能回复，提升用户满意度
2. **运营效率**：自动化处理减少人工成本，提高工作效率
3. **数据驱动**：丰富的数据分析支持业务决策优化
4. **平台整合**：统一管理多平台账号，简化运营流程
5. **智能化**：AI能力持续学习优化，提供更好的服务质量

### 实施建议

#### 分阶段实施
1. **第一阶段**：核心功能实现（用户管理、渠道接入、消息处理）
2. **第二阶段**：AI能力集成（意图识别、自动回复、知识匹配）
3. **第三阶段**：高级功能开发（工作流引擎、数据分析、性能优化）
4. **第四阶段**：扩展性增强（分布式部署、微服务拆分、智能化升级）

#### 风险控制
1. **数据备份**：建立完善的备份和恢复机制
2. **性能监控**：实时监控系统性能和业务指标
3. **安全防护**：定期安全审计和漏洞扫描
4. **容量规划**：根据业务增长预测进行容量规划
5. **灾难恢复**：制定详细的灾难恢复预案

#### 持续优化
1. **性能调优**：根据实际使用情况持续优化数据库性能
2. **功能迭代**：根据用户反馈不断完善产品功能
3. **技术升级**：跟进新技术发展，适时进行技术栈升级
4. **数据治理**：建立数据质量管理和数据生命周期管理
5. **智能化提升**：持续优化AI算法，提高智能化水平

通过本数据架构设计，柴管家系统将具备强大的数据处理能力、优秀的性能表现和良好的扩展性，为用户提供高质量的智能客服服务，助力个人IP运营者实现私域运营的数字化转型。

## 🗄️ 逻辑数据模型

### 数据表设计

#### 表名：users (用户表)
**表说明**：存储系统用户的基本信息、认证信息和状态数据

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| username | VARCHAR | 50 | NOT NULL | - | 用户名 | UNIQUE, INDEX |
| email | VARCHAR | 255 | NOT NULL | - | 邮箱地址 | UNIQUE, INDEX |
| phone | VARCHAR | 20 | NULL | - | 手机号码 | UNIQUE, INDEX |
| password_hash | VARCHAR | 255 | NOT NULL | - | 密码哈希 | - |
| full_name | VARCHAR | 100 | NULL | - | 真实姓名 | - |
| avatar_url | VARCHAR | 500 | NULL | - | 头像URL | - |
| bio | TEXT | - | NULL | - | 个人简介 | - |
| is_active | BOOLEAN | - | NOT NULL | true | 是否激活 | - |
| is_verified | BOOLEAN | - | NOT NULL | false | 是否已验证 | - |
| is_superuser | BOOLEAN | - | NOT NULL | false | 是否超级用户 | - |
| last_login_at | TIMESTAMP | - | NULL | - | 最后登录时间 | - |
| email_verified_at | TIMESTAMP | - | NULL | - | 邮箱验证时间 | - |
| phone_verified_at | TIMESTAMP | - | NULL | - | 手机验证时间 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- UNIQUE INDEX: `uk_users_username` (`username`)
- UNIQUE INDEX: `uk_users_email` (`email`)
- UNIQUE INDEX: `uk_users_phone` (`phone`)
- INDEX: `idx_users_is_active` (`is_active`)
- INDEX: `idx_users_created_at` (`created_at`)

**业务规则**:
- 用户名长度3-50字符，支持字母、数字、下划线
- 邮箱格式必须符合RFC标准
- 手机号格式必须符合中国大陆11位数字标准
- 密码使用bcrypt加密，最小长度8位
- 软删除用户保留数据但标记is_deleted=true

#### 表名：user_sessions (用户会话表)
**表说明**：管理用户登录会话和状态

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| user_id | UUID | - | NOT NULL | - | 用户ID | FOREIGN KEY |
| session_token | VARCHAR | 255 | NOT NULL | - | 会话令牌 | UNIQUE |
| refresh_token | VARCHAR | 255 | NOT NULL | - | 刷新令牌 | UNIQUE |
| device_info | JSONB | - | NULL | - | 设备信息 | - |
| ip_address | INET | - | NULL | - | IP地址 | - |
| user_agent | TEXT | - | NULL | - | 用户代理 | - |
| is_active | BOOLEAN | - | NOT NULL | true | 是否活跃 | - |
| expires_at | TIMESTAMP | - | NOT NULL | - | 过期时间 | - |
| last_activity_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 最后活动时间 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_user_sessions_user_id` (`user_id` REFERENCES `users(id)`)
- UNIQUE INDEX: `uk_user_sessions_session_token` (`session_token`)
- UNIQUE INDEX: `uk_user_sessions_refresh_token` (`refresh_token`)
- INDEX: `idx_user_sessions_user_id` (`user_id`)
- INDEX: `idx_user_sessions_expires_at` (`expires_at`)
- INDEX: `idx_user_sessions_is_active` (`is_active`)

**业务规则**:
- 会话令牌有效期7天，可通过刷新令牌延期
- 每5分钟检查一次会话状态，超时自动失效
- 记录设备信息用于安全检测
- 支持多设备同时登录，每个设备独立会话

#### 表名：channels (渠道表)
**表说明**：存储用户接入的第三方平台账号信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| user_id | UUID | - | NOT NULL | - | 用户ID | FOREIGN KEY |
| platform_type | VARCHAR | 50 | NOT NULL | - | 平台类型 | - |
| platform_account_id | VARCHAR | 255 | NOT NULL | - | 平台账号ID | - |
| account_alias | VARCHAR | 100 | NOT NULL | - | 账号别名 | - |
| access_token | TEXT | - | NULL | - | 访问令牌(加密) | - |
| refresh_token | TEXT | - | NULL | - | 刷新令牌(加密) | - |
| connection_config | JSONB | - | NULL | - | 连接配置 | - |
| connection_status | VARCHAR | 20 | NOT NULL | 'disconnected' | 连接状态 | - |
| last_connected_at | TIMESTAMP | - | NULL | - | 最后连接时间 | - |
| token_expires_at | TIMESTAMP | - | NULL | - | 令牌过期时间 | - |
| error_message | TEXT | - | NULL | - | 错误信息 | - |
| retry_count | INTEGER | - | NOT NULL | 0 | 重试次数 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_channels_user_id` (`user_id` REFERENCES `users(id)`)
- UNIQUE INDEX: `uk_channels_user_platform_account` (`user_id`, `platform_type`, `platform_account_id`)
- INDEX: `idx_channels_user_id` (`user_id`)
- INDEX: `idx_channels_platform_type` (`platform_type`)
- INDEX: `idx_channels_connection_status` (`connection_status`)
- INDEX: `idx_channels_last_connected_at` (`last_connected_at`)

**业务规则**:
- 平台类型包括：wechat、douyin、xiaohongshu、zhishixingqiu等
- 连接状态：connected、disconnected、error、expired
- 访问令牌使用AES-256加密存储
- 支持令牌自动刷新机制
- 账号别名在用户范围内必须唯一

#### 表名：conversations (对话表)
**表说明**：管理与特定用户或群组的对话会话

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| channel_id | UUID | - | NOT NULL | - | 渠道ID | FOREIGN KEY |
| conversation_type | VARCHAR | 20 | NOT NULL | - | 对话类型 | - |
| external_conversation_id | VARCHAR | 255 | NOT NULL | - | 外部对话ID | - |
| participant_name | VARCHAR | 255 | NULL | - | 参与者名称 | - |
| participant_avatar | VARCHAR | 500 | NULL | - | 参与者头像 | - |
| participant_info | JSONB | - | NULL | - | 参与者信息 | - |
| conversation_mode | VARCHAR | 20 | NOT NULL | 'manual' | 对话模式 | - |
| conversation_status | VARCHAR | 20 | NOT NULL | 'active' | 对话状态 | - |
| ai_confidence_threshold | DECIMAL | 3,2 | NOT NULL | 0.80 | AI置信度阈值 | - |
| last_message_at | TIMESTAMP | - | NULL | - | 最后消息时间 | - |
| message_count | INTEGER | - | NOT NULL | 0 | 消息数量 | - |
| unread_count | INTEGER | - | NOT NULL | 0 | 未读消息数 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_conversations_channel_id` (`channel_id` REFERENCES `channels(id)`)
- UNIQUE INDEX: `uk_conversations_channel_external` (`channel_id`, `external_conversation_id`)
- INDEX: `idx_conversations_channel_id` (`channel_id`)
- INDEX: `idx_conversations_type` (`conversation_type`)
- INDEX: `idx_conversations_mode` (`conversation_mode`)
- INDEX: `idx_conversations_status` (`conversation_status`)
- INDEX: `idx_conversations_last_message_at` (`last_message_at`)

**业务规则**:
- 对话类型：private（私聊）、group（群聊）
- 对话模式：manual（人工模式）、ai_managed（AI托管模式）、pending_takeover（待人工接管）
- 对话状态：active（活跃）、archived（归档）、blocked（屏蔽）
- AI置信度阈值可按对话个性化设置
- 外部对话ID在渠道范围内必须唯一

#### 表名：messages (消息表)
**表说明**：存储具体的消息内容和处理状态

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| conversation_id | UUID | - | NOT NULL | - | 对话ID | FOREIGN KEY |
| channel_id | UUID | - | NOT NULL | - | 渠道ID | FOREIGN KEY |
| external_message_id | VARCHAR | 255 | NULL | - | 外部消息ID | - |
| message_type | VARCHAR | 20 | NOT NULL | - | 消息类型 | - |
| direction | VARCHAR | 10 | NOT NULL | - | 消息方向 | - |
| content | TEXT | - | NULL | - | 消息内容 | - |
| metadata | JSONB | - | NULL | - | 消息元数据 | - |
| processing_status | VARCHAR | 20 | NOT NULL | 'pending' | 处理状态 | - |
| sender_name | VARCHAR | 255 | NULL | - | 发送者名称 | - |
| sender_avatar | VARCHAR | 500 | NULL | - | 发送者头像 | - |
| reply_to_message_id | UUID | - | NULL | - | 回复的消息ID | FOREIGN KEY |
| sent_at | TIMESTAMP | - | NULL | - | 发送时间 | - |
| received_at | TIMESTAMP | - | NULL | - | 接收时间 | - |
| processed_at | TIMESTAMP | - | NULL | - | 处理时间 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_messages_conversation_id` (`conversation_id` REFERENCES `conversations(id)`)
- FOREIGN KEY: `fk_messages_channel_id` (`channel_id` REFERENCES `channels(id)`)
- FOREIGN KEY: `fk_messages_reply_to` (`reply_to_message_id` REFERENCES `messages(id)`)
- INDEX: `idx_messages_conversation_id` (`conversation_id`)
- INDEX: `idx_messages_channel_id` (`channel_id`)
- INDEX: `idx_messages_type` (`message_type`)
- INDEX: `idx_messages_direction` (`direction`)
- INDEX: `idx_messages_processing_status` (`processing_status`)
- INDEX: `idx_messages_sent_at` (`sent_at`)
- INDEX: `idx_messages_conversation_sent_at` (`conversation_id`, `sent_at`)

**业务规则**:
- 消息类型：text、image、file、audio、video、system
- 消息方向：inbound（接收）、outbound（发送）
- 处理状态：pending、processing、processed、failed、sent
- 元数据存储平台特定信息（如微信的msgType、小红书的noteId等）
- 支持消息回复关系链

#### 表名：message_attachments (消息附件表)
**表说明**：存储消息的附件信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| message_id | UUID | - | NOT NULL | - | 消息ID | FOREIGN KEY |
| attachment_type | VARCHAR | 20 | NOT NULL | - | 附件类型 | - |
| file_name | VARCHAR | 255 | NULL | - | 文件名 | - |
| file_size | BIGINT | - | NULL | - | 文件大小(字节) | - |
| file_url | VARCHAR | 1000 | NULL | - | 文件URL | - |
| local_path | VARCHAR | 500 | NULL | - | 本地存储路径 | - |
| mime_type | VARCHAR | 100 | NULL | - | MIME类型 | - |
| thumbnail_url | VARCHAR | 1000 | NULL | - | 缩略图URL | - |
| metadata | JSONB | - | NULL | - | 附件元数据 | - |
| download_status | VARCHAR | 20 | NOT NULL | 'pending' | 下载状态 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_message_attachments_message_id` (`message_id` REFERENCES `messages(id)`)
- INDEX: `idx_message_attachments_message_id` (`message_id`)
- INDEX: `idx_message_attachments_type` (`attachment_type`)
- INDEX: `idx_message_attachments_download_status` (`download_status`)

**业务规则**:
- 附件类型：image、audio、video、document、other
- 下载状态：pending、downloading、completed、failed
- 支持本地存储和云存储两种方式
- 图片和视频自动生成缩略图

#### 表名：ai_analyses (AI分析表)
**表说明**：存储AI对消息的分析结果

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| message_id | UUID | - | NOT NULL | - | 消息ID | FOREIGN KEY |
| analysis_type | VARCHAR | 20 | NOT NULL | - | 分析类型 | - |
| intent_category | VARCHAR | 50 | NULL | - | 意图分类 | - |
| intent_confidence | DECIMAL | 5,4 | NULL | - | 意图置信度 | - |
| sentiment_score | DECIMAL | 3,2 | NULL | - | 情感分数 | - |
| urgency_level | VARCHAR | 10 | NULL | - | 紧急程度 | - |
| extracted_entities | JSONB | - | NULL | - | 提取的实体 | - |
| keywords | JSONB | - | NULL | - | 关键词 | - |
| analysis_result | JSONB | - | NULL | - | 完整分析结果 | - |
| processing_time_ms | INTEGER | - | NULL | - | 处理时间(毫秒) | - |
| ai_model_version | VARCHAR | 50 | NULL | - | AI模型版本 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_ai_analyses_message_id` (`message_id` REFERENCES `messages(id)`)
- UNIQUE INDEX: `uk_ai_analyses_message_type` (`message_id`, `analysis_type`)
- INDEX: `idx_ai_analyses_message_id` (`message_id`)
- INDEX: `idx_ai_analyses_intent_category` (`intent_category`)
- INDEX: `idx_ai_analyses_intent_confidence` (`intent_confidence`)
- INDEX: `idx_ai_analyses_urgency_level` (`urgency_level`)

**业务规则**:
- 分析类型：intent（意图识别）、sentiment（情感分析）、entity（实体提取）
- 意图置信度范围：0.0000-1.0000
- 情感分数范围：-1.00到1.00（负面到正面）
- 紧急程度：low、medium、high、urgent
- 支持多种AI模型版本并行分析

#### 表名：ai_responses (AI回复表)
**表说明**：存储AI生成的回复内容和置信度

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| message_id | UUID | - | NOT NULL | - | 原消息ID | FOREIGN KEY |
| ai_analysis_id | UUID | - | NOT NULL | - | AI分析ID | FOREIGN KEY |
| response_content | TEXT | - | NOT NULL | - | 回复内容 | - |
| confidence_score | DECIMAL | 5,4 | NOT NULL | - | 置信度分数 | - |
| response_type | VARCHAR | 20 | NOT NULL | - | 回复类型 | - |
| knowledge_sources | JSONB | - | NULL | - | 知识来源 | - |
| generation_params | JSONB | - | NULL | - | 生成参数 | - |
| is_auto_sent | BOOLEAN | - | NOT NULL | false | 是否自动发送 | - |
| sent_message_id | UUID | - | NULL | - | 发送的消息ID | FOREIGN KEY |
| feedback_score | INTEGER | - | NULL | - | 反馈评分 | - |
| feedback_comment | TEXT | - | NULL | - | 反馈评论 | - |
| processing_time_ms | INTEGER | - | NULL | - | 生成时间(毫秒) | - |
| ai_model_version | VARCHAR | 50 | NULL | - | AI模型版本 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_ai_responses_message_id` (`message_id` REFERENCES `messages(id)`)
- FOREIGN KEY: `fk_ai_responses_ai_analysis_id` (`ai_analysis_id` REFERENCES `ai_analyses(id)`)
- FOREIGN KEY: `fk_ai_responses_sent_message_id` (`sent_message_id` REFERENCES `messages(id)`)
- INDEX: `idx_ai_responses_message_id` (`message_id`)
- INDEX: `idx_ai_responses_confidence_score` (`confidence_score`)
- INDEX: `idx_ai_responses_is_auto_sent` (`is_auto_sent`)
- INDEX: `idx_ai_responses_feedback_score` (`feedback_score`)

**业务规则**:
- 置信度分数范围：0.0000-1.0000
- 回复类型：direct（直接回复）、suggestion（建议回复）、template（模板回复）
- 置信度≥0.8时可自动发送
- 反馈评分范围：1-5分
- 记录知识来源用于可解释性

#### 表名：knowledge_bases (知识库表)
**表说明**：管理用户的知识库

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| user_id | UUID | - | NOT NULL | - | 用户ID | FOREIGN KEY |
| name | VARCHAR | 100 | NOT NULL | - | 知识库名称 | - |
| description | TEXT | - | NULL | - | 知识库描述 | - |
| knowledge_type | VARCHAR | 20 | NOT NULL | - | 知识库类型 | - |
| is_default | BOOLEAN | - | NOT NULL | false | 是否默认知识库 | - |
| is_active | BOOLEAN | - | NOT NULL | true | 是否激活 | - |
| item_count | INTEGER | - | NOT NULL | 0 | 知识条目数量 | - |
| last_updated_at | TIMESTAMP | - | NULL | - | 最后更新时间 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_knowledge_bases_user_id` (`user_id` REFERENCES `users(id)`)
- INDEX: `idx_knowledge_bases_user_id` (`user_id`)
- INDEX: `idx_knowledge_bases_type` (`knowledge_type`)
- INDEX: `idx_knowledge_bases_is_active` (`is_active`)

**业务规则**:
- 知识库类型：faq（问答）、document（文档）、template（模板）
- 每个用户至少有一个默认知识库
- 知识条目数量通过触发器自动维护

#### 表名：knowledge_items (知识条目表)
**表说明**：存储具体的知识条目内容

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| knowledge_base_id | UUID | - | NOT NULL | - | 知识库ID | FOREIGN KEY |
| item_type | VARCHAR | 20 | NOT NULL | - | 条目类型 | - |
| question | TEXT | - | NOT NULL | - | 问题内容 | - |
| answer | TEXT | - | NOT NULL | - | 答案内容 | - |
| keywords | JSONB | - | NULL | - | 关键词 | - |
| metadata | JSONB | - | NULL | - | 元数据 | - |
| usage_count | INTEGER | - | NOT NULL | 0 | 使用次数 | - |
| effectiveness_score | DECIMAL | 3,2 | NOT NULL | 0.00 | 有效性评分 | - |
| last_used_at | TIMESTAMP | - | NULL | - | 最后使用时间 | - |
| is_active | BOOLEAN | - | NOT NULL | true | 是否激活 | - |
| priority | INTEGER | - | NOT NULL | 0 | 优先级 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_knowledge_items_knowledge_base_id` (`knowledge_base_id` REFERENCES `knowledge_bases(id)`)
- INDEX: `idx_knowledge_items_knowledge_base_id` (`knowledge_base_id`)
- INDEX: `idx_knowledge_items_type` (`item_type`)
- INDEX: `idx_knowledge_items_is_active` (`is_active`)
- INDEX: `idx_knowledge_items_usage_count` (`usage_count`)
- INDEX: `idx_knowledge_items_effectiveness_score` (`effectiveness_score`)
- INDEX: `idx_knowledge_items_priority` (`priority`)
- FULLTEXT INDEX: `idx_knowledge_items_question_fulltext` (`question`)
- FULLTEXT INDEX: `idx_knowledge_items_answer_fulltext` (`answer`)

**业务规则**:
- 条目类型：faq、template、document、link
- 有效性评分范围：0.00-5.00
- 优先级数值越大优先级越高
- 支持全文搜索
- 使用次数和有效性评分用于智能排序

#### 表名：knowledge_matches (知识匹配表)
**表说明**：记录AI分析与知识条目的匹配关系

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| ai_analysis_id | UUID | - | NOT NULL | - | AI分析ID | FOREIGN KEY |
| knowledge_item_id | UUID | - | NOT NULL | - | 知识条目ID | FOREIGN KEY |
| match_score | DECIMAL | 5,4 | NOT NULL | - | 匹配分数 | - |
| match_type | VARCHAR | 20 | NOT NULL | - | 匹配类型 | - |
| match_reason | TEXT | - | NULL | - | 匹配原因 | - |
| is_used | BOOLEAN | - | NOT NULL | false | 是否被使用 | - |
| feedback_score | INTEGER | - | NULL | - | 反馈评分 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_knowledge_matches_ai_analysis_id` (`ai_analysis_id` REFERENCES `ai_analyses(id)`)
- FOREIGN KEY: `fk_knowledge_matches_knowledge_item_id` (`knowledge_item_id` REFERENCES `knowledge_items(id)`)
- INDEX: `idx_knowledge_matches_ai_analysis_id` (`ai_analysis_id`)
- INDEX: `idx_knowledge_matches_knowledge_item_id` (`knowledge_item_id`)
- INDEX: `idx_knowledge_matches_match_score` (`match_score`)
- INDEX: `idx_knowledge_matches_match_type` (`match_type`)
- INDEX: `idx_knowledge_matches_is_used` (`is_used`)

**业务规则**:
- 匹配分数范围：0.0000-1.0000
- 匹配类型：semantic（语义匹配）、keyword（关键词匹配）、exact（精确匹配）
- 反馈评分用于优化匹配算法
- 记录匹配原因用于可解释性

#### 表名：workflow_templates (工作流模板表)
**表说明**：定义自动化工作流模板

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| user_id | UUID | - | NOT NULL | - | 用户ID | FOREIGN KEY |
| name | VARCHAR | 100 | NOT NULL | - | 模板名称 | - |
| description | TEXT | - | NULL | - | 模板描述 | - |
| trigger_type | VARCHAR | 20 | NOT NULL | - | 触发类型 | - |
| trigger_conditions | JSONB | - | NOT NULL | - | 触发条件 | - |
| workflow_definition | JSONB | - | NOT NULL | - | 工作流定义 | - |
| is_active | BOOLEAN | - | NOT NULL | true | 是否激活 | - |
| execution_count | INTEGER | - | NOT NULL | 0 | 执行次数 | - |
| success_count | INTEGER | - | NOT NULL | 0 | 成功次数 | - |
| last_executed_at | TIMESTAMP | - | NULL | - | 最后执行时间 | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_workflow_templates_user_id` (`user_id` REFERENCES `users(id)`)
- INDEX: `idx_workflow_templates_user_id` (`user_id`)
- INDEX: `idx_workflow_templates_trigger_type` (`trigger_type`)
- INDEX: `idx_workflow_templates_is_active` (`is_active`)

**业务规则**:
- 触发类型：message_received、time_based、condition_met、manual
- 工作流定义使用JSON格式存储
- 执行统计用于性能监控

#### 表名：workflow_executions (工作流执行表)
**表说明**：记录工作流的执行实例

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 | 约束条件 |
|--------|----------|------|----------|--------|------|----------|
| id | UUID | - | NOT NULL | uuid_generate_v4() | 主键ID | PRIMARY KEY |
| workflow_template_id | UUID | - | NOT NULL | - | 工作流模板ID | FOREIGN KEY |
| conversation_id | UUID | - | NULL | - | 对话ID | FOREIGN KEY |
| trigger_message_id | UUID | - | NULL | - | 触发消息ID | FOREIGN KEY |
| execution_status | VARCHAR | 20 | NOT NULL | 'pending' | 执行状态 | - |
| execution_context | JSONB | - | NULL | - | 执行上下文 | - |
| execution_result | JSONB | - | NULL | - | 执行结果 | - |
| error_message | TEXT | - | NULL | - | 错误信息 | - |
| started_at | TIMESTAMP | - | NULL | - | 开始时间 | - |
| completed_at | TIMESTAMP | - | NULL | - | 完成时间 | - |
| execution_time_ms | INTEGER | - | NULL | - | 执行时间(毫秒) | - |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | - |
| is_deleted | BOOLEAN | - | NOT NULL | false | 软删除标记 | - |

**索引设计**:
- PRIMARY KEY: `id`
- FOREIGN KEY: `fk_workflow_executions_workflow_template_id` (`workflow_template_id` REFERENCES `workflow_templates(id)`)
- FOREIGN KEY: `fk_workflow_executions_conversation_id` (`conversation_id` REFERENCES `conversations(id)`)
- FOREIGN KEY: `fk_workflow_executions_trigger_message_id` (`trigger_message_id` REFERENCES `messages(id)`)
- INDEX: `idx_workflow_executions_workflow_template_id` (`workflow_template_id`)
- INDEX: `idx_workflow_executions_conversation_id` (`conversation_id`)
- INDEX: `idx_workflow_executions_execution_status` (`execution_status`)
- INDEX: `idx_workflow_executions_started_at` (`started_at`)

**业务规则**:
- 执行状态：pending、running、completed、failed、cancelled
- 记录完整的执行上下文和结果
- 支持执行时间统计和性能分析

### 表关系图

```mermaid
erDiagram
    users ||--o{ user_sessions : "1:N"
    users ||--o{ channels : "1:N"
    users ||--o{ knowledge_bases : "1:N"
    users ||--o{ workflow_templates : "1:N"
    
    channels ||--o{ conversations : "1:N"
    channels ||--o{ messages : "1:N"
    
    conversations ||--o{ messages : "1:N"
    conversations ||--o{ workflow_executions : "1:N"
    
    messages ||--o{ message_attachments : "1:N"
    messages ||--o{ ai_analyses : "1:N"
    messages ||--o{ ai_responses : "1:N"
    messages ||--o{ workflow_executions : "1:N"
    
    ai_analyses ||--o{ ai_responses : "1:N"
    ai_analyses ||--o{ knowledge_matches : "1:N"
    
    knowledge_bases ||--o{ knowledge_items : "1:N"
    knowledge_items ||--o{ knowledge_matches : "1:N"
    
    workflow_templates ||--o{ workflow_executions : "1:N"
    
    ai_responses ||--o{ messages : "sent_message"
    messages ||--o{ messages : "reply_to"
```

## 🔧 物理数据模型

### 数据库配置建议

#### 存储引擎
- **PostgreSQL 15+**：选择原因
  - 强大的JSON/JSONB支持，适合存储消息元数据和AI分析结果
  - 优秀的全文搜索能力，支持知识库检索
  - 成熟的事务支持和数据一致性保证
  - 丰富的扩展生态，支持向量搜索等AI相关功能

#### 字符集与排序规则
- **字符集**：UTF-8 (支持多语言和emoji)
- **排序规则**：zh_CN.UTF-8 (支持中文排序)
- **时区设置**：Asia/Shanghai (统一时区管理)

#### 分区策略
- **消息表分区**：按月分区，提升查询性能
  ```sql
  -- 按月分区示例
  CREATE TABLE messages_2024_01 PARTITION OF messages
  FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
  ```
- **日志表分区**：按日分区，便于数据归档
- **分区维护**：自动创建新分区，定期归档旧分区

#### 索引优化策略
- **主键索引**：使用UUID，分布均匀避免热点
- **外键索引**：所有外键字段自动创建索引
- **复合索引**：根据查询模式设计复合索引
- **部分索引**：对活跃数据创建部分索引
- **表达式索引**：对JSON字段的特定路径创建索引

### 性能优化方案

#### 查询优化
- **消息查询优化**：
  ```sql
  -- 对话消息查询优化
  CREATE INDEX idx_messages_conversation_sent_at 
  ON messages (conversation_id, sent_at DESC) 
  WHERE is_deleted = false;
  
  -- AI分析结果查询优化
  CREATE INDEX idx_ai_analyses_message_intent 
  ON ai_analyses (message_id, intent_category, intent_confidence DESC);
  ```

- **知识库检索优化**：
  ```sql
  -- 全文搜索索引
  CREATE INDEX idx_knowledge_items_fulltext 
  ON knowledge_items USING gin(to_tsvector('chinese', question || ' ' || answer));
  
  -- 知识匹配优化
  CREATE INDEX idx_knowledge_matches_score_used 
  ON knowledge_matches (knowledge_item_id, match_score DESC, is_used);
  ```

#### 缓存策略
- **L1缓存（应用内存）**：
  - 用户会话信息（30分钟TTL）
  - 渠道连接状态（5分钟TTL）
  - 热点知识条目（1小时TTL）

- **L2缓存（Redis）**：
  - 用户权限信息（1小时TTL）
  - 对话上下文（24小时TTL）
  - AI模型响应（6小时TTL）
  - 知识库搜索结果（30分钟TTL）

- **L3缓存（数据库查询结果集）**：
  - 统计查询结果（15分钟TTL）
  - 复杂关联查询（10分钟TTL）

#### 读写分离方案
- **主库（写操作）**：
  - 用户注册、登录
  - 消息接收、发送
  - 知识库管理
  - 工作流执行

- **从库（读操作）**：
  - 消息历史查询
  - 统计报表生成
  - 知识库搜索
  - 日志分析

- **数据同步**：
  - 主从延迟控制在100ms内
  - 关键业务查询强制走主库
  - 定期检查主从数据一致性

#### 数据归档策略
- **消息数据归档**：
  - 6个月以上的消息数据迁移到归档库
  - 保留索引信息便于检索
  - 归档数据压缩存储

- **日志数据归档**：
  - 30天以上的日志数据归档到对象存储
  - 保留关键错误日志便于问题排查
  - 定期清理临时日志文件

- **AI分析结果归档**：
  - 3个月以上的AI分析结果归档
  - 保留统计汇总数据
  - 定期清理低价值分析记录

## 🔒 数据安全设计

### 敏感数据识别

#### 个人信息
- **用户身份信息**：手机号、邮箱、真实姓名
- **认证信息**：密码哈希、访问令牌、刷新令牌
- **设备信息**：IP地址、设备标识、用户代理
- **行为数据**：登录记录、操作日志、位置信息

#### 业务机密
- **消息内容**：用户对话记录、群聊内容
- **知识库数据**：FAQ内容、业务规则、模板信息
- **AI分析结果**：意图识别、情感分析、用户画像
- **平台凭证**：第三方平台的访问令牌和密钥

#### 访问控制
- **基于角色的权限控制（RBAC）**：
  - 超级管理员：系统全部权限
  - 普通用户：个人数据管理权限
  - 只读用户：数据查看权限
  - 审计用户：日志查看权限

- **数据访问权限**：
  - 用户只能访问自己的数据
  - 跨用户数据访问需要明确授权
  - 敏感操作需要二次验证
  - 定期审计权限分配

### 安全措施

#### 数据加密
- **传输加密**：
  - 所有API通信使用TLS 1.3
  - 数据库连接使用SSL加密
  - 内部服务通信使用mTLS

- **存储加密**：
  ```sql
  -- 敏感字段加密示例
  CREATE EXTENSION IF NOT EXISTS pgcrypto;
  
  -- 访问令牌加密存储
  UPDATE channels SET 
  access_token = pgp_sym_encrypt(access_token, 'encryption_key')
  WHERE access_token IS NOT NULL;
  
  -- 密码哈希存储
  CREATE OR REPLACE FUNCTION hash_password(password TEXT) 
  RETURNS TEXT AS $$
  BEGIN
    RETURN crypt(password, gen_salt('bf', 12));
  END;
  $$ LANGUAGE plpgsql;
  ```

- **字段级加密**：
  - 访问令牌：AES-256-GCM加密
  - 密码：bcrypt哈希（cost=12）
  - 手机号：可逆加密，支持搜索
  - 消息内容：可选加密，用户控制

#### 审计日志
- **操作审计**：
  ```sql
  CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    operation_type VARCHAR(50) NOT NULL,
    table_name VARCHAR(50),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
  
  -- 触发器示例
  CREATE OR REPLACE FUNCTION audit_trigger_function()
  RETURNS TRIGGER AS $$
  BEGIN
    INSERT INTO audit_logs (
      user_id, operation_type, table_name, record_id, 
      old_values, new_values, ip_address
    ) VALUES (
      COALESCE(NEW.user_id, OLD.user_id),
      TG_OP,
      TG_TABLE_NAME,
      COALESCE(NEW.id, OLD.id),
      CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
      CASE WHEN TG_OP = 'INSERT' THEN row_to_json(NEW) 
           WHEN TG_OP = 'UPDATE' THEN row_to_json(NEW) ELSE NULL END,
      inet_client_addr()
    );
    RETURN COALESCE(NEW, OLD);
  END;
  $$ LANGUAGE plpgsql;
  ```

- **访问日志**：
  - 记录所有数据访问操作
  - 包含用户身份、访问时间、访问内容
  - 异常访问模式自动告警
  - 日志数据定期归档和分析

#### 备份恢复
- **备份策略**：
  - 每日全量备份到异地存储
  - 每小时增量备份到本地存储
  - 关键数据实时同步到灾备中心
  - 备份数据加密存储

- **恢复测试**：
  ```bash
  #!/bin/bash
  # 数据恢复测试脚本
  
  # 1. 创建测试环境
  createdb chaiguanjia_test
  
  # 2. 恢复备份数据
  pg_restore -d chaiguanjia_test /backup/chaiguanjia_$(date +%Y%m%d).dump
  
  # 3. 数据完整性检查
  psql -d chaiguanjia_test -c "
    SELECT 
      'users' as table_name, COUNT(*) as record_count 
    FROM users
    UNION ALL
    SELECT 
      'messages' as table_name, COUNT(*) as record_count 
    FROM messages
    UNION ALL
    SELECT 
      'channels' as table_name, COUNT(*) as record_count 
    FROM channels;
  "
  
  # 4. 清理测试环境
  dropdb chaiguanjia_test
  
  echo "备份恢复测试完成"
  ```

## 📊 数据字典

### 枚举值定义

#### 平台类型 (platform_type)
| 值 | 含义 | 说明 |
|----|------|------|
| wechat | 微信 | 微信公众号、企业微信 |
| douyin | 抖音 | 抖音私信、评论 |
| xiaohongshu | 小红书 | 小红书私信、评论 |
| zhishixingqiu | 知识星球 | 知识星球问答 |
| qq | QQ | QQ群、QQ空间 |
| weibo | 微博 | 微博私信、评论 |

#### 连接状态 (connection_status)
| 值 | 含义 | 说明 |
|----|------|------|
| connected | 已连接 | 正常连接状态 |
| disconnected | 已断开 | 主动断开连接 |
| error | 连接错误 | 连接异常，需要重试 |
| expired | 令牌过期 | 访问令牌已过期 |
| unauthorized | 未授权 | 授权被撤销 |

#### 消息类型 (message_type)
| 值 | 含义 | 说明 |
|----|------|------|
| text | 文本消息 | 纯文本内容 |
| image | 图片消息 | 图片文件 |
| video | 视频消息 | 视频文件 |
| audio | 语音消息 | 音频文件 |
| file | 文件消息 | 其他类型文件 |
| location | 位置消息 | 地理位置信息 |
| system | 系统消息 | 系统通知消息 |

#### 处理状态 (processing_status)
| 值 | 含义 | 说明 |
|----|------|------|
| pending | 待处理 | 消息已接收，等待处理 |
| processing | 处理中 | 正在进行AI分析 |
| processed | 已处理 | AI分析完成 |
| failed | 处理失败 | 处理过程中出错 |
| sent | 已发送 | 回复消息已发送 |

#### 对话模式 (conversation_mode)
| 值 | 含义 | 说明 |
|----|------|------|
| manual | 人工模式 | 完全人工处理 |
| ai_managed | AI托管模式 | AI自动处理 |
| pending_takeover | 待人工接管 | AI处理失败，等待人工接管 |
| hybrid | 混合模式 | AI辅助人工处理 |

### 编码规则

#### 主键规则
- **UUID格式**：使用UUID v4格式，如 `550e8400-e29b-41d4-a716-************`
- **生成方式**：数据库自动生成，确保全局唯一性
- **索引策略**：主键自动创建聚簇索引

#### 业务编码
- **用户编号**：U + 8位数字，如 `U00000001`
- **渠道编号**：C + 8位数字，如 `C00000001`
- **对话编号**：CONV + 10位数字，如 `CONV0000000001`
- **消息编号**：MSG + 12位数字，如 `MSG000000000001`

#### 命名规范
- **表名**：小写字母，单词间用下划线分隔，如 `user_sessions`
- **字段名**：小写字母，单词间用下划线分隔，如 `created_at`
- **索引名**：
  - 主键索引：`pk_表名`
  - 唯一索引：`uk_表名_字段名`
  - 普通索引：`idx_表名_字段名`
  - 外键索引：`fk_表名_引用表名`

## 🚀 实施建议

### 开发阶段规划

#### 第一阶段：基础架构搭建（2-3周）
- **数据库环境搭建**：
  - PostgreSQL 15安装配置
  - 数据库用户权限设置
  - 备份恢复机制建立
  - 监控告警系统部署

- **核心表结构创建**：
  - 用户管理相关表
  - 渠道管理相关表
  - 基础配置表
  - 审计日志表

- **基础功能开发**：
  - 用户注册登录
  - 渠道连接管理
  - 基础权限控制
  - 数据库迁移脚本

#### 第二阶段：消息处理系统（3-4周）
- **消息相关表创建**：
  - 对话表和消息表
  - 消息附件表
  - 消息处理状态表

- **消息处理功能**：
  - 消息接收和存储
  - 消息格式标准化
  - 消息状态管理
  - 消息查询和检索

- **实时通信**：
  - WebSocket连接管理
  - 消息推送机制
  - 连接状态监控

#### 第三阶段：AI智能处理（4-5周）
- **AI相关表创建**：
  - AI分析结果表
  - AI响应记录表
  - 知识库相关表

- **AI功能集成**：
  - 意图识别服务
  - 自动回复生成
  - 知识库匹配
  - 置信度评估

- **智能决策**：
  - 自动回复策略
  - 人工接管机制
  - 学习优化算法

#### 第四阶段：高级功能开发（3-4周）
- **工作流引擎**：
  - 工作流模板设计
  - 工作流执行引擎
  - 条件判断和分支
  - 工作流监控

- **数据分析**：
  - 统计报表生成
  - 用户行为分析
  - 效果评估指标
  - 数据可视化

- **系统优化**：
  - 性能调优
  - 缓存优化
  - 查询优化
  - 容量规划

### 数据迁移方案

#### 迁移策略
- **增量迁移**：支持在线业务不中断的数据迁移
- **数据验证**：迁移过程中实时验证数据完整性
- **回滚机制**：迁移失败时快速回滚到原始状态
- **性能监控**：监控迁移过程对系统性能的影响

#### 迁移步骤
```sql
-- 1. 创建迁移日志表
CREATE TABLE migration_log (
  id SERIAL PRIMARY KEY,
  migration_name VARCHAR(100) NOT NULL,
  start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  end_time TIMESTAMP,
  status VARCHAR(20) DEFAULT 'running',
  records_processed INTEGER DEFAULT 0,
  error_message TEXT
);

-- 2. 数据迁移函数
CREATE OR REPLACE FUNCTION migrate_user_data()
RETURNS void AS $$
DECLARE
  migration_id INTEGER;
  processed_count INTEGER := 0;
BEGIN
  -- 记录迁移开始
  INSERT INTO migration_log (migration_name) 
  VALUES ('migrate_user_data') 
  RETURNING id INTO migration_id;
  
  -- 执行数据迁移
  -- ... 迁移逻辑 ...
  
  -- 更新迁移状态
  UPDATE migration_log 
  SET end_time = CURRENT_TIMESTAMP, 
      status = 'completed',
      records_processed = processed_count
  WHERE id = migration_id;
  
EXCEPTION WHEN OTHERS THEN
  -- 记录迁移失败
  UPDATE migration_log 
  SET end_time = CURRENT_TIMESTAMP, 
      status = 'failed',
      error_message = SQLERRM
  WHERE id = migration_id;
  RAISE;
END;
$$ LANGUAGE plpgsql;
```

### 监控和维护

#### 性能监控指标
- **数据库性能**：
  - 查询响应时间（目标：<100ms）
  - 连接池使用率（目标：<80%）
  - 慢查询数量（目标：<10/小时）
  - 锁等待时间（目标：<50ms）

- **业务指标**：
  - 消息处理延迟（目标：<1秒）
  - AI回复准确率（目标：>85%）
  - 系统可用性（目标：>99.9%）
  - 用户满意度（目标：>4.5/5）

#### 容量规划
- **存储容量**：
  - 用户数据：每用户约1MB
  - 消息数据：每条消息约2KB
  - 知识库数据：每条目约5KB
  - 日志数据：每日约100MB

- **性能容量**：
  - 并发用户数：1000+
  - 消息处理量：10000条/小时
  - 数据库连接数：200+
  - API请求量：50000次/小时

#### 维护计划
- **日常维护**：
  - 监控系统状态和性能指标
  - 检查备份任务执行情况
  - 清理临时文件和日志
  - 更新统计信息

- **周期维护**：
  - 数据库性能调优（每周）
  - 索引重建和优化（每月）
  - 数据归档和清理（每月）
  - 安全补丁更新（每月）

- **年度维护**：
  - 系统架构评估和优化
  - 容量规划和扩展
  - 灾难恢复演练
  - 技术栈升级评估

## 📝 附录

### 参考资料
- [PostgreSQL官方文档](https://www.postgresql.org/docs/)
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Redis官方文档](https://redis.io/documentation)
- [RabbitMQ官方文档](https://www.rabbitmq.com/documentation.html)
- [数据库设计最佳实践](https://www.postgresql.org/docs/current/ddl-best-practices.html)

### 变更记录
| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2024-12-19 | 初始版本，完成基础数据架构设计 | 系统架构师 |

---

**文档说明**：本文档基于柴管家项目的业务需求和技术架构，设计了完整的数据架构方案。涵盖了从概念模型到物理实现的全过程，包括性能优化、安全设计、监控维护等各个方面。文档将随着项目发展持续更新和完善。