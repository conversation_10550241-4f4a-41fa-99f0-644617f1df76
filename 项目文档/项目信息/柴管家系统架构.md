# 柴管家系统架构

## 📋 项目概述

**柴管家**是一个基于AI的智能客服系统，采用模块化单体架构，支持多渠道接入、智能对话、知识管理和工作流自动化。

## 🏗️ 系统架构

### 架构风格
- **模块化单体架构**：平衡开发效率与系统复杂度
- **分层架构**：API层 → 业务层 → 数据层 → 基础设施层
- **事件驱动**：模块间通过RabbitMQ异步通信

### 核心模块

```mermaid
graph TB
    subgraph "柴管家系统架构"
        subgraph "API网关层"
            GATEWAY[API Gateway<br/>Nginx + FastAPI]
        end
        
        subgraph "业务模块层"
            CHANNEL[渠道管理模块<br/>多平台接入]
            MESSAGE[消息处理模块<br/>消息路由分发]
            AI[AI智能服务模块<br/>对话生成]
            KNOWLEDGE[知识管理模块<br/>知识库检索]
            USER[用户管理模块<br/>权限认证]
            WORKFLOW[工作流引擎模块<br/>业务流程]
        end
        
        subgraph "基础设施层"
            DB[(PostgreSQL<br/>主数据库)]
            CACHE[(Redis<br/>缓存)]
            MQ[RabbitMQ<br/>消息队列]
            AI_API[通义千问 API<br/>AI服务]
        end
        
        GATEWAY --> CHANNEL
        GATEWAY --> MESSAGE
        GATEWAY --> AI
        GATEWAY --> KNOWLEDGE
        GATEWAY --> USER
        GATEWAY --> WORKFLOW
        
        CHANNEL --> MQ
        MESSAGE --> MQ
        AI --> MQ
        KNOWLEDGE --> MQ
        USER --> MQ
        WORKFLOW --> MQ
        
        CHANNEL --> DB
        MESSAGE --> DB
        AI --> CACHE
        KNOWLEDGE --> DB
        USER --> DB
        WORKFLOW --> DB
        
        AI --> AI_API
    end
    
    style GATEWAY fill:#e3f2fd
    style CHANNEL fill:#f3e5f5
    style MESSAGE fill:#e8f5e8
    style AI fill:#fff3e0
    style KNOWLEDGE fill:#fce4ec
    style USER fill:#e0f2f1
    style WORKFLOW fill:#f1f8e9
    style DB fill:#ffecb3
    style CACHE fill:#ffcdd2
    style MQ fill:#d1c4e9
    style AI_API fill:#c8e6c9
```

## 🛠️ 技术栈

### 前端技术
- **React** 18.x - 用户界面框架
- **TypeScript** 5.x - 类型安全
- **shadcn/ui** - 现代化UI组件
- **Tailwind CSS** - 样式框架

### 后端技术
- **Python** 3.11+ - 主要开发语言
- **FastAPI** 0.104+ - API框架
- **SQLAlchemy** - ORM框架
- **Pydantic** - 数据验证

### 数据存储
- **PostgreSQL** 15+ - 主数据库
- **Redis** 7+ - 缓存和会话

### 消息队列
- **RabbitMQ** 3.12+ - 异步消息处理

### AI服务
- **通义千问 API** - 主要AI对话服务
- **GPT-4/Claude** - 备用AI服务
- **智能路由** - 多模型负载均衡

### 基础设施
- **Docker** 24+ - 容器化部署
- **Docker Compose** - 服务编排
- **Nginx** 1.24+ - 反向代理和负载均衡

### 监控运维（渐进式方案）
#### MVP阶段
- **应用内置监控** - FastAPI集成基础指标
- **健康检查端点** - /health, /metrics接口
- **结构化日志** - JSON格式文件日志
- **基础告警** - 邮件/微信通知

### 安全认证
- **JWT** - 无状态认证
- **OAuth2** - 统一授权
- **RBAC** - 基于角色的权限控制

## 🔄 数据流架构

```mermaid
sequenceDiagram
    participant User as 用户
    participant Gateway as API网关
    participant Channel as 渠道管理
    participant Message as 消息处理
    participant AI as AI服务
    participant Knowledge as 知识库
    participant MQ as RabbitMQ
    
    User->>Gateway: 发送消息
    Gateway->>Channel: 路由到渠道
    Channel->>MQ: 发布消息事件
    MQ->>Message: 消息处理
    Message->>AI: 请求AI回复
    AI->>Knowledge: 检索知识库
    Knowledge-->>AI: 返回相关知识
    AI-->>Message: 生成回复
    Message->>MQ: 发布回复事件
    MQ->>Channel: 推送回复
    Channel-->>Gateway: 返回回复
    Gateway-->>User: 发送回复
```

## 📊 模块通信策略

### 主模块间通信
- **异步优先**：使用RabbitMQ进行事件驱动通信
- **事件命名**：`模块名.动作.版本` (如：`message.received.v1`)
- **故障隔离**：模块故障不影响其他模块
- **幂等性**：确保消息重复处理的安全性

### 子模块内通信
- **同步调用**：模块内部使用直接函数调用
- **内部事件**：复杂业务逻辑使用内部事件总线
- **共享资源**：通过依赖注入共享数据库连接等资源

## 🔐 安全架构

### 认证授权
- **JWT Token**：无状态认证，支持分布式部署
- **Refresh Token**：安全的令牌刷新机制
- **OAuth2**：支持第三方平台集成
- **RBAC权限**：细粒度的角色权限控制

### API安全
- **API网关**：统一入口，限流熔断
- **HTTPS/TLS**：传输层加密
- **输入验证**：Pydantic数据验证
- **SQL注入防护**：SQLAlchemy ORM保护

## 📈 缓存策略

### 多层缓存架构
- **L1缓存**：应用内存缓存，AI响应和热点数据
- **L2缓存**：Redis缓存，用户会话和状态数据
- **L3缓存**：数据库查询结果集缓存

### AI特化缓存
- **相似问题复用**：减少重复API调用成本
- **对话上下文**：支持多轮对话状态保持
- **知识库缓存**：提升检索响应速度

## 📊 监控观测（渐进式实施）

### MVP阶段监控指标
```mermaid
pie title MVP阶段监控优先级
    "服务可用性" : 35
    "AI服务状态" : 25
    "消息处理性能" : 20
    "用户体验指标" : 15
    "资源使用率" : 5
```

#### 核心监控指标
- **服务健康**：应用启动状态、数据库连接、Redis连接
- **AI服务**：模型响应时间、API调用成功率、置信度分布
- **消息处理**：消息队列长度、处理延迟、失败率
- **用户体验**：登录成功率、对话响应时间

### 日志管理策略
#### MVP阶段
- **结构化日志**：JSON格式输出到文件
- **分级记录**：ERROR/WARN/INFO（生产环境）
- **日志轮转**：按日期和大小自动轮转
- **关键事件**：用户登录、AI调用、错误异常



## 📁 项目结构设计

### 整体项目结构

```
chaiguanjia/
├── frontend/                   # 前端应用
│   ├── src/
│   │   ├── components/         # 通用组件
│   │   ├── pages/             # 页面组件
│   │   ├── hooks/             # 自定义Hooks
│   │   ├── services/          # API服务
│   │   ├── stores/            # 状态管理
│   │   ├── utils/             # 工具函数
│   │   ├── types/             # TypeScript类型定义
│   │   ├── assets/            # 静态资源
│   │   │   ├── images/        # 图片资源
│   │   │   ├── icons/         # 图标资源
│   │   │   └── styles/        # 样式文件
│   │   ├── layouts/           # 布局组件
│   │   ├── contexts/          # React Context
│   │   └── constants/         # 常量定义
│   ├── public/                # 公共静态资源
│   ├── package.json
│   ├── tsconfig.json
│   └── tailwind.config.js
│
├── backend/                    # 后端应用
│   ├── app/
│   │   ├── main.py            # FastAPI应用入口
│   │   ├── config/            # 配置管理
│   │   │   ├── __init__.py
│   │   │   ├── environments/  # 环境配置
│   │   │   │   ├── __init__.py
│   │   │   │   ├── development.py
│   │   │   │   ├── testing.py
│   │   │   │   ├── staging.py
│   │   │   │   └── production.py
│   │   │   ├── settings.py    # 基础配置
│   │   │   ├── database.py    # 数据库配置
│   │   │   ├── cache.py       # 缓存配置
│   │   │   ├── messaging.py   # 消息队列配置
│   │   │   ├── security.py    # 安全配置
│   │   │   └── logging.py     # 日志配置
│   │   ├── core/              # 核心功能
│   │   │   ├── __init__.py
│   │   │   ├── security.py    # 安全认证
│   │   │   ├── middleware.py  # 中间件
│   │   │   └── exceptions.py  # 异常处理
│   │   ├── middleware/        # 中间件管理
│   │   │   ├── __init__.py
│   │   │   ├── auth.py        # 认证中间件
│   │   │   ├── cors.py        # CORS中间件
│   │   │   ├── logging.py     # 日志中间件
│   │   │   └── rate_limiting.py # 限流中间件
│   │   ├── plugins/           # 插件扩展
│   │   │   ├── __init__.py
│   │   │   └── interfaces/    # 插件接口
│   │   │       └── __init__.py
│   │   ├── modules/           # 业务模块
│   │   │   ├── __init__.py
│   │   │   ├── channel_management/    # 渠道管理模块
│   │   │   │   ├── __init__.py
│   │   │   │   ├── api/              # API层
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── routers.py    # 路由定义
│   │   │   │   │   ├── schemas.py    # 请求/响应模型
│   │   │   │   │   └── dependencies.py # 依赖注入
│   │   │   │   ├── services/         # 业务层
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── channel_service.py
│   │   │   │   │   └── connection_service.py
│   │   │   │   ├── models/           # 数据层
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── channel.py
│   │   │   │   │   └── connection.py
│   │   │   │   └── tests/            # 测试
│   │   │   │       ├── __init__.py
│   │   │   │       ├── test_api.py
│   │   │   │       ├── test_services.py
│   │   │   │       └── features/     # BDD测试场景
│   │   │   ├── message_processing/   # 消息处理模块
│   │   │   │   ├── __init__.py
│   │   │   │   ├── api/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── routers.py
│   │   │   │   │   ├── schemas.py
│   │   │   │   │   └── dependencies.py
│   │   │   │   ├── services/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   └── message_service.py
│   │   │   │   ├── models/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   └── message.py
│   │   │   │   └── tests/
│   │   │   │       ├── __init__.py
│   │   │   │       ├── test_api.py
│   │   │   │       ├── test_services.py
│   │   │   │       └── features/
│   │   │   ├── ai_services/          # AI服务模块
│   │   │   │   ├── __init__.py
│   │   │   │   ├── api/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── routers.py
│   │   │   │   │   ├── schemas.py
│   │   │   │   │   └── dependencies.py
│   │   │   │   ├── services/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   └── ai_service.py
│   │   │   │   ├── models/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   └── ai_model.py
│   │   │   │   └── tests/
│   │   │   │       ├── __init__.py
│   │   │   │       ├── test_api.py
│   │   │   │       ├── test_services.py
│   │   │   │       └── features/
│   │   │   ├── knowledge_management/ # 知识管理模块
│   │   │   │   ├── __init__.py
│   │   │   │   ├── api/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── routers.py
│   │   │   │   │   ├── schemas.py
│   │   │   │   │   └── dependencies.py
│   │   │   │   ├── services/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   └── knowledge_service.py
│   │   │   │   ├── models/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   └── knowledge.py
│   │   │   │   └── tests/
│   │   │   │       ├── __init__.py
│   │   │   │       ├── test_api.py
│   │   │   │       ├── test_services.py
│   │   │   │       └── features/
│   │   │   ├── user_management/      # 用户管理模块
│   │   │   │   ├── __init__.py
│   │   │   │   ├── api/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── routers.py
│   │   │   │   │   ├── schemas.py
│   │   │   │   │   └── dependencies.py
│   │   │   │   ├── services/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   └── user_service.py
│   │   │   │   ├── models/
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   └── user.py
│   │   │   │   └── tests/
│   │   │   │       ├── __init__.py
│   │   │   │       ├── test_api.py
│   │   │   │       ├── test_services.py
│   │   │   │       └── features/
│   │   │   └── workflow_engine/      # 工作流引擎模块
│   │   │       ├── __init__.py
│   │   │       ├── api/
│   │   │       │   ├── __init__.py
│   │   │       │   ├── routers.py
│   │   │       │   ├── schemas.py
│   │   │       │   └── dependencies.py
│   │   │       ├── services/
│   │   │       │   ├── __init__.py
│   │   │       │   └── workflow_service.py
│   │   │       ├── models/
│   │   │       │   ├── __init__.py
│   │   │       │   └── workflow.py
│   │   │       └── tests/
│   │   │           ├── __init__.py
│   │   │           ├── test_api.py
│   │   │           ├── test_services.py
│   │   │           └── features/
│   │   ├── shared/            # 共享组件
│   │   │   ├── __init__.py
│   │   │   ├── database/      # 数据库配置
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base.py    # 基础模型
│   │   │   │   └── session.py # 数据库会话
│   │   │   ├── cache/         # 缓存配置
│   │   │   │   ├── __init__.py
│   │   │   │   ├── redis_client.py
│   │   │   │   └── cache_manager.py
│   │   │   ├── messaging/     # 消息队列
│   │   │   │   ├── __init__.py
│   │   │   │   ├── rabbitmq_client.py
│   │   │   │   └── event_bus.py
│   │   │   ├── security/      # 安全组件
│   │   │   │   ├── __init__.py
│   │   │   │   ├── jwt_handler.py
│   │   │   │   ├── oauth2/    # OAuth2认证
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── providers/
│   │   │   │   │   │   └── __init__.py
│   │   │   │   │   └── callbacks/
│   │   │   │   │       └── __init__.py
│   │   │   │   ├── rbac/      # 基于角色的权限控制
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── roles.py
│   │   │   │   │   └── permissions.py
│   │   │   │   └── encryption/ # 加密工具
│   │   │   │       ├── __init__.py
│   │   │   │       └── utils.py
│   │   │   ├── monitoring/    # 监控组件
│   │   │   │   ├── __init__.py
│   │   │   │   ├── metrics.py
│   │   │   │   └── logging.py
│   │   │   └── utils/         # 工具函数
│   │   │       ├── __init__.py
│   │   │       ├── validators.py
│   │   │       └── helpers.py
│   │   └── api/               # API路由聚合
│   │       ├── __init__.py
│   │       ├── v1/            # API版本管理v1
│   │       │   ├── __init__.py
│   │       │   └── api.py     # 路由聚合
│   │       └── v2/            # API版本管理v2（预留）
│   │           └── __init__.py
│   ├── database/              # 数据库结构设计
│   │   ├── migrations/        # 数据库迁移文件
│   │   │   ├── 001_initial_schema.sql
│   │   │   ├── 002_add_channels.sql
│   │   │   ├── 003_add_messages.sql
│   │   │   └── ...
│   │   ├── schemas/           # 数据库Schema
│   │   │   ├── users/         # 用户相关表
│   │   │   ├── channels/      # 渠道相关表
│   │   │   ├── messages/      # 消息相关表
│   │   │   ├── knowledge/     # 知识库相关表
│   │   │   └── workflows/     # 工作流相关表
│   │   └── seeds/            # 初始数据
│   │       ├── users.sql
│   │       ├── roles.sql
│   │       └── permissions.sql
│   ├── alembic/               # 数据库迁移
│   │   ├── versions/
│   │   ├── env.py
│   │   └── alembic.ini
│   ├── tests/                 # 集成测试
│   │   ├── conftest.py
│   │   ├── test_integration.py
│   │   └── features/          # BDD集成测试
│   ├── requirements.txt       # Python依赖
│   ├── Dockerfile
│   └── .env.example          # 环境变量示例
│
├── infrastructure/            # 基础设施配置
│   ├── gateway/              # API网关配置
│   │   ├── nginx/            # Nginx网关
│   │   │   ├── conf.d/       # 配置文件
│   │   │   ├── ssl/          # SSL证书
│   │   │   └── logs/         # 日志文件
│   │   ├── load-balancer/    # 负载均衡
│   │   └── rate-limiting/    # 限流配置
│   ├── docker/               # Docker配置
│   │   ├── docker-compose.yml
│   │   ├── docker-compose.prod.yml
│   │   └── nginx/
│   │       └── nginx.conf
│   ├── monitoring/           # 监控配置（MVP阶段）
│   │   ├── logging/          # 日志配置
│   │   │   ├── logrotate.conf
│   │   │   └── rsyslog.conf
│   │   ├── health-checks/    # 健康检查脚本
│   │   │   ├── app-health.sh
│   │   │   └── db-health.sh
│   │   └── alerting/         # 基础告警配置
│   │       ├── email-alerts.py
│   │       └── wechat-alerts.py
│   └── scripts/              # 部署脚本
│       ├── deploy.sh
│       ├── backup.sh
│       └── init-db.sh
│
├── docs/                     # 项目文档
│   ├── api/                  # API文档
│   ├── architecture/         # 架构文档
│   ├── deployment/           # 部署文档
│   └── user-guide/          # 用户指南
│
├── tests/                    # 端到端测试
│   ├── e2e/                 # E2E测试
│   ├── performance/         # 性能测试
│   └── security/            # 安全测试
│
├── .github/                  # GitHub配置
│   ├── workflows/           # CI/CD工作流
│   │   ├── backend-ci.yml
│   │   ├── frontend-ci.yml
│   │   └── deploy.yml
│   └── ISSUE_TEMPLATE/      # Issue模板
│
├── README.md                # 项目说明
├── .gitignore              # Git忽略文件
├── .env.example            # 环境变量示例
└── docker-compose.yml      # 开发环境配置
```

### 模块内部结构设计

每个业务模块采用统一的**三层架构**：

```mermaid
graph TB
    subgraph "模块内部结构 (以渠道管理为例)"
        subgraph "API层"
            API1[routers.py<br/>路由定义和控制器]
            API2[schemas.py<br/>请求/响应数据模型]
            API3[dependencies.py<br/>依赖注入和验证]
        end
        
        subgraph "业务层"
            BIZ1[channel_service.py<br/>渠道业务逻辑]
            BIZ2[connection_service.py<br/>连接管理逻辑]
            BIZ3[event_handlers.py<br/>事件处理器]
        end
        
        subgraph "数据层"
            DATA1[channel.py<br/>渠道数据模型]
            DATA2[connection.py<br/>连接数据模型]
            DATA3[repositories.py<br/>数据访问层]
        end
        
        subgraph "测试层"
            TEST1[test_api.py<br/>API测试]
            TEST2[test_services.py<br/>业务逻辑测试]
            TEST3[features/<br/>BDD行为测试]
        end
    end
    
    API1 --> BIZ1
    API2 --> BIZ1
    API3 --> BIZ2
    
    BIZ1 --> DATA1
    BIZ2 --> DATA2
    BIZ3 --> DATA3
    
    TEST1 --> API1
    TEST2 --> BIZ1
    TEST3 --> BIZ1
    
    style API1 fill:#e3f2fd
    style API2 fill:#e3f2fd
    style API3 fill:#e3f2fd
    style BIZ1 fill:#f3e5f5
    style BIZ2 fill:#f3e5f5
    style BIZ3 fill:#f3e5f5
    style DATA1 fill:#e8f5e8
    style DATA2 fill:#e8f5e8
    style DATA3 fill:#e8f5e8
    style TEST1 fill:#fff3e0
    style TEST2 fill:#fff3e0
    style TEST3 fill:#fff3e0
```

### 配置管理结构

```
config/
├── environments/             # 环境配置
│   ├── development.py       # 开发环境
│   ├── testing.py          # 测试环境
│   ├── staging.py          # 预发布环境
│   └── production.py       # 生产环境
├── settings.py             # 基础配置
├── database.py             # 数据库配置
├── cache.py               # 缓存配置
├── messaging.py           # 消息队列配置
├── security.py            # 安全配置
└── logging.py             # 日志配置
```

### 数据库结构设计

```
database/
├── migrations/             # 数据库迁移文件
│   ├── 001_initial_schema.sql
│   ├── 002_add_channels.sql
│   ├── 003_add_messages.sql
│   └── ...
├── schemas/               # 数据库Schema
│   ├── users/            # 用户相关表
│   ├── channels/         # 渠道相关表
│   ├── messages/         # 消息相关表
│   ├── knowledge/        # 知识库相关表
│   └── workflows/        # 工作流相关表
└── seeds/                # 初始数据
    ├── users.sql
    ├── roles.sql
    └── permissions.sql
```

## 🚀 部署架构

### Docker Compose部署（MVP阶段）
```yaml
# MVP阶段简化部署配置
services:
  api-gateway:
    image: nginx:1.24
    ports:
      - "80:80"
      - "443:443"
  
  chaiguanjia-api:
    image: chaiguanjia:latest
    environment:
      - DATABASE_URL=**********************************************/chaiguanjia
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - LOG_LEVEL=INFO
      - ENABLE_METRICS=true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs  # 日志文件映射
  
  postgresql:
    image: postgres:15
    environment:
      - POSTGRES_DB=chaiguanjia
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:7
    volumes:
      - redis_data:/data
  
  rabbitmq:
    image: rabbitmq:3.12-management
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
```

### 成长阶段扩展配置
```yaml
# 可选：当需要更复杂监控时添加
  prometheus:
    image: prom/prometheus
    # 仅在用户量增长后添加
  
  grafana:
    image: grafana/grafana
    # 仅在需要复杂分析时添加
```

