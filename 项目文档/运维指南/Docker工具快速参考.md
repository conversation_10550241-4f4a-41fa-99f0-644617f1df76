# Docker工具快速参考

> 柴管家项目Docker运维工具速查手册

## 🛠️ 可用工具列表

### 核心运维脚本

| 脚本名称 | 功能描述 | 使用示例 |
|---------|----------|----------|
| `health-check.sh` | 全面健康检查 | `./scripts/health-check.sh` |
| `diagnose.sh` | 故障诊断工具 | `./scripts/diagnose.sh backend` |
| `cleanup.sh` | 环境清理工具 | `./scripts/cleanup.sh --medium` |
| `project-manager.sh` | 项目管理工具 | `./scripts/project-manager.sh dev` |

### 专用管理脚本

| 脚本名称 | 功能描述 | 使用示例 |
|---------|----------|----------|
| `docker-start.sh` | Docker服务启动 | `./scripts/docker-start.sh` |
| `db-manager.sh` | 数据库管理 | `./scripts/db-manager.sh backup` |
| `log-manager.sh` | 日志管理 | `./scripts/log-manager.sh view backend` |
| `performance-monitor.sh` | 性能监控 | `./scripts/performance-monitor.sh` |

## 🚀 快速命令参考

### 健康检查
```bash
# 完整健康检查
./scripts/health-check.sh

# 静默模式检查
./scripts/health-check.sh --quiet

# 生成健康报告
./scripts/health-check.sh --report
```

### 故障诊断
```bash
# 诊断所有服务
./scripts/diagnose.sh

# 诊断特定服务
./scripts/diagnose.sh backend

# 网络诊断
./scripts/diagnose.sh --network

# 性能诊断
./scripts/diagnose.sh --performance

# 生成诊断报告
./scripts/diagnose.sh --report
```

### 环境清理
```bash
# 交互式清理菜单
./scripts/cleanup.sh

# 轻度清理（停止容器）
./scripts/cleanup.sh --light

# 中度清理（删除容器和未使用镜像）
./scripts/cleanup.sh --medium

# 深度清理（删除所有项目资源）
./scripts/cleanup.sh --deep

# 系统清理（清理整个Docker系统）
./scripts/cleanup.sh --system

# 备份重要数据
./scripts/cleanup.sh --backup
```

### 项目管理
```bash
# 开发环境启动
./scripts/project-manager.sh dev

# 生产环境启动
./scripts/project-manager.sh prod

# 测试环境启动
./scripts/project-manager.sh test

# 停止所有服务
./scripts/project-manager.sh stop

# 重启所有服务
./scripts/project-manager.sh restart
```

## 🔧 常用Docker命令

### 容器管理
```bash
# 查看容器状态
docker-compose ps

# 查看所有容器（包括停止的）
docker-compose ps -a

# 启动服务
docker-compose up -d [service_name]

# 停止服务
docker-compose stop [service_name]

# 重启服务
docker-compose restart [service_name]

# 删除容器
docker-compose rm [service_name]
```

### 日志查看
```bash
# 查看服务日志
docker-compose logs [service_name]

# 实时跟踪日志
docker-compose logs -f [service_name]

# 查看最近N行日志
docker-compose logs --tail 50 [service_name]

# 查看特定时间段日志
docker-compose logs --since "2024-01-01T00:00:00" [service_name]
```

### 镜像管理
```bash
# 构建镜像
docker-compose build [service_name]

# 强制重新构建
docker-compose build --no-cache [service_name]

# 拉取最新镜像
docker-compose pull [service_name]

# 查看镜像列表
docker images

# 删除未使用的镜像
docker image prune -a
```

### 网络诊断
```bash
# 查看网络列表
docker network ls

# 查看网络详情
docker network inspect [network_name]

# 测试容器间连接
docker exec [container1] ping [container2]

# 查看端口映射
docker port [container_name]
```

### 资源监控
```bash
# 查看容器资源使用
docker stats

# 查看系统资源使用
docker system df

# 查看详细系统信息
docker system info
```

## 🆘 故障排除快速指南

### 常见问题及解决方案

#### 1. 容器无法启动
```bash
# 检查容器状态
docker-compose ps

# 查看错误日志
docker-compose logs [service_name]

# 重新构建镜像
docker-compose build --no-cache [service_name]

# 重新创建容器
docker-compose up -d --force-recreate [service_name]
```

#### 2. 网络连接问题
```bash
# 诊断网络问题
./scripts/diagnose.sh --network

# 重置网络
docker-compose down
docker network prune
docker-compose up -d

# 检查端口占用
lsof -i :[port_number]
```

#### 3. 性能问题
```bash
# 性能监控
./scripts/performance-monitor.sh

# 查看资源使用
docker stats

# 清理系统资源
./scripts/cleanup.sh --medium
```

#### 4. 构建失败
```bash
# 诊断构建问题
./scripts/diagnose.sh --build

# 清理构建缓存
docker builder prune -a

# 使用无代理构建
./scripts/docker-build-no-proxy.sh
```

#### 5. 数据库连接失败
```bash
# 检查数据库状态
./scripts/db-manager.sh status

# 重启数据库
docker-compose restart postgres

# 检查环境变量
docker exec chaiguanjia-backend env | grep DATABASE_URL
```

## 📋 检查清单

### 日常维护检查清单
- [ ] 运行健康检查：`./scripts/health-check.sh`
- [ ] 检查磁盘使用：`docker system df`
- [ ] 查看容器状态：`docker-compose ps`
- [ ] 检查日志错误：`./scripts/log-manager.sh errors`
- [ ] 监控资源使用：`docker stats`

### 问题排查检查清单
- [ ] 查看服务日志：`docker-compose logs [service]`
- [ ] 运行诊断工具：`./scripts/diagnose.sh [service]`
- [ ] 检查网络连接：`./scripts/diagnose.sh --network`
- [ ] 验证环境变量：`docker exec [container] env`
- [ ] 测试端口连通：`telnet localhost [port]`

### 部署前检查清单
- [ ] 备份重要数据：`./scripts/cleanup.sh --backup`
- [ ] 运行完整测试：`./scripts/test-dev-environment.sh`
- [ ] 检查配置文件：`docker-compose config`
- [ ] 验证镜像构建：`docker-compose build`
- [ ] 执行健康检查：`./scripts/health-check.sh`

## 📞 获取帮助

### 工具帮助信息
```bash
# 查看脚本帮助
./scripts/[script_name].sh --help

# 查看Docker命令帮助
docker --help
docker-compose --help
```

### 文档资源
- [Docker避坑指南](./Docker避坑指南.md)
- [Docker网络配置指南](./Docker网络配置指南.md)
- [项目README](../../README.md)

### 联系支持
- 提交Issue：项目GitHub仓库
- 查看日志：`./scripts/log-manager.sh`
- 生成报告：`./scripts/diagnose.sh --report`

---

**提示**：将此文档保存为书签，以便快速查找命令和解决问题！
