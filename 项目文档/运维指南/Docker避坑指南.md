# Docker 避坑指南

> 基于柴管家项目实战经验总结的 Docker 问题诊断与解决方案

## 📋 目录

- [问题分类总结](#问题分类总结)
- [实际案例分析](#实际案例分析)
- [最佳实践指南](#最佳实践指南)
- [故障排查工具箱](#故障排查工具箱)
- [团队协作规范](#团队协作规范)

## 🔍 问题分类总结

### 1. 网络问题

#### 1.1 代理配置问题

**问题描述**：容器构建时无法通过代理访问外部网络

**根本原因**：

- 容器内部的 `127.0.0.1` 指向容器自身，而不是宿主机
- 系统环境变量中的代理设置被 Docker 继承但无法正确解析

**错误信息示例**：

```
Could not connect to 127.0.0.1:7890 (127.0.0.1). - connect (111: Connection refused)
Unable to connect to 127.0.0.1:7890
```

**解决方案**：

```yaml
# docker-compose.yml
services:
  app:
    build:
      context: .
      args:
        - http_proxy=
        - https_proxy=
        - HTTP_PROXY=
        - HTTPS_PROXY=
```

**预防措施**：

- 在 Dockerfile 中显式禁用代理环境变量
- 使用国内镜像源替代代理访问
- 配置 Docker daemon 的代理设置而非容器级别

#### 1.2 镜像拉取缓慢

**问题描述**：从 Docker Hub 拉取镜像速度极慢或超时

**解决方案**：

```json
// ~/.docker/daemon.json
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ]
}
```

### 2. 构建问题

#### 2.1 依赖安装失败

**问题描述**：apt-get update 或 pip install 失败

**解决方案**：

```dockerfile
# 配置国内镜像源
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources

# Python包使用国内源
RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements.txt
```

### 3. 服务启动问题

#### 3.1 模块导入失败

**问题描述**：Celery 等服务无法找到应用模块

**错误信息示例**：

```
Error: Invalid value for '-A' / '--app':
Unable to load celery application.
The module app.core.celery was not found.
```

**解决方案**：

1. 确保模块文件存在
2. 检查 Python 路径配置
3. 验证 `__init__.py` 文件

### 4. 健康检查问题

#### 4.1 健康检查超时

**问题描述**：容器显示 "starting" 状态过久

**解决方案**：

```yaml
healthcheck:
  test:
    [
      "CMD",
      "python",
      "-c",
      "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')",
    ]
  interval: 10s
  timeout: 5s
  retries: 3
  start_period: 30s
```

## 📚 实际案例分析

### 案例 1：Celery 容器构建失败

**问题背景**：柴管家项目中 Celery 相关容器无法构建

**错误日志**：

```
ERROR: failed to solve: process "/bin/sh -c apt-get update && apt-get install -y build-essential libpq-dev curl && rm -rf /var/lib/apt/lists/*" did not complete successfully: exit code: 100
```

**诊断过程**：

1. 检查 Docker daemon 配置：`docker info | grep -i proxy`
2. 测试代理连接：`curl -I http://127.0.0.1:7890`
3. 检查系统环境变量：`env | grep -i proxy`

**解决步骤**：

```bash
# 1. 修改docker-compose.yml添加build-arg
services:
  celery-worker:
    build:
      args:
        - http_proxy=
        - https_proxy=
        - HTTP_PROXY=
        - HTTPS_PROXY=

# 2. 在Dockerfile中配置国内镜像源
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources

# 3. 重新构建
docker-compose build celery-worker
```

**经验总结**：

- 代理配置是容器构建的常见障碍
- build-arg 可以覆盖环境变量
- 国内镜像源是代理的有效替代方案

### 案例 2：Flower 监控界面无法访问

**问题背景**：Celery Flower 监控界面返回认证错误

**错误现象**：

- 浏览器访问 http://localhost:5555 返回空白页面
- curl 请求返回认证失败

**诊断过程**：

```bash
# 1. 检查Flower容器状态
docker-compose ps celery-flower

# 2. 查看Flower日志
docker logs chaiguanjia-celery-flower --tail 20

# 3. 测试无认证访问
curl -s http://localhost:5555/

# 4. 检查环境变量配置
docker exec chaiguanjia-celery-flower env | grep FLOWER
```

**解决方案**：

```bash
# 使用正确的认证信息访问
curl -u admin:flower123 http://localhost:5555/

# 或在浏览器中访问
*************************************
```

### 案例 3：后端健康检查持续失败

**问题背景**：后端容器状态显示 "starting" 而非 "healthy"

**错误日志**：

```
Health check failed: curl: command not found
```

**解决过程**：

```bash
# 1. 检查健康检查配置
docker inspect chaiguanjia-backend | grep -A 10 -B 5 Health

# 2. 修改健康检查命令（从curl改为Python）
healthcheck:
  test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
  interval: 10s
  timeout: 5s
  retries: 3
  start_period: 30s

# 3. 重新构建和启动
docker-compose build backend
docker-compose up -d backend
```

### 案例 4：环境变量配置错误

**问题背景**：数据库连接失败，服务无法启动

**错误信息**：

```
sqlalchemy.exc.OperationalError: (psycopg2.OperationalError) could not connect to server: Connection refused
```

**诊断步骤**：

```bash
# 1. 检查环境变量
docker exec chaiguanjia-backend env | grep DATABASE_URL

# 2. 测试数据库连接
docker exec chaiguanjia-backend python -c "
import os
import psycopg2
url = os.getenv('DATABASE_URL')
print(f'Database URL: {url}')
"

# 3. 验证数据库服务状态
docker-compose ps postgres
```

**解决方案**：

```bash
# 1. 检查.env文件配置
cat .env | grep POSTGRES

# 2. 确保环境变量格式正确
DATABASE_URL=postgresql+asyncpg://chaiguanjia:chaiguanjia123@postgres:5432/chaiguanjia

# 3. 重启相关服务
docker-compose restart backend
```

### 案例 5：容器间网络不通

**问题背景**：后端无法连接到 Redis 和 RabbitMQ

**诊断命令**：

```bash
# 1. 检查网络配置
docker network ls
docker network inspect chaiguanjia_84_chaiguanjia-network

# 2. 测试容器间连接
docker exec chaiguanjia-backend ping chaiguanjia-redis
docker exec chaiguanjia-backend ping chaiguanjia-rabbitmq

# 3. 检查端口开放
docker exec chaiguanjia-backend telnet redis 6379
docker exec chaiguanjia-backend telnet rabbitmq 5672
```

**解决方案**：

```yaml
# 确保所有服务在同一网络中
networks:
  chaiguanjia-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

services:
  backend:
    networks:
      - chaiguanjia-network
  redis:
    networks:
      - chaiguanjia-network
  rabbitmq:
    networks:
      - chaiguanjia-network
```

### 案例 6：Docker 镜像构建缓存问题

**问题背景**：代码更新后容器仍使用旧版本

**解决方案**：

```bash
# 1. 清理构建缓存
docker builder prune -a

# 2. 强制重新构建
docker-compose build --no-cache

# 3. 重新创建容器
docker-compose up -d --force-recreate

# 4. 验证更新
docker exec chaiguanjia-backend python -c "
import app.core.celery
print('Celery module loaded successfully')
"
```

### 案例 7：端口冲突问题

**问题背景**：服务启动失败，端口被占用

**错误信息**：

```
Error starting userland proxy: listen tcp4 0.0.0.0:5432: bind: address already in use
```

**诊断和解决**：

```bash
# 1. 查找占用端口的进程
lsof -i :5432
netstat -tulpn | grep :5432

# 2. 停止冲突的服务
sudo systemctl stop postgresql

# 3. 或修改docker-compose.yml中的端口映射
ports:
  - "15432:5432"  # 使用不同的宿主机端口

# 4. 重新启动服务
docker-compose up -d postgres
```

### 案例 8：文件权限问题

**问题背景**：容器内文件权限错误，服务无法写入日志

**解决方案**：

```dockerfile
# 在Dockerfile中正确设置用户和权限
RUN groupadd -r chaiguanjia && useradd -r -g chaiguanjia chaiguanjia
RUN chown -R chaiguanjia:chaiguanjia /app
USER chaiguanjia
```

```yaml
# 在docker-compose.yml中设置用户
services:
  backend:
    user: "1000:1000" # 使用宿主机用户ID
```

## 🛠 最佳实践指南

### Docker 网络配置

#### 1. 自定义网络

```yaml
networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

#### 2. 服务发现

```yaml
services:
  backend:
    networks:
      app-network:
        ipv4_address: ***********
  database:
    networks:
      app-network:
        ipv4_address: ***********
```

### 代理和镜像源配置

#### 1. 系统级代理配置

```bash
# /etc/systemd/system/docker.service.d/http-proxy.conf
[Service]
Environment="HTTP_PROXY=http://proxy.example.com:8080/"
Environment="HTTPS_PROXY=http://proxy.example.com:8080/"
Environment="NO_PROXY=localhost,127.0.0.1"
```

#### 2. 容器级镜像源配置

```dockerfile
# Debian/Ubuntu
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources

# Alpine
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

# Python
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 容器健康检查配置

#### 1. HTTP 服务健康检查

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
```

#### 2. 数据库健康检查

```yaml
healthcheck:
  test: ["CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"]
  interval: 10s
  timeout: 5s
  retries: 5
```

### docker-compose 服务编排技巧

#### 1. 依赖管理

```yaml
services:
  backend:
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
```

#### 2. 环境变量管理

```yaml
services:
  app:
    env_file:
      - .env
      - .env.local
    environment:
      - NODE_ENV=production
```

#### 3. 卷挂载优化

```yaml
volumes:
  - ./app:/app:cached # macOS性能优化
  - /app/node_modules # 排除node_modules
```

## 🔧 故障排查工具箱

### 常用诊断命令

#### 1. 容器状态检查

```bash
# 查看所有容器状态
docker-compose ps

# 查看特定服务日志
docker-compose logs -f service_name

# 查看容器详细信息
docker inspect container_name

# 进入容器调试
docker exec -it container_name /bin/bash
```

#### 2. 网络诊断

```bash
# 查看Docker网络
docker network ls
docker network inspect network_name

# 测试容器间连接
docker exec container1 ping container2

# 查看端口映射
docker port container_name

# 测试服务可用性
curl -I http://localhost:8000/health
```

#### 3. 资源使用监控

```bash
# 查看容器资源使用
docker stats

# 查看镜像大小
docker images

# 清理未使用资源
docker system prune -a
```

### 日志查看和分析

#### 1. 结构化日志查看

```bash
# 查看最近日志
docker logs --tail 50 container_name

# 实时跟踪日志
docker logs -f container_name

# 查看特定时间段日志
docker logs --since "2024-01-01T00:00:00" container_name

# 过滤错误日志
docker logs container_name 2>&1 | grep -i error
```

#### 2. 日志分析技巧

```bash
# 统计错误数量
docker logs container_name | grep -c "ERROR"

# 查找特定模式
docker logs container_name | grep -E "(ERROR|FATAL|Exception)"

# 导出日志到文件
docker logs container_name > app.log 2>&1
```

### 网络连通性测试

#### 1. 基础连通性测试

```bash
# 测试容器间网络
docker exec container1 ping container2

# 测试外部网络
docker exec container1 curl -I http://www.baidu.com

# 测试DNS解析
docker exec container1 nslookup google.com
```

#### 2. 端口连通性测试

```bash
# 测试端口开放
docker exec container1 telnet container2 5432

# 使用nc测试端口
docker exec container1 nc -zv container2 5432

# 测试HTTP服务
docker exec container1 curl -f http://container2:8000/health
```

## 👥 团队协作规范

### 环境配置标准化流程

#### 1. 环境准备清单

```bash
# 1. 检查Docker版本
docker --version
docker-compose --version

# 2. 配置镜像源
cat ~/.docker/daemon.json

# 3. 复制环境配置
cp .env.example .env

# 4. 验证配置
docker-compose config
```

#### 2. 标准启动流程

```bash
# 1. 拉取最新代码
git pull origin main

# 2. 构建镜像
docker-compose build

# 3. 启动服务
docker-compose up -d

# 4. 验证服务状态
docker-compose ps
./scripts/health-check.sh
```

### 配置文件管理规范

#### 1. 环境变量管理

```
.env.example     # 模板文件，包含所有必需变量
.env             # 本地配置，不提交到版本控制
.env.production  # 生产环境配置
.env.staging     # 测试环境配置
```

#### 2. Docker 配置文件结构

```
docker/
├── Dockerfile.backend
├── Dockerfile.frontend
├── Dockerfile.celery
├── nginx/
│   └── nginx.conf
└── scripts/
    ├── build.sh
    ├── deploy.sh
    └── health-check.sh
```

### 问题报告和解决流程

#### 1. 问题报告模板

```markdown
## 问题描述

简要描述遇到的问题

## 环境信息

- OS: macOS/Linux/Windows
- Docker 版本:
- docker-compose 版本:

## 错误信息
```

完整的错误日志

```

## 复现步骤
1. 步骤一
2. 步骤二
3. 步骤三

## 已尝试的解决方案
列出已经尝试过的方法
```

#### 2. 解决方案文档化

```markdown
## 解决方案

详细的解决步骤

## 根本原因

问题的根本原因分析

## 预防措施

如何避免类似问题再次发生

## 相关资源

- 相关文档链接
- 参考资料
```

## 🚀 自动化脚本工具

### 健康检查脚本

创建 `scripts/health-check.sh`：

```bash
#!/bin/bash
# 健康检查脚本

echo "🔍 Docker服务健康检查"
echo "===================="

# 检查Docker服务状态
echo "📊 容器状态检查:"
docker-compose ps

echo ""
echo "🌐 服务可用性检查:"

# 检查后端API
if curl -s http://localhost:8000/health | grep -q "healthy"; then
    echo "✅ 后端API: 正常"
else
    echo "❌ 后端API: 异常"
fi

# 检查前端服务
if curl -s http://localhost:5173/ | grep -q "<!DOCTYPE html>"; then
    echo "✅ 前端服务: 正常"
else
    echo "❌ 前端服务: 异常"
fi

# 检查Flower监控
if curl -s -u admin:flower123 http://localhost:5555/ | grep -q "Flower"; then
    echo "✅ Flower监控: 正常"
else
    echo "❌ Flower监控: 异常"
fi

# 检查数据库连接
if docker exec chaiguanjia-postgres pg_isready -U chaiguanjia > /dev/null 2>&1; then
    echo "✅ PostgreSQL: 正常"
else
    echo "❌ PostgreSQL: 异常"
fi

# 检查Redis连接
if docker exec chaiguanjia-redis redis-cli ping | grep -q "PONG"; then
    echo "✅ Redis: 正常"
else
    echo "❌ Redis: 异常"
fi

echo ""
echo "🔧 Celery服务检查:"

# 检查Celery Worker
if docker exec chaiguanjia-celery-worker celery -A app.core.celery inspect ping | grep -q "pong"; then
    echo "✅ Celery Worker: 正常"
else
    echo "❌ Celery Worker: 异常"
fi

echo ""
echo "📈 资源使用情况:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
```

### 环境初始化脚本

创建 `scripts/init-env.sh`：

```bash
#!/bin/bash
# 环境初始化脚本

echo "🚀 柴管家项目环境初始化"
echo "======================"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose未安装，请先安装docker-compose"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 复制环境配置文件
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ 已创建.env配置文件"
else
    echo "ℹ️  .env文件已存在"
fi

# 创建必要的目录
mkdir -p logs
mkdir -p data/postgres
mkdir -p data/redis

echo "✅ 目录结构创建完成"

# 构建镜像
echo "🔨 开始构建Docker镜像..."
docker-compose build

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 运行健康检查
echo "🔍 运行健康检查..."
./scripts/health-check.sh

echo ""
echo "🎉 环境初始化完成！"
echo "📱 访问地址："
echo "   前端: http://localhost:5173"
echo "   后端API: http://localhost:8000"
echo "   Flower监控: *************************************"
echo "   RabbitMQ管理: http://localhost:15672"
```

### 故障诊断脚本

创建 `scripts/diagnose.sh`：

```bash
#!/bin/bash
# 故障诊断脚本

echo "🔍 Docker故障诊断工具"
echo "==================="

SERVICE_NAME=${1:-"all"}

diagnose_service() {
    local service=$1
    echo ""
    echo "🔍 诊断服务: $service"
    echo "------------------------"

    # 检查容器状态
    echo "📊 容器状态:"
    docker-compose ps $service

    # 查看最近日志
    echo ""
    echo "📝 最近日志 (最后20行):"
    docker-compose logs --tail 20 $service

    # 检查资源使用
    echo ""
    echo "📈 资源使用:"
    docker stats --no-stream $service 2>/dev/null || echo "容器未运行"

    # 检查网络连接
    if docker-compose ps $service | grep -q "Up"; then
        echo ""
        echo "🌐 网络连接测试:"
        case $service in
            "backend")
                docker exec chaiguanjia-backend ping -c 1 postgres > /dev/null 2>&1 && echo "✅ 数据库连接正常" || echo "❌ 数据库连接失败"
                docker exec chaiguanjia-backend ping -c 1 redis > /dev/null 2>&1 && echo "✅ Redis连接正常" || echo "❌ Redis连接失败"
                ;;
            "celery-worker")
                docker exec chaiguanjia-celery-worker ping -c 1 rabbitmq > /dev/null 2>&1 && echo "✅ RabbitMQ连接正常" || echo "❌ RabbitMQ连接失败"
                ;;
        esac
    fi
}

if [ "$SERVICE_NAME" = "all" ]; then
    # 诊断所有服务
    services=$(docker-compose config --services)
    for service in $services; do
        diagnose_service $service
    done
else
    # 诊断指定服务
    diagnose_service $SERVICE_NAME
fi

echo ""
echo "🔧 常用修复命令:"
echo "  重启服务: docker-compose restart $SERVICE_NAME"
echo "  重新构建: docker-compose build $SERVICE_NAME"
echo "  查看详细日志: docker-compose logs -f $SERVICE_NAME"
echo "  进入容器调试: docker exec -it chaiguanjia-$SERVICE_NAME /bin/bash"
```

## 📖 参考资源

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [Docker 网络配置指南](./Docker网络配置指南.md)
- [柴管家项目 README](../../README.md)
- [Docker 最佳实践](https://docs.docker.com/develop/dev-best-practices/)
- [容器安全指南](https://docs.docker.com/engine/security/)

## 🤝 贡献指南

### 如何添加新的案例

1. 在 `实际案例分析` 部分添加新案例
2. 包含完整的问题描述、诊断过程和解决方案
3. 提供可复现的命令和配置示例
4. 更新相关的最佳实践指南

### 文档更新流程

1. 遇到新问题时，记录完整的解决过程
2. 将解决方案整理成标准格式
3. 更新相关的脚本和工具
4. 提交 PR 并请求代码审查

---

**维护说明**：本文档基于实际项目经验编写，会根据新遇到的问题持续更新。如有问题或建议，请提交 Issue 或 PR。

**最后更新**：2024-08-07
**版本**：v1.0
**维护者**：柴管家开发团队
