# Docker网络配置指南

## 概述

本文档记录了柴管家项目中Docker网络配置的最佳实践和解决方案，确保团队成员能够复现相同的网络环境。

## 网络问题诊断与解决

### 问题描述

在构建Celery相关容器时遇到网络连接问题：
- 错误信息：`无法连接到127.0.0.1:7890代理`
- 根本原因：容器内部的127.0.0.1指向容器自身，而不是宿主机

### 解决方案

#### 1. 代理配置问题

**问题**：Dockerfile中配置的代理地址`127.0.0.1:7890`在容器内部无法访问宿主机代理。

**解决方案**：在docker-compose.yml中使用build-arg禁用代理环境变量：

```yaml
celery-worker:
  build:
    context: ./backend
    dockerfile: Dockerfile.celery
    target: celery-worker
    args:
      - http_proxy=
      - https_proxy=
      - HTTP_PROXY=
      - HTTPS_PROXY=
```

#### 2. 镜像源配置

**优化**：在Dockerfile中配置国内镜像源以提高构建速度：

```dockerfile
# 配置国内镜像源
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources
```

## 网络架构

### 容器网络拓扑

```
┌─────────────────────────────────────────────────────────────┐
│                    chaiguanjia-network                      │
│                     (172.20.0.0/16)                        │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Nginx     │  │   Backend   │  │  Frontend   │         │
│  │ 172.20.0.10 │  │ 172.20.0.20 │  │ 172.20.0.25 │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ PostgreSQL  │  │    Redis    │  │  RabbitMQ   │         │
│  │ 172.20.0.11 │  │ 172.20.0.12 │  │ 172.20.0.13 │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │Celery Worker│  │ Celery Beat │  │Celery Flower│         │
│  │ 172.20.0.30 │  │ 172.20.0.31 │  │ 172.20.0.32 │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 端口映射

| 服务 | 容器端口 | 宿主机端口 | 访问地址 |
|------|----------|------------|----------|
| Nginx | 80 | 80 | http://localhost:80 |
| Backend | 8000 | 8000 | http://localhost:8000 |
| Frontend | 80 | 5173 | http://localhost:5173 |
| Flower | 5555 | 5555 | http://localhost:5555 |
| PostgreSQL | 5432 | 5432 | localhost:5432 |
| Redis | 6379 | 6379 | localhost:6379 |
| RabbitMQ | 5672 | 5672 | localhost:5672 |
| RabbitMQ管理 | 15672 | 15672 | http://localhost:15672 |

## 服务访问认证

### Flower监控界面

- **URL**: http://localhost:5555
- **用户名**: admin
- **密码**: flower123

### RabbitMQ管理界面

- **URL**: http://localhost:15672
- **用户名**: chaiguanjia
- **密码**: rabbitmq123

## 故障排除

### 常见问题

1. **容器构建失败**
   ```bash
   # 检查代理设置
   docker info | grep -i proxy
   
   # 使用build-arg禁用代理
   docker build --build-arg http_proxy= --build-arg https_proxy= .
   ```

2. **容器间网络不通**
   ```bash
   # 检查网络
   docker network ls
   docker network inspect chaiguanjia_84_chaiguanjia-network
   
   # 测试容器间连接
   docker exec container1 ping container2
   ```

3. **服务无法访问**
   ```bash
   # 检查端口映射
   docker port container_name
   
   # 检查服务状态
   docker-compose ps
   ```

## 最佳实践

1. **环境变量管理**：使用.env文件管理敏感信息
2. **网络隔离**：使用自定义网络隔离不同环境
3. **健康检查**：为所有服务配置适当的健康检查
4. **日志管理**：配置统一的日志收集和轮转策略

## 团队协作

### 环境同步

确保团队成员使用相同的配置：

1. 复制`.env.example`到`.env`
2. 根据本地环境调整配置
3. 使用`docker-compose up -d`启动服务
4. 验证所有服务健康状态

### 配置更新

当网络配置发生变化时：

1. 更新相关文档
2. 通知团队成员
3. 提供迁移指南
4. 验证新配置的兼容性
