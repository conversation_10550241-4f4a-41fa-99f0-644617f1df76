# 柴管家项目开发环境 Docker Compose 配置
# 专门用于开发环境的配置覆盖

version: '3.8'

services:
  # ================================
  # 后端开发环境配置
  # ================================
  backend:
    build:
      target: development
    environment:
      DEBUG: "true"
      LOG_LEVEL: DEBUG
      ENABLE_DOCS: "true"
      ENABLE_DEBUG_TOOLBAR: "true"
      ENABLE_RELOAD: "true"
    volumes:
      - ./backend:/app:cached
      - /app/__pycache__
      - backend-cache:/app/.cache
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]

  # ================================
  # 前端开发环境配置
  # ================================
  frontend:
    build:
      target: development
    environment:
      NODE_ENV: development
      VITE_API_BASE_URL: http://localhost:8000
      VITE_APP_ENV: development
      VITE_ENABLE_DEVTOOLS: "true"
    volumes:
      - ./frontend:/app:cached
      - /app/node_modules
      - frontend-cache:/app/.cache
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "5173"]

  # ================================
  # 数据库开发环境配置
  # ================================
  postgres:
    environment:
      POSTGRES_DB: chaiguanjia_dev
      POSTGRES_USER: chaiguanjia_dev
      POSTGRES_PASSWORD: dev123
    ports:
      - "5432:5432"
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./backend/database/dev-seed.sql:/docker-entrypoint-initdb.d/99-seed.sql:ro

  # ================================
  # Redis 开发环境配置
  # ================================
  redis:
    command: redis-server --appendonly yes --requirepass dev123
    environment:
      REDIS_PASSWORD: dev123
    ports:
      - "6379:6379"

  # ================================
  # RabbitMQ 开发环境配置
  # ================================
  rabbitmq:
    environment:
      RABBITMQ_DEFAULT_USER: chaiguanjia_dev
      RABBITMQ_DEFAULT_PASS: dev123
      RABBITMQ_DEFAULT_VHOST: chaiguanjia_dev
    ports:
      - "5672:5672"
      - "15672:15672"

  # ================================
  # Celery Worker 开发环境配置
  # ================================
  celery-worker:
    environment:
      DATABASE_URL: *************************************************/chaiguanjia_dev
      REDIS_URL: redis://:dev123@redis:6379/0
      CELERY_BROKER_URL: amqp://chaiguanjia_dev:dev123@rabbitmq:5672/chaiguanjia_dev
      CELERY_RESULT_BACKEND: redis://:dev123@redis:6379/1
      LOG_LEVEL: DEBUG
    volumes:
      - ./backend:/app:cached
    command: ["celery", "-A", "app.core.celery", "worker", "--loglevel=debug", "--concurrency=2"]

  # ================================
  # Celery Beat 开发环境配置
  # ================================
  celery-beat:
    environment:
      DATABASE_URL: *************************************************/chaiguanjia_dev
      REDIS_URL: redis://:dev123@redis:6379/0
      CELERY_BROKER_URL: amqp://chaiguanjia_dev:dev123@rabbitmq:5672/chaiguanjia_dev
      CELERY_RESULT_BACKEND: redis://:dev123@redis:6379/1
      LOG_LEVEL: DEBUG
    volumes:
      - ./backend:/app:cached

  # ================================
  # Celery Flower 开发环境配置
  # ================================
  celery-flower:
    environment:
      CELERY_BROKER_URL: amqp://chaiguanjia_dev:dev123@rabbitmq:5672/chaiguanjia_dev
      CELERY_RESULT_BACKEND: redis://:dev123@redis:6379/1
      FLOWER_BASIC_AUTH: admin:dev123
    ports:
      - "5555:5555"

  # ================================
  # 开发工具服务
  # ================================
  # 数据库管理工具 (可选)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: chaiguanjia-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    networks:
      - chaiguanjia-network
    depends_on:
      - postgres

  # Redis 管理工具 (可选)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: chaiguanjia-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:1:dev123
    ports:
      - "8081:8081"
    networks:
      - chaiguanjia-network
    depends_on:
      - redis

# ================================
# 开发环境专用数据卷
# ================================
volumes:
  postgres-dev-data:
    driver: local
  backend-cache:
    driver: local
  frontend-cache:
    driver: local
