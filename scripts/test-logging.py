#!/usr/bin/env python3
"""
柴管家项目日志功能测试脚本
验证日志系统的各项功能是否正常工作
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "backend"))

def test_basic_logging():
    """测试基础日志功能"""
    print("🧪 测试基础日志功能...")

    try:
        from app.shared.logging import get_logger, setup_logging

        # 初始化日志系统
        setup_logging()

        # 获取日志器
        logger = get_logger("test.basic")

        # 测试不同级别的日志
        logger.debug("这是调试日志")
        logger.info("这是信息日志")
        logger.warning("这是警告日志")
        logger.error("这是错误日志")

        print("✅ 基础日志功能测试通过")
        return True

    except Exception as e:
        print(f"❌ 基础日志功能测试失败: {e}")
        return False


def test_business_logging():
    """测试业务日志功能"""
    print("🧪 测试业务日志功能...")

    try:
        from app.shared.logging import get_business_logger, EventType, EventStatus

        business_logger = get_business_logger()

        # 测试简单业务事件
        business_logger.log_simple_event(
            event_type=EventType.USER_LOGIN,
            event_name="测试用户登录",
            request_id="test-req-123",
            user_id="test-user-456",
            business_data={"test": True},
            status=EventStatus.SUCCESS
        )

        # 测试事件上下文
        with business_logger.event_context(
            event_type=EventType.MESSAGE_PROCESS,
            event_name="测试消息处理",
            request_id="test-req-123"
        ) as event_id:
            time.sleep(0.1)  # 模拟处理时间

        print("✅ 业务日志功能测试通过")
        return True

    except Exception as e:
        print(f"❌ 业务日志功能测试失败: {e}")
        return False


def test_module_loggers():
    """测试模块日志器"""
    print("🧪 测试模块日志器...")

    try:
        from app.shared.logging import get_module_logger

        # 测试用户管理模块日志
        user_logger = get_module_logger("user_management")
        user_logger.log_user_register(
            user_id="test-user-123",
            email="<EMAIL>",
            request_id="test-req-456",
            success=True
        )

        # 测试消息处理模块日志
        msg_logger = get_module_logger("message_processing")
        msg_logger.log_message_flow(
            operation="receive",
            message_id="test-msg-123",
            channel_id="test-channel-456",
            user_id="test-user-123",
            success=True
        )

        print("✅ 模块日志器测试通过")
        return True

    except Exception as e:
        print(f"❌ 模块日志器测试失败: {e}")
        return False


def test_context_logging():
    """测试上下文日志"""
    print("🧪 测试上下文日志...")

    try:
        from app.shared.logging import get_logger, set_request_context, clear_request_context

        logger = get_logger("test.context")

        # 设置上下文
        set_request_context(
            request_id="test-req-789",
            user_id="test-user-789",
            trace_id="test-trace-789"
        )

        # 记录日志（应该包含上下文信息）
        logger.info("测试上下文日志")

        # 清除上下文
        clear_request_context()

        print("✅ 上下文日志测试通过")
        return True

    except Exception as e:
        print(f"❌ 上下文日志测试失败: {e}")
        return False


def test_log_files():
    """测试日志文件生成"""
    print("🧪 测试日志文件生成...")

    try:
        log_dir = Path("logs")
        app_log = log_dir / "app.log"
        business_log = log_dir / "business.log"

        # 检查日志目录是否存在
        if not log_dir.exists():
            print(f"❌ 日志目录不存在: {log_dir}")
            return False

        # 检查应用日志文件
        if not app_log.exists():
            print(f"❌ 应用日志文件不存在: {app_log}")
            return False

        # 检查业务日志文件
        if not business_log.exists():
            print(f"❌ 业务日志文件不存在: {business_log}")
            return False

        # 检查日志文件内容
        with open(app_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            if not lines:
                print("❌ 应用日志文件为空")
                return False

            # 尝试解析最后一行JSON
            try:
                last_line = lines[-1].strip()
                if last_line:
                    json.loads(last_line)
                    print("✅ 应用日志JSON格式正确")
            except json.JSONDecodeError:
                print("⚠️ 应用日志不是JSON格式（可能是结构化格式）")

        with open(business_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            if lines:
                try:
                    last_line = lines[-1].strip()
                    if last_line:
                        json.loads(last_line)
                        print("✅ 业务日志JSON格式正确")
                except json.JSONDecodeError:
                    print("⚠️ 业务日志格式异常")

        print("✅ 日志文件生成测试通过")
        return True

    except Exception as e:
        print(f"❌ 日志文件生成测试失败: {e}")
        return False


def test_log_tools():
    """测试日志工具"""
    print("🧪 测试日志工具...")

    try:
        # 测试日志查询脚本是否存在
        log_query_script = Path("scripts/log-query.sh")
        if not log_query_script.exists():
            print(f"❌ 日志查询脚本不存在: {log_query_script}")
            return False

        # 测试日志分析脚本是否存在
        log_analyzer_script = Path("scripts/log-analyzer.py")
        if not log_analyzer_script.exists():
            print(f"❌ 日志分析脚本不存在: {log_analyzer_script}")
            return False

        # 检查脚本是否可执行
        if not os.access(log_query_script, os.X_OK):
            print(f"❌ 日志查询脚本不可执行: {log_query_script}")
            return False

        if not os.access(log_analyzer_script, os.X_OK):
            print(f"❌ 日志分析脚本不可执行: {log_analyzer_script}")
            return False

        print("✅ 日志工具测试通过")
        return True

    except Exception as e:
        print(f"❌ 日志工具测试失败: {e}")
        return False


def test_configuration():
    """测试日志配置"""
    print("🧪 测试日志配置...")

    try:
        from app.shared.logging import get_current_config, LogLevel, Environment

        # 获取当前配置
        config = get_current_config()

        # 检查配置属性
        assert hasattr(config, 'level'), "配置缺少level属性"
        assert hasattr(config, 'log_dir'), "配置缺少log_dir属性"
        assert hasattr(config, 'environment'), "配置缺少environment属性"

        # 检查配置值
        assert isinstance(config.level, (LogLevel, str)), "level不是LogLevel或字符串类型"
        assert isinstance(config.environment, (Environment, str)), "environment不是Environment或字符串类型"

        print(f"✅ 日志配置测试通过 - 环境: {config.environment}, 级别: {config.level}")
        return True

    except Exception as e:
        print(f"❌ 日志配置测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始柴管家日志系统功能测试")
    print("=" * 50)

    tests = [
        ("配置测试", test_configuration),
        ("基础日志", test_basic_logging),
        ("业务日志", test_business_logging),
        ("模块日志器", test_module_loggers),
        ("上下文日志", test_context_logging),
        ("日志文件", test_log_files),
        ("日志工具", test_log_tools),
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            failed += 1

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")

    if failed == 0:
        print("🎉 所有测试通过！日志系统功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查日志系统配置")
        return False


def show_log_samples():
    """显示日志样例"""
    print("\n📄 日志文件样例:")

    log_dir = Path("logs")

    # 显示应用日志样例
    app_log = log_dir / "app.log"
    if app_log.exists():
        print(f"\n📁 应用日志 ({app_log}):")
        with open(app_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[-3:]:  # 显示最后3行
                print(f"  {line.strip()}")

    # 显示业务日志样例
    business_log = log_dir / "business.log"
    if business_log.exists():
        print(f"\n📁 业务日志 ({business_log}):")
        with open(business_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[-3:]:  # 显示最后3行
                print(f"  {line.strip()}")


def main():
    """主函数"""
    # 设置环境变量
    os.environ.setdefault("APP_ENV", "development")
    os.environ.setdefault("LOG_LEVEL", "INFO")

    # 运行测试
    success = run_all_tests()

    # 显示日志样例
    show_log_samples()

    # 显示使用提示
    print("\n💡 使用提示:")
    print("1. 查看实时日志: ./scripts/log-query.sh tail")
    print("2. 搜索错误日志: ./scripts/log-query.sh errors")
    print("3. 查看业务日志: ./scripts/log-query.sh business")
    print("4. 生成分析报告: ./scripts/log-query.sh analyze")
    print("5. 查看日志统计: ./scripts/log-query.sh stats")

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
