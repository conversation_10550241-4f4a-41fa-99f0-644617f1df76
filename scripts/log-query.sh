#!/bin/bash

# 柴管家项目日志查询工具
# 提供快速的日志查询和分析功能

set -e

# 默认配置
LOG_DIR="logs"
DEFAULT_LINES=50

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    cat << EOF
柴管家日志查询工具

用法: $0 [选项] [命令]

命令:
  tail [文件]     实时查看日志文件 (默认: app.log)
  search <关键词> 搜索日志内容
  errors          查看错误日志
  business        查看业务日志
  stats           显示日志统计信息
  clean           清理旧日志文件
  analyze         运行详细分析 (需要Python)

选项:
  -d, --dir <目录>    指定日志目录 (默认: logs)
  -n, --lines <数量>  显示行数 (默认: 50)
  -f, --follow        跟踪日志文件变化
  -h, --help          显示此帮助信息

示例:
  $0 tail                    # 实时查看应用日志
  $0 search "ERROR"          # 搜索错误信息
  $0 errors -n 100          # 查看最近100条错误
  $0 business --follow       # 实时查看业务日志
  $0 stats                   # 显示日志统计
  $0 analyze --start-time "2024-01-01 00:00:00"  # 分析指定时间的日志

EOF
}

# 检查日志目录
check_log_dir() {
    if [[ ! -d "$LOG_DIR" ]]; then
        echo -e "${RED}错误: 日志目录不存在: $LOG_DIR${NC}"
        exit 1
    fi
}

# 获取日志文件路径
get_log_file() {
    local file_type="$1"
    case "$file_type" in
        "app"|"application"|"")
            echo "$LOG_DIR/app.log"
            ;;
        "business"|"biz")
            echo "$LOG_DIR/business.log"
            ;;
        "error"|"errors")
            echo "$LOG_DIR/app.log"
            ;;
        *)
            echo "$LOG_DIR/$file_type"
            ;;
    esac
}

# 实时查看日志
tail_logs() {
    local file_type="$1"
    local log_file=$(get_log_file "$file_type")
    
    if [[ ! -f "$log_file" ]]; then
        echo -e "${RED}错误: 日志文件不存在: $log_file${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}正在查看日志文件: $log_file${NC}"
    
    if [[ "$FOLLOW" == "true" ]]; then
        tail -f -n "$DEFAULT_LINES" "$log_file" | while read line; do
            # 根据日志级别着色
            if echo "$line" | grep -q '"level":"ERROR"'; then
                echo -e "${RED}$line${NC}"
            elif echo "$line" | grep -q '"level":"WARNING"'; then
                echo -e "${YELLOW}$line${NC}"
            elif echo "$line" | grep -q '"level":"INFO"'; then
                echo -e "${GREEN}$line${NC}"
            else
                echo "$line"
            fi
        done
    else
        tail -n "$DEFAULT_LINES" "$log_file" | while read line; do
            if echo "$line" | grep -q '"level":"ERROR"'; then
                echo -e "${RED}$line${NC}"
            elif echo "$line" | grep -q '"level":"WARNING"'; then
                echo -e "${YELLOW}$line${NC}"
            elif echo "$line" | grep -q '"level":"INFO"'; then
                echo -e "${GREEN}$line${NC}"
            else
                echo "$line"
            fi
        done
    fi
}

# 搜索日志
search_logs() {
    local keyword="$1"
    local log_file=$(get_log_file "app")
    
    if [[ -z "$keyword" ]]; then
        echo -e "${RED}错误: 请提供搜索关键词${NC}"
        exit 1
    fi
    
    if [[ ! -f "$log_file" ]]; then
        echo -e "${RED}错误: 日志文件不存在: $log_file${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}在 $log_file 中搜索: $keyword${NC}"
    
    grep -i --color=always "$keyword" "$log_file" | tail -n "$DEFAULT_LINES"
}

# 查看错误日志
show_errors() {
    local log_file=$(get_log_file "app")
    
    if [[ ! -f "$log_file" ]]; then
        echo -e "${RED}错误: 日志文件不存在: $log_file${NC}"
        exit 1
    fi
    
    echo -e "${RED}最近的错误日志:${NC}"
    
    grep '"level":"ERROR"' "$log_file" | tail -n "$DEFAULT_LINES" | while read line; do
        echo -e "${RED}$line${NC}"
    done
}

# 查看业务日志
show_business() {
    local log_file=$(get_log_file "business")
    
    if [[ ! -f "$log_file" ]]; then
        echo -e "${YELLOW}警告: 业务日志文件不存在: $log_file${NC}"
        echo "可能业务日志功能尚未启用或没有业务事件发生"
        exit 0
    fi
    
    echo -e "${BLUE}业务日志:${NC}"
    
    if [[ "$FOLLOW" == "true" ]]; then
        tail -f -n "$DEFAULT_LINES" "$log_file"
    else
        tail -n "$DEFAULT_LINES" "$log_file"
    fi
}

# 显示日志统计
show_stats() {
    local app_log=$(get_log_file "app")
    local business_log=$(get_log_file "business")
    
    echo -e "${BLUE}=== 柴管家日志统计 ===${NC}"
    echo
    
    # 应用日志统计
    if [[ -f "$app_log" ]]; then
        echo -e "${GREEN}应用日志 ($app_log):${NC}"
        echo "  总行数: $(wc -l < "$app_log")"
        echo "  文件大小: $(du -h "$app_log" | cut -f1)"
        echo "  最后修改: $(stat -f "%Sm" "$app_log" 2>/dev/null || stat -c "%y" "$app_log" 2>/dev/null || echo "未知")"
        
        # 日志级别统计
        echo "  日志级别分布:"
        for level in DEBUG INFO WARNING ERROR CRITICAL; do
            count=$(grep -c "\"level\":\"$level\"" "$app_log" 2>/dev/null || echo 0)
            echo "    $level: $count"
        done
        echo
    else
        echo -e "${YELLOW}应用日志文件不存在${NC}"
        echo
    fi
    
    # 业务日志统计
    if [[ -f "$business_log" ]]; then
        echo -e "${GREEN}业务日志 ($business_log):${NC}"
        echo "  总行数: $(wc -l < "$business_log")"
        echo "  文件大小: $(du -h "$business_log" | cut -f1)"
        echo "  最后修改: $(stat -f "%Sm" "$business_log" 2>/dev/null || stat -c "%y" "$business_log" 2>/dev/null || echo "未知")"
        echo
    else
        echo -e "${YELLOW}业务日志文件不存在${NC}"
        echo
    fi
    
    # 磁盘使用情况
    echo -e "${GREEN}日志目录磁盘使用:${NC}"
    du -sh "$LOG_DIR"/* 2>/dev/null | sort -hr || echo "  无日志文件"
}

# 清理旧日志
clean_logs() {
    echo -e "${YELLOW}清理旧日志文件...${NC}"
    
    # 查找并删除7天前的日志文件
    find "$LOG_DIR" -name "*.log.*" -mtime +7 -type f -print -delete
    
    # 压缩3天前的日志文件
    find "$LOG_DIR" -name "*.log" -mtime +3 -type f | while read file; do
        if [[ ! -f "$file.gz" ]]; then
            echo "压缩: $file"
            gzip -c "$file" > "$file.gz" && > "$file"
        fi
    done
    
    echo -e "${GREEN}日志清理完成${NC}"
}

# 运行详细分析
run_analysis() {
    local python_script="scripts/log-analyzer.py"
    
    if [[ ! -f "$python_script" ]]; then
        echo -e "${RED}错误: 分析脚本不存在: $python_script${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}运行详细日志分析...${NC}"
    python3 "$python_script" --log-dir "$LOG_DIR" "$@"
}

# 解析命令行参数
FOLLOW="false"
COMMAND=""
ARGS=()

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--dir)
            LOG_DIR="$2"
            shift 2
            ;;
        -n|--lines)
            DEFAULT_LINES="$2"
            shift 2
            ;;
        -f|--follow)
            FOLLOW="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        tail|search|errors|business|stats|clean|analyze)
            COMMAND="$1"
            shift
            ;;
        *)
            ARGS+=("$1")
            shift
            ;;
    esac
done

# 检查日志目录
check_log_dir

# 执行命令
case "$COMMAND" in
    "tail")
        tail_logs "${ARGS[0]}"
        ;;
    "search")
        search_logs "${ARGS[0]}"
        ;;
    "errors")
        show_errors
        ;;
    "business")
        show_business
        ;;
    "stats")
        show_stats
        ;;
    "clean")
        clean_logs
        ;;
    "analyze")
        run_analysis "${ARGS[@]}"
        ;;
    "")
        echo -e "${YELLOW}未指定命令，显示日志统计:${NC}"
        echo
        show_stats
        ;;
    *)
        echo -e "${RED}错误: 未知命令: $COMMAND${NC}"
        echo
        show_help
        exit 1
        ;;
esac
