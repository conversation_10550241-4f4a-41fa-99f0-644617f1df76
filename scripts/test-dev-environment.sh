#!/bin/bash

# 柴管家项目开发环境自动化测试脚本
# 验证一键启动功能的各项指标

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
    ((TOTAL_TESTS++))
}

# 显示帮助信息
show_help() {
    cat << EOF
柴管家项目开发环境测试脚本

用法: $0 [选项]

选项:
    --test TEST     运行特定测试 (startup|health|hot-reload|database|api|performance)
    --timeout SEC   设置超时时间 (默认: 300秒)
    --clean         测试前清理环境
    --help, -h      显示帮助信息

示例:
    $0                              # 运行所有测试
    $0 --test startup               # 只测试启动功能
    $0 --test hot-reload            # 只测试热重载
    $0 --clean                      # 清理环境后运行所有测试

EOF
}

# 等待服务就绪
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=${3:-30}
    local sleep_interval=${4:-5}
    
    log_info "等待 $service_name 服务就绪..."
    
    for i in $(seq 1 $max_attempts); do
        if curl -f -s "$url" > /dev/null 2>&1; then
            log_success "$service_name 服务已就绪"
            return 0
        fi
        
        if [ $i -eq $max_attempts ]; then
            log_error "$service_name 服务启动超时"
            return 1
        fi
        
        echo -n "."
        sleep $sleep_interval
    done
}

# 测试启动功能
test_startup() {
    log_header "测试一键启动功能"
    
    local start_time=$(date +%s)
    
    # 启动开发环境
    log_info "启动开发环境..."
    if ./scripts/docker-start.sh dev -d; then
        log_success "启动命令执行成功"
    else
        log_error "启动命令执行失败"
        return 1
    fi
    
    # 等待服务就绪
    local services=(
        "PostgreSQL:http://localhost:5432:12"
        "Redis:http://localhost:6379:12"
        "Backend:http://localhost:8000/health:20"
        "Frontend:http://localhost:5173:15"
    )
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r name url attempts <<< "$service_info"
        if ! wait_for_service "$name" "$url" "$attempts" 5; then
            log_error "服务启动验证失败"
            return 1
        fi
    done
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $duration -le 150 ]; then  # 2.5分钟
        log_success "启动时间符合要求: ${duration}秒 (≤150秒)"
    else
        log_error "启动时间超出要求: ${duration}秒 (>150秒)"
        return 1
    fi
    
    return 0
}

# 测试健康检查
test_health_checks() {
    log_header "测试服务健康检查"
    
    # 运行健康检查脚本
    if ./scripts/health-check.sh --quiet; then
        log_success "所有服务健康检查通过"
    else
        log_error "服务健康检查失败"
        return 1
    fi
    
    # 测试API健康端点
    local health_response=$(curl -s http://localhost:8000/health)
    if echo "$health_response" | grep -q '"status":"healthy"'; then
        log_success "API健康检查端点正常"
    else
        log_error "API健康检查端点异常"
        echo "响应: $health_response"
        return 1
    fi
    
    return 0
}

# 测试热重载功能
test_hot_reload() {
    log_header "测试热重载功能"
    
    # 测试后端热重载
    log_info "测试后端热重载..."
    
    # 获取当前健康检查响应
    local original_response=$(curl -s http://localhost:8000/health)
    
    # 修改健康检查响应
    local test_marker="test_reload_$(date +%s)"
    local temp_file="/tmp/main_py_backup"
    
    # 备份原文件
    cp backend/app/main.py "$temp_file"
    
    # 修改文件添加测试标记
    sed -i.bak "s/\"version\": \"0.1.0\"/\"version\": \"0.1.0\", \"$test_marker\": \"success\"/" backend/app/main.py
    
    # 等待热重载生效
    sleep 5
    
    # 检查修改是否生效
    local new_response=$(curl -s http://localhost:8000/health)
    if echo "$new_response" | grep -q "$test_marker"; then
        log_success "后端热重载功能正常"
    else
        log_error "后端热重载功能异常"
        echo "原响应: $original_response"
        echo "新响应: $new_response"
        # 恢复原文件
        cp "$temp_file" backend/app/main.py
        return 1
    fi
    
    # 恢复原文件
    cp "$temp_file" backend/app/main.py
    rm -f "$temp_file" backend/app/main.py.bak
    
    # 等待恢复生效
    sleep 3
    
    # 测试前端热重载（简单检查Vite配置）
    log_info "检查前端热重载配置..."
    if grep -q "usePolling: true" frontend/vite.config.ts; then
        log_success "前端热重载配置正确"
    else
        log_error "前端热重载配置缺失"
        return 1
    fi
    
    return 0
}

# 测试数据库功能
test_database() {
    log_header "测试数据库自动化配置"
    
    # 测试数据库连接
    log_info "测试数据库连接..."
    if docker exec chaiguanjia-postgres pg_isready -U chaiguanjia -d chaiguanjia >/dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        return 1
    fi
    
    # 测试表结构
    log_info "检查表结构..."
    local tables=$(docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia -t -c "\dt" 2>/dev/null | wc -l)
    if [ "$tables" -gt 0 ]; then
        log_success "数据库表结构已创建"
    else
        log_error "数据库表结构未创建"
        return 1
    fi
    
    # 测试默认用户
    log_info "检查默认用户数据..."
    local user_count=$(docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia -t -c "SELECT COUNT(*) FROM users WHERE username IN ('admin', 'testuser');" 2>/dev/null | xargs)
    if [ "$user_count" -ge 2 ]; then
        log_success "默认用户数据已创建"
    else
        log_error "默认用户数据缺失"
        return 1
    fi
    
    # 测试CRUD操作
    log_info "测试CRUD操作..."
    local test_user="test_$(date +%s)"
    if docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia -c "INSERT INTO users (username, email, password_hash, full_name) VALUES ('$test_user', '$<EMAIL>', 'hash', 'Test User');" >/dev/null 2>&1; then
        log_success "数据库写入操作正常"
        
        # 清理测试数据
        docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia -c "DELETE FROM users WHERE username = '$test_user';" >/dev/null 2>&1
    else
        log_error "数据库写入操作失败"
        return 1
    fi
    
    return 0
}

# 测试API功能
test_api() {
    log_header "测试API功能"
    
    # 测试根路径
    log_info "测试API根路径..."
    local root_response=$(curl -s http://localhost:8000/)
    if echo "$root_response" | grep -q "柴管家 API"; then
        log_success "API根路径正常"
    else
        log_error "API根路径异常"
        return 1
    fi
    
    # 测试API文档
    log_info "测试API文档..."
    local docs_status=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:8000/docs)
    if [ "$docs_status" = "200" ]; then
        log_success "API文档访问正常"
    else
        log_error "API文档访问失败，状态码: $docs_status"
        return 1
    fi
    
    # 测试CORS配置
    log_info "测试CORS配置..."
    local cors_headers=$(curl -s -H "Origin: http://localhost:5173" -I http://localhost:8000/health | grep -i "access-control")
    if [ -n "$cors_headers" ]; then
        log_success "CORS配置正常"
    else
        log_error "CORS配置缺失"
        return 1
    fi
    
    return 0
}

# 测试性能指标
test_performance() {
    log_header "测试性能指标"
    
    # 测试API响应时间
    log_info "测试API响应时间..."
    local response_time=$(curl -w "%{time_total}" -o /dev/null -s http://localhost:8000/health)
    local response_time_ms=$(echo "$response_time * 1000" | bc)
    
    if (( $(echo "$response_time < 1.0" | bc -l) )); then
        log_success "API响应时间正常: ${response_time_ms}ms (<1000ms)"
    else
        log_error "API响应时间过长: ${response_time_ms}ms (≥1000ms)"
        return 1
    fi
    
    # 测试内存使用
    log_info "检查内存使用..."
    local total_memory=$(docker stats --no-stream --format "{{.MemUsage}}" | grep -o '[0-9.]*MiB' | awk '{sum += $1} END {print sum}')
    if [ -n "$total_memory" ] && (( $(echo "$total_memory < 1000" | bc -l) )); then
        log_success "内存使用正常: ${total_memory}MB (<1000MB)"
    else
        log_warning "内存使用较高: ${total_memory}MB"
    fi
    
    return 0
}

# 清理环境
clean_environment() {
    log_info "清理测试环境..."
    ./scripts/docker-start.sh --clean >/dev/null 2>&1 || true
    sleep 5
}

# 显示测试结果
show_test_results() {
    echo ""
    log_header "测试结果汇总"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过: $PASSED_TESTS"
    echo "失败: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        log_success "🎉 所有测试通过！开发环境一键启动功能正常"
        return 0
    else
        log_error "❌ $FAILED_TESTS 个测试失败"
        return 1
    fi
}

# 主函数
main() {
    local specific_test=""
    local timeout=300
    local clean_first=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --test)
                specific_test="$2"
                shift 2
                ;;
            --timeout)
                timeout="$2"
                shift 2
                ;;
            --clean)
                clean_first=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo -e "${CYAN}"
    echo "🧪 柴管家项目开发环境测试"
    echo "========================================"
    echo -e "${NC}"
    
    # 清理环境（如果需要）
    if [ "$clean_first" = true ]; then
        clean_environment
    fi
    
    # 设置超时
    timeout $timeout bash -c '
        # 运行指定测试或所有测试
        case "'$specific_test'" in
            startup)
                test_startup
                ;;
            health)
                test_health_checks
                ;;
            hot-reload)
                test_hot_reload
                ;;
            database)
                test_database
                ;;
            api)
                test_api
                ;;
            performance)
                test_performance
                ;;
            "")
                # 运行所有测试
                test_startup && \
                test_health_checks && \
                test_hot_reload && \
                test_database && \
                test_api && \
                test_performance
                ;;
            *)
                log_error "未知测试: '$specific_test'"
                exit 1
                ;;
        esac
    ' || {
        log_error "测试执行超时或失败"
        exit 1
    }
    
    # 显示结果
    show_test_results
}

# 执行主函数
main "$@"
