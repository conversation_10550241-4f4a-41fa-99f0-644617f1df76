#!/bin/bash
# Docker环境清理脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 显示磁盘使用情况
show_disk_usage() {
    log_header "当前Docker磁盘使用情况"
    docker system df
    echo ""
}

# 轻度清理
light_cleanup() {
    log_header "执行轻度清理"
    
    log_info "停止所有容器..."
    docker-compose down
    
    log_success "轻度清理完成"
    log_info "容器已停止，镜像和数据保持不变"
}

# 中度清理
medium_cleanup() {
    log_header "执行中度清理"
    
    log_info "停止并删除容器..."
    docker-compose down
    
    log_info "删除未使用的镜像..."
    docker image prune -f
    
    log_info "删除未使用的网络..."
    docker network prune -f
    
    log_success "中度清理完成"
    log_info "容器和未使用的镜像已删除，数据卷保持不变"
}

# 深度清理
deep_cleanup() {
    log_header "执行深度清理"
    
    log_warning "这将删除所有项目相关的Docker资源！"
    echo "包括："
    echo "  - 所有容器"
    echo "  - 项目镜像"
    echo "  - 数据卷"
    echo "  - 网络"
    echo ""
    
    read -p "确认继续？(y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "取消深度清理"
        return 0
    fi
    
    log_info "停止并删除容器..."
    docker-compose down -v --remove-orphans
    
    log_info "删除项目镜像..."
    # 删除所有包含chaiguanjia的镜像
    docker images --format "{{.Repository}}:{{.Tag}} {{.ID}}" | grep chaiguanjia | while read image id; do
        log_info "删除镜像: $image"
        docker rmi -f $id 2>/dev/null || true
    done
    
    log_info "删除数据卷..."
    docker volume prune -f
    
    log_info "删除未使用的网络..."
    docker network prune -f
    
    log_success "深度清理完成"
    log_warning "所有项目数据已删除，下次启动需要重新初始化"
}

# 系统清理
system_cleanup() {
    log_header "执行系统清理"
    
    log_warning "这将清理整个Docker系统！"
    echo "包括："
    echo "  - 所有停止的容器"
    echo "  - 所有未使用的镜像"
    echo "  - 所有未使用的网络"
    echo "  - 所有未使用的数据卷"
    echo "  - 构建缓存"
    echo ""
    
    read -p "确认继续？(y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "取消系统清理"
        return 0
    fi
    
    log_info "清理Docker系统..."
    docker system prune -a --volumes -f
    
    log_info "清理构建缓存..."
    docker builder prune -a -f
    
    log_success "系统清理完成"
    log_warning "Docker系统已完全清理，所有数据已删除"
}

# 选择性清理
selective_cleanup() {
    log_header "选择性清理"
    
    echo "请选择要清理的项目："
    echo "1) 停止的容器"
    echo "2) 悬空镜像"
    echo "3) 未使用的镜像"
    echo "4) 未使用的网络"
    echo "5) 未使用的数据卷"
    echo "6) 构建缓存"
    echo "7) 全部以上项目"
    echo "0) 返回主菜单"
    echo ""
    
    read -p "请选择 (0-7): " choice
    
    case $choice in
        1)
            log_info "删除停止的容器..."
            docker container prune -f
            ;;
        2)
            log_info "删除悬空镜像..."
            docker image prune -f
            ;;
        3)
            log_info "删除未使用的镜像..."
            docker image prune -a -f
            ;;
        4)
            log_info "删除未使用的网络..."
            docker network prune -f
            ;;
        5)
            log_info "删除未使用的数据卷..."
            docker volume prune -f
            ;;
        6)
            log_info "清理构建缓存..."
            docker builder prune -a -f
            ;;
        7)
            log_info "执行全面清理..."
            docker container prune -f
            docker image prune -a -f
            docker network prune -f
            docker volume prune -f
            docker builder prune -a -f
            ;;
        0)
            return 0
            ;;
        *)
            log_error "无效选择"
            return 1
            ;;
    esac
    
    log_success "选择性清理完成"
}

# 清理日志文件
cleanup_logs() {
    log_header "清理日志文件"
    
    # 清理项目日志
    if [ -d "logs" ]; then
        log_info "清理项目日志文件..."
        find logs -name "*.log" -type f -mtime +7 -delete 2>/dev/null || true
        find logs -name "*.log.*" -type f -mtime +7 -delete 2>/dev/null || true
        log_success "项目日志清理完成"
    fi
    
    # 清理Docker日志
    log_info "清理Docker容器日志..."
    docker ps -aq | xargs -r docker inspect --format='{{.LogPath}}' | xargs -r sudo truncate -s 0 2>/dev/null || true
    
    log_success "日志清理完成"
}

# 显示清理统计
show_cleanup_stats() {
    log_header "清理后磁盘使用情况"
    docker system df
    echo ""
    
    log_info "Docker镜像数量: $(docker images -q | wc -l)"
    log_info "Docker容器数量: $(docker ps -aq | wc -l)"
    log_info "Docker网络数量: $(docker network ls -q | wc -l)"
    log_info "Docker数据卷数量: $(docker volume ls -q | wc -l)"
}

# 备份重要数据
backup_data() {
    log_header "备份重要数据"
    
    local backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份环境配置
    if [ -f ".env" ]; then
        cp .env "$backup_dir/"
        log_success "已备份 .env 文件"
    fi
    
    # 备份docker-compose配置
    if [ -f "docker-compose.yml" ]; then
        cp docker-compose.yml "$backup_dir/"
        log_success "已备份 docker-compose.yml 文件"
    fi
    
    # 备份数据库（如果容器正在运行）
    if docker ps --format "{{.Names}}" | grep -q "chaiguanjia-postgres"; then
        log_info "备份数据库..."
        docker exec chaiguanjia-postgres pg_dump -U chaiguanjia chaiguanjia > "$backup_dir/database_backup.sql" 2>/dev/null || true
        if [ -f "$backup_dir/database_backup.sql" ]; then
            log_success "已备份数据库"
        fi
    fi
    
    log_success "数据备份完成，保存在: $backup_dir"
}

# 主菜单
show_menu() {
    echo -e "${CYAN}"
    echo "🧹 Docker环境清理工具"
    echo "========================================"
    echo -e "${NC}"
    
    show_disk_usage
    
    echo "选择清理级别:"
    echo "1) 轻度清理 - 停止容器，保留镜像和数据"
    echo "2) 中度清理 - 删除容器和未使用的镜像"
    echo "3) 深度清理 - 删除所有相关资源（谨慎使用）"
    echo "4) 系统清理 - 清理整个Docker系统"
    echo "5) 选择性清理 - 自定义清理项目"
    echo "6) 清理日志文件"
    echo "7) 备份重要数据"
    echo "8) 显示清理统计"
    echo "0) 退出"
    echo ""
}

# 主函数
main() {
    # 检查Docker是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行或无权限访问"
        exit 1
    fi
    
    # 解析命令行参数
    case "${1:-menu}" in
        --light|-l)
            light_cleanup
            show_cleanup_stats
            ;;
        --medium|-m)
            medium_cleanup
            show_cleanup_stats
            ;;
        --deep|-d)
            deep_cleanup
            show_cleanup_stats
            ;;
        --system|-s)
            system_cleanup
            show_cleanup_stats
            ;;
        --logs)
            cleanup_logs
            ;;
        --backup|-b)
            backup_data
            ;;
        --stats)
            show_cleanup_stats
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --light, -l    轻度清理"
            echo "  --medium, -m   中度清理"
            echo "  --deep, -d     深度清理"
            echo "  --system, -s   系统清理"
            echo "  --logs         清理日志文件"
            echo "  --backup, -b   备份重要数据"
            echo "  --stats        显示清理统计"
            echo "  --help, -h     显示帮助信息"
            echo ""
            echo "交互模式: $0 (无参数)"
            ;;
        menu|*)
            # 交互模式
            while true; do
                show_menu
                read -p "请选择 (0-8): " choice
                
                case $choice in
                    1)
                        light_cleanup
                        show_cleanup_stats
                        ;;
                    2)
                        medium_cleanup
                        show_cleanup_stats
                        ;;
                    3)
                        deep_cleanup
                        show_cleanup_stats
                        ;;
                    4)
                        system_cleanup
                        show_cleanup_stats
                        ;;
                    5)
                        selective_cleanup
                        show_cleanup_stats
                        ;;
                    6)
                        cleanup_logs
                        ;;
                    7)
                        backup_data
                        ;;
                    8)
                        show_cleanup_stats
                        ;;
                    0)
                        log_info "退出清理工具"
                        exit 0
                        ;;
                    *)
                        log_error "无效选择，请重新输入"
                        ;;
                esac
                
                echo ""
                read -p "按回车键继续..."
                clear
            done
            ;;
    esac
}

# 执行主函数
main "$@"
