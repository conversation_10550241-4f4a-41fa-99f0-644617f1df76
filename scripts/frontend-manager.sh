#!/bin/bash

# 柴管家项目 - 前端管理脚本
# 用于管理前端开发、构建、测试等操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 检查Node.js环境
check_node_env() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 18+"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2)
    local major_version=$(echo $node_version | cut -d'.' -f1)
    
    if [ "$major_version" -lt 18 ]; then
        log_error "Node.js版本过低，需要18+，当前版本: $node_version"
        exit 1
    fi
    
    log_success "Node.js环境检查通过: v$node_version"
}

# 检查依赖
check_dependencies() {
    log_info "检查前端依赖..."
    
    if [ ! -d "frontend/node_modules" ]; then
        log_warning "依赖未安装，正在安装..."
        install_dependencies
    else
        log_success "依赖已安装"
    fi
}

# 安装依赖
install_dependencies() {
    log_header "安装前端依赖"
    
    cd frontend
    
    # 使用国内镜像源
    if command -v npm &> /dev/null; then
        log_info "使用npm安装依赖..."
        npm config set registry https://registry.npmmirror.com
        npm install
    elif command -v yarn &> /dev/null; then
        log_info "使用yarn安装依赖..."
        yarn config set registry https://registry.npmmirror.com
        yarn install
    else
        log_error "未找到npm或yarn"
        exit 1
    fi
    
    cd ..
    log_success "依赖安装完成"
}

# 启动开发服务器
start_dev_server() {
    log_header "启动前端开发服务器"
    
    check_node_env
    check_dependencies
    
    cd frontend
    
    log_info "启动Vite开发服务器..."
    log_info "访问地址: http://localhost:5173"
    log_info "按 Ctrl+C 停止服务器"
    
    npm run dev
}

# 构建生产版本
build_production() {
    log_header "构建生产版本"
    
    check_node_env
    check_dependencies
    
    cd frontend
    
    log_info "执行TypeScript类型检查..."
    npm run type-check
    
    log_info "执行代码检查..."
    npm run lint
    
    log_info "构建生产版本..."
    npm run build
    
    cd ..
    log_success "生产版本构建完成: frontend/dist/"
}

# 运行测试
run_tests() {
    local watch_mode=${1:-false}
    
    log_header "运行前端测试"
    
    check_node_env
    check_dependencies
    
    cd frontend
    
    if [ "$watch_mode" = "true" ]; then
        log_info "启动测试监听模式..."
        npm run test:watch
    else
        log_info "运行测试套件..."
        npm run test
    fi
    
    cd ..
}

# 运行测试覆盖率
run_test_coverage() {
    log_header "运行测试覆盖率"
    
    check_node_env
    check_dependencies
    
    cd frontend
    
    log_info "生成测试覆盖率报告..."
    npm run test:coverage
    
    cd ..
    log_success "测试覆盖率报告生成完成: frontend/coverage/"
}

# 代码格式化
format_code() {
    log_header "格式化前端代码"
    
    check_node_env
    check_dependencies
    
    cd frontend
    
    log_info "格式化代码..."
    npm run format
    
    log_info "修复ESLint问题..."
    npm run lint:fix
    
    cd ..
    log_success "代码格式化完成"
}

# 预览生产构建
preview_build() {
    log_header "预览生产构建"
    
    check_node_env
    
    if [ ! -d "frontend/dist" ]; then
        log_warning "生产构建不存在，正在构建..."
        build_production
    fi
    
    cd frontend
    
    log_info "启动预览服务器..."
    log_info "访问地址: http://localhost:4173"
    log_info "按 Ctrl+C 停止服务器"
    
    npm run preview
}

# 清理构建文件
clean_build() {
    log_header "清理构建文件"
    
    if [ -d "frontend/dist" ]; then
        rm -rf frontend/dist
        log_success "已清理 frontend/dist/"
    fi
    
    if [ -d "frontend/coverage" ]; then
        rm -rf frontend/coverage
        log_success "已清理 frontend/coverage/"
    fi
    
    if [ -d "frontend/node_modules" ]; then
        rm -rf frontend/node_modules
        log_success "已清理 frontend/node_modules/"
    fi
}

# 显示帮助信息
show_help() {
    echo "柴管家前端管理脚本"
    echo ""
    echo "用法: $0 <command> [options]"
    echo ""
    echo "命令:"
    echo "  dev              启动开发服务器"
    echo "  build            构建生产版本"
    echo "  preview          预览生产构建"
    echo "  test             运行测试"
    echo "  test:watch       运行测试(监听模式)"
    echo "  test:coverage    运行测试覆盖率"
    echo "  format           格式化代码"
    echo "  install          安装依赖"
    echo "  clean            清理构建文件"
    echo "  help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev           # 启动开发服务器"
    echo "  $0 build         # 构建生产版本"
    echo "  $0 test          # 运行测试"
}

# 主函数
main() {
    local command=${1:-help}
    
    case $command in
        dev)
            start_dev_server
            ;;
        build)
            build_production
            ;;
        preview)
            preview_build
            ;;
        test)
            run_tests false
            ;;
        test:watch)
            run_tests true
            ;;
        test:coverage)
            run_test_coverage
            ;;
        format)
            format_code
            ;;
        install)
            install_dependencies
            ;;
        clean)
            clean_build
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
