#!/usr/bin/env python3
"""
柴管家项目错误统计分析工具
提供错误频率、类型分布、时间趋势等分析功能
"""

import argparse
import json
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter

# 添加项目路径到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "backend"))

try:
    from app.shared.monitoring.error_tracker import get_error_tracker, ErrorRecord
    from app.shared.monitoring.error_monitor import ErrorCategory, ErrorSeverity
except ImportError as e:
    print(f"错误：无法导入监控模块: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)


class ErrorAnalyzer:
    """错误分析器"""
    
    def __init__(self):
        self.error_tracker = get_error_tracker()
    
    def analyze_error_trends(self, 
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None,
                           interval_hours: int = 1) -> Dict[str, Any]:
        """分析错误趋势"""
        
        if not start_time:
            start_time = datetime.now() - timedelta(days=7)
        if not end_time:
            end_time = datetime.now()
        
        # 获取错误记录
        errors = self.error_tracker.search_errors(
            start_time=start_time,
            end_time=end_time,
            limit=10000
        )
        
        # 按时间间隔分组
        time_buckets = defaultdict(int)
        interval_delta = timedelta(hours=interval_hours)
        
        current_time = start_time
        while current_time <= end_time:
            time_buckets[current_time.isoformat()] = 0
            current_time += interval_delta
        
        # 统计每个时间段的错误数量
        for error in errors:
            # 找到对应的时间桶
            bucket_time = start_time
            while bucket_time <= error.timestamp:
                bucket_time += interval_delta
            bucket_time -= interval_delta
            
            if bucket_time >= start_time:
                time_buckets[bucket_time.isoformat()] += 1
        
        return {
            'time_series': [
                {'timestamp': timestamp, 'count': count}
                for timestamp, count in sorted(time_buckets.items())
            ],
            'total_errors': len(errors),
            'time_range': {
                'start': start_time.isoformat(),
                'end': end_time.isoformat(),
                'interval_hours': interval_hours
            }
        }
    
    def analyze_error_patterns(self,
                             start_time: Optional[datetime] = None,
                             end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """分析错误模式"""
        
        if not start_time:
            start_time = datetime.now() - timedelta(days=7)
        if not end_time:
            end_time = datetime.now()
        
        errors = self.error_tracker.search_errors(
            start_time=start_time,
            end_time=end_time,
            limit=10000
        )
        
        # 分析模式
        category_counts = Counter()
        severity_counts = Counter()
        type_counts = Counter()
        module_counts = Counter()
        function_counts = Counter()
        user_counts = Counter()
        hourly_counts = defaultdict(int)
        daily_counts = defaultdict(int)
        
        for error in errors:
            category_counts[error.category] += 1
            severity_counts[error.severity] += 1
            type_counts[error.error_type] += 1
            
            if error.module:
                module_counts[error.module] += 1
            if error.function:
                function_counts[error.function] += 1
            if error.user_id:
                user_counts[error.user_id] += 1
            
            # 时间模式
            hour = error.timestamp.hour
            hourly_counts[hour] += 1
            
            date = error.timestamp.date().isoformat()
            daily_counts[date] += 1
        
        return {
            'category_distribution': [
                {'category': cat, 'count': count, 'percentage': count/len(errors)*100}
                for cat, count in category_counts.most_common()
            ],
            'severity_distribution': [
                {'severity': sev, 'count': count, 'percentage': count/len(errors)*100}
                for sev, count in severity_counts.most_common()
            ],
            'top_error_types': [
                {'error_type': etype, 'count': count}
                for etype, count in type_counts.most_common(10)
            ],
            'top_modules': [
                {'module': module, 'count': count}
                for module, count in module_counts.most_common(10)
            ],
            'top_functions': [
                {'function': func, 'count': count}
                for func, count in function_counts.most_common(10)
            ],
            'top_users': [
                {'user_id': user, 'count': count}
                for user, count in user_counts.most_common(10)
            ],
            'hourly_pattern': [
                {'hour': hour, 'count': hourly_counts[hour]}
                for hour in range(24)
            ],
            'daily_pattern': [
                {'date': date, 'count': count}
                for date, count in sorted(daily_counts.items())
            ],
            'total_errors': len(errors),
            'unique_error_types': len(type_counts),
            'unique_modules': len(module_counts),
            'unique_users': len(user_counts),
        }
    
    def analyze_critical_errors(self,
                               start_time: Optional[datetime] = None,
                               end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """分析严重错误"""
        
        if not start_time:
            start_time = datetime.now() - timedelta(days=7)
        if not end_time:
            end_time = datetime.now()
        
        # 获取严重错误
        critical_errors = self.error_tracker.search_errors(
            severity=ErrorSeverity.CRITICAL.value,
            start_time=start_time,
            end_time=end_time,
            limit=1000
        )
        
        high_errors = self.error_tracker.search_errors(
            severity=ErrorSeverity.HIGH.value,
            start_time=start_time,
            end_time=end_time,
            limit=1000
        )
        
        # 分析未处理的错误
        unhandled_critical = [e for e in critical_errors if not e.is_handled]
        unhandled_high = [e for e in high_errors if not e.is_handled]
        
        # 分析错误频率
        critical_types = Counter(e.error_type for e in critical_errors)
        high_types = Counter(e.error_type for e in high_errors)
        
        return {
            'critical_errors': {
                'total': len(critical_errors),
                'unhandled': len(unhandled_critical),
                'handled_percentage': (len(critical_errors) - len(unhandled_critical)) / len(critical_errors) * 100 if critical_errors else 0,
                'top_types': [
                    {'error_type': etype, 'count': count}
                    for etype, count in critical_types.most_common(5)
                ],
                'recent_unhandled': [
                    {
                        'error_id': e.error_id,
                        'timestamp': e.timestamp.isoformat(),
                        'error_type': e.error_type,
                        'error_message': e.error_message[:100] + '...' if len(e.error_message) > 100 else e.error_message,
                        'module': e.module,
                        'function': e.function,
                    }
                    for e in sorted(unhandled_critical, key=lambda x: x.timestamp, reverse=True)[:5]
                ]
            },
            'high_errors': {
                'total': len(high_errors),
                'unhandled': len(unhandled_high),
                'handled_percentage': (len(high_errors) - len(unhandled_high)) / len(high_errors) * 100 if high_errors else 0,
                'top_types': [
                    {'error_type': etype, 'count': count}
                    for etype, count in high_types.most_common(5)
                ]
            },
            'recommendations': self._generate_recommendations(critical_errors + high_errors)
        }
    
    def analyze_user_errors(self,
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """分析用户相关错误"""
        
        if not start_time:
            start_time = datetime.now() - timedelta(days=7)
        if not end_time:
            end_time = datetime.now()
        
        errors = self.error_tracker.search_errors(
            start_time=start_time,
            end_time=end_time,
            limit=10000
        )
        
        # 按用户分组
        user_errors = defaultdict(list)
        for error in errors:
            if error.user_id:
                user_errors[error.user_id].append(error)
        
        # 分析用户错误模式
        user_stats = []
        for user_id, user_error_list in user_errors.items():
            error_types = Counter(e.error_type for e in user_error_list)
            categories = Counter(e.category for e in user_error_list)
            
            user_stats.append({
                'user_id': user_id,
                'total_errors': len(user_error_list),
                'unique_error_types': len(error_types),
                'most_common_error': error_types.most_common(1)[0] if error_types else None,
                'most_common_category': categories.most_common(1)[0] if categories else None,
                'first_error': min(user_error_list, key=lambda x: x.timestamp).timestamp.isoformat(),
                'last_error': max(user_error_list, key=lambda x: x.timestamp).timestamp.isoformat(),
            })
        
        # 排序用户（按错误数量）
        user_stats.sort(key=lambda x: x['total_errors'], reverse=True)
        
        return {
            'total_users_with_errors': len(user_stats),
            'top_error_users': user_stats[:10],
            'error_distribution': {
                'users_with_1_error': len([u for u in user_stats if u['total_errors'] == 1]),
                'users_with_2_5_errors': len([u for u in user_stats if 2 <= u['total_errors'] <= 5]),
                'users_with_6_10_errors': len([u for u in user_stats if 6 <= u['total_errors'] <= 10]),
                'users_with_more_than_10_errors': len([u for u in user_stats if u['total_errors'] > 10]),
            }
        }
    
    def _generate_recommendations(self, errors: List[ErrorRecord]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if not errors:
            return recommendations
        
        # 分析错误类型
        error_types = Counter(e.error_type for e in errors)
        categories = Counter(e.category for e in errors)
        
        # 基于错误类型的建议
        if error_types.get('ConnectionError', 0) > 5:
            recommendations.append("检测到大量连接错误，建议检查网络配置和外部服务可用性")
        
        if error_types.get('TimeoutError', 0) > 3:
            recommendations.append("检测到超时错误，建议优化API响应时间或增加超时配置")
        
        if error_types.get('ValidationError', 0) > 10:
            recommendations.append("检测到大量验证错误，建议改进前端输入验证")
        
        # 基于错误分类的建议
        if categories.get('database_error', 0) > 5:
            recommendations.append("检测到数据库错误，建议检查数据库性能和连接池配置")
        
        if categories.get('external_api_error', 0) > 3:
            recommendations.append("检测到外部API错误，建议实现重试机制和降级策略")
        
        # 基于未处理错误的建议
        unhandled_count = len([e for e in errors if not e.is_handled])
        if unhandled_count > len(errors) * 0.5:
            recommendations.append(f"有{unhandled_count}个错误未处理，建议及时处理严重错误")
        
        return recommendations
    
    def generate_report(self,
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None) -> str:
        """生成错误分析报告"""
        
        if not start_time:
            start_time = datetime.now() - timedelta(days=7)
        if not end_time:
            end_time = datetime.now()
        
        print(f"正在分析 {start_time} 到 {end_time} 的错误数据...")
        
        # 获取各种分析结果
        patterns = self.analyze_error_patterns(start_time, end_time)
        critical = self.analyze_critical_errors(start_time, end_time)
        trends = self.analyze_error_trends(start_time, end_time, interval_hours=6)
        user_errors = self.analyze_user_errors(start_time, end_time)
        
        # 生成报告
        report = f"""
# 柴管家错误分析报告

**分析时间范围**: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}
**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 总体概况

- **总错误数**: {patterns['total_errors']}
- **唯一错误类型**: {patterns['unique_error_types']}
- **涉及模块数**: {patterns['unique_modules']}
- **影响用户数**: {user_errors['total_users_with_errors']}

## 🚨 严重错误分析

### 严重级别错误
- **总数**: {critical['critical_errors']['total']}
- **未处理**: {critical['critical_errors']['unhandled']}
- **处理率**: {critical['critical_errors']['handled_percentage']:.1f}%

### 高级别错误
- **总数**: {critical['high_errors']['total']}
- **未处理**: {critical['high_errors']['unhandled']}
- **处理率**: {critical['high_errors']['handled_percentage']:.1f}%

## 📈 错误分布

### 按分类分布
{self._format_distribution(patterns['category_distribution'])}

### 按严重程度分布
{self._format_distribution(patterns['severity_distribution'])}

### 最常见错误类型
{self._format_top_items(patterns['top_error_types'], 'error_type')}

## 👥 用户错误分析

- **有错误的用户数**: {user_errors['total_users_with_errors']}
- **单次错误用户**: {user_errors['error_distribution']['users_with_1_error']}
- **2-5次错误用户**: {user_errors['error_distribution']['users_with_2_5_errors']}
- **6-10次错误用户**: {user_errors['error_distribution']['users_with_6_10_errors']}
- **10次以上错误用户**: {user_errors['error_distribution']['users_with_more_than_10_errors']}

## 🔧 改进建议

{self._format_recommendations(critical['recommendations'])}

## 📋 需要关注的未处理严重错误

{self._format_unhandled_errors(critical['critical_errors']['recent_unhandled'])}

---
*报告由柴管家错误分析工具生成*
"""
        
        return report
    
    def _format_distribution(self, distribution: List[Dict]) -> str:
        """格式化分布数据"""
        lines = []
        for item in distribution:
            name = list(item.keys())[0]
            count = item['count']
            percentage = item['percentage']
            lines.append(f"  - {item[name]}: {count} ({percentage:.1f}%)")
        return "\n".join(lines)
    
    def _format_top_items(self, items: List[Dict], key: str) -> str:
        """格式化Top项目"""
        lines = []
        for item in items:
            lines.append(f"  - {item[key]}: {item['count']}")
        return "\n".join(lines)
    
    def _format_recommendations(self, recommendations: List[str]) -> str:
        """格式化建议"""
        if not recommendations:
            return "  暂无特殊建议"
        
        lines = []
        for i, rec in enumerate(recommendations, 1):
            lines.append(f"{i}. {rec}")
        return "\n".join(lines)
    
    def _format_unhandled_errors(self, errors: List[Dict]) -> str:
        """格式化未处理错误"""
        if not errors:
            return "  无未处理的严重错误"
        
        lines = []
        for error in errors:
            lines.append(f"- **{error['error_type']}** ({error['timestamp']})")
            lines.append(f"  - 错误ID: {error['error_id']}")
            lines.append(f"  - 位置: {error['module']}:{error['function']}")
            lines.append(f"  - 消息: {error['error_message']}")
            lines.append("")
        
        return "\n".join(lines)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="柴管家错误分析工具")
    parser.add_argument("--command", choices=["report", "trends", "patterns", "critical", "users"], 
                       default="report", help="执行的命令")
    parser.add_argument("--start-time", help="开始时间 (YYYY-MM-DD HH:MM:SS)")
    parser.add_argument("--end-time", help="结束时间 (YYYY-MM-DD HH:MM:SS)")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--format", choices=["text", "json"], default="text", help="输出格式")
    
    args = parser.parse_args()
    
    # 解析时间参数
    start_time = None
    end_time = None
    
    if args.start_time:
        try:
            start_time = datetime.strptime(args.start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            print("开始时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式")
            sys.exit(1)
    
    if args.end_time:
        try:
            end_time = datetime.strptime(args.end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            print("结束时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式")
            sys.exit(1)
    
    # 创建分析器
    analyzer = ErrorAnalyzer()
    
    # 执行命令
    try:
        if args.command == "report":
            result = analyzer.generate_report(start_time, end_time)
        elif args.command == "trends":
            result = analyzer.analyze_error_trends(start_time, end_time)
            if args.format == "text":
                result = f"错误趋势分析:\n{json.dumps(result, indent=2, ensure_ascii=False)}"
        elif args.command == "patterns":
            result = analyzer.analyze_error_patterns(start_time, end_time)
            if args.format == "text":
                result = f"错误模式分析:\n{json.dumps(result, indent=2, ensure_ascii=False)}"
        elif args.command == "critical":
            result = analyzer.analyze_critical_errors(start_time, end_time)
            if args.format == "text":
                result = f"严重错误分析:\n{json.dumps(result, indent=2, ensure_ascii=False)}"
        elif args.command == "users":
            result = analyzer.analyze_user_errors(start_time, end_time)
            if args.format == "text":
                result = f"用户错误分析:\n{json.dumps(result, indent=2, ensure_ascii=False)}"
        
        # 输出结果
        if args.format == "json" and args.command != "report":
            result = json.dumps(result, indent=2, ensure_ascii=False)
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"分析结果已保存到: {args.output}")
        else:
            print(result)
    
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
