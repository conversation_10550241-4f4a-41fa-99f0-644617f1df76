#!/bin/bash

# 柴管家项目代码质量工具链验证脚本
# 验证所有配置的工具是否正常工作

set -e  # 遇到错误立即退出

echo "🔧 柴管家项目代码质量工具链验证"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 成功和失败计数
SUCCESS_COUNT=0
FAIL_COUNT=0

# 检查函数
check_command() {
    local cmd="$1"
    local name="$2"
    local dir="$3"
    
    echo -e "\n${BLUE}检查 $name...${NC}"
    
    if [ -n "$dir" ]; then
        cd "$dir"
    fi
    
    if eval "$cmd" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ $name 可用${NC}"
        ((SUCCESS_COUNT++))
    else
        echo -e "${RED}✗ $name 不可用${NC}"
        ((FAIL_COUNT++))
    fi
    
    if [ -n "$dir" ]; then
        cd - > /dev/null
    fi
}

# 检查文件是否存在
check_file() {
    local file="$1"
    local name="$2"
    
    echo -e "\n${BLUE}检查 $name...${NC}"
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ $name 存在${NC}"
        ((SUCCESS_COUNT++))
    else
        echo -e "${RED}✗ $name 不存在${NC}"
        ((FAIL_COUNT++))
    fi
}

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${YELLOW}项目根目录: $PROJECT_ROOT${NC}"

# ===== 检查配置文件 =====
echo -e "\n${YELLOW}=== 检查配置文件 ===${NC}"

check_file ".editorconfig" "EditorConfig 配置"
check_file ".pre-commit-config.yaml" "Pre-commit 配置"
check_file "Makefile" "Makefile"

# 前端配置文件
check_file "frontend/package.json" "前端 package.json"
check_file "frontend/tsconfig.json" "TypeScript 配置"
check_file "frontend/.eslintrc.js" "ESLint 配置"
check_file "frontend/.prettierrc.js" "Prettier 配置"
check_file "frontend/vite.config.ts" "Vite 配置"
check_file "frontend/jest.config.js" "Jest 配置"

# 后端配置文件
check_file "backend/pyproject.toml" "Python 项目配置"
check_file "backend/.flake8" "Flake8 配置"
check_file "backend/requirements.txt" "Python 依赖"
check_file "backend/requirements-dev.txt" "Python 开发依赖"

# IDE 配置文件
check_file ".vscode/settings.json" "VSCode 设置"
check_file ".vscode/extensions.json" "VSCode 扩展推荐"
check_file ".vscode/tasks.json" "VSCode 任务配置"
check_file ".vscode/launch.json" "VSCode 启动配置"
check_file ".idea/codeStyles/Project.xml" "PyCharm 代码风格"

# ===== 检查系统工具 =====
echo -e "\n${YELLOW}=== 检查系统工具 ===${NC}"

check_command "python3 --version" "Python 3"
check_command "node --version" "Node.js"
check_command "npm --version" "npm"
check_command "git --version" "Git"
check_command "docker --version" "Docker"
check_command "make --version" "Make"

# ===== 检查 Python 工具 =====
echo -e "\n${YELLOW}=== 检查 Python 工具 ===${NC}"

# 检查是否可以导入工具（不需要安装）
check_command "python3 -c 'import black'" "Black (需要安装)"
check_command "python3 -c 'import isort'" "isort (需要安装)"
check_command "python3 -c 'import flake8'" "Flake8 (需要安装)"
check_command "python3 -c 'import mypy'" "MyPy (需要安装)"
check_command "python3 -c 'import pytest'" "Pytest (需要安装)"

# ===== 检查前端工具 =====
echo -e "\n${YELLOW}=== 检查前端工具 ===${NC}"

# 检查前端目录是否有 node_modules（表示已安装依赖）
if [ -d "frontend/node_modules" ]; then
    echo -e "${GREEN}✓ 前端依赖已安装${NC}"
    ((SUCCESS_COUNT++))
    
    # 检查前端工具
    check_command "npm run lint --silent" "ESLint" "frontend"
    check_command "npm run type-check --silent" "TypeScript 编译" "frontend"
    check_command "npm run format:check --silent" "Prettier 检查" "frontend"
else
    echo -e "${YELLOW}⚠ 前端依赖未安装，跳过前端工具检查${NC}"
    echo -e "${BLUE}运行 'cd frontend && npm install' 安装依赖${NC}"
fi

# ===== 检查 Git hooks =====
echo -e "\n${YELLOW}=== 检查 Git hooks ===${NC}"

if [ -f ".git/hooks/pre-commit" ]; then
    echo -e "${GREEN}✓ Pre-commit hook 已安装${NC}"
    ((SUCCESS_COUNT++))
else
    echo -e "${YELLOW}⚠ Pre-commit hook 未安装${NC}"
    echo -e "${BLUE}运行 'pre-commit install' 安装 hooks${NC}"
fi

# ===== 检查示例文件语法 =====
echo -e "\n${YELLOW}=== 检查示例文件语法 ===${NC}"

# 检查 Python 文件语法
if [ -f "backend/app/main.py" ]; then
    check_command "python3 -m py_compile backend/app/main.py" "Python 文件语法"
fi

# 检查 TypeScript 文件语法（如果有 tsc）
if command -v tsc > /dev/null 2>&1 && [ -f "frontend/src/App.tsx" ]; then
    check_command "cd frontend && tsc --noEmit --skipLibCheck" "TypeScript 文件语法"
fi

# ===== 总结 =====
echo -e "\n${YELLOW}=== 验证总结 ===${NC}"
echo -e "成功: ${GREEN}$SUCCESS_COUNT${NC}"
echo -e "失败: ${RED}$FAIL_COUNT${NC}"

if [ $FAIL_COUNT -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有检查都通过了！代码质量工具链配置正确。${NC}"
    echo -e "\n${BLUE}下一步操作：${NC}"
    echo -e "1. 安装前端依赖: ${YELLOW}cd frontend && npm install${NC}"
    echo -e "2. 安装后端依赖: ${YELLOW}cd backend && pip install -r requirements-dev.txt${NC}"
    echo -e "3. 安装 pre-commit: ${YELLOW}pip install pre-commit && pre-commit install${NC}"
    echo -e "4. 运行完整检查: ${YELLOW}make check-all${NC}"
    exit 0
else
    echo -e "\n${RED}❌ 有 $FAIL_COUNT 个检查失败。请检查上述错误并修复。${NC}"
    exit 1
fi
