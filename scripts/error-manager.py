#!/usr/bin/env python3
"""
柴管家项目错误管理工具
提供错误查看、状态更新、工作流管理等功能
"""

import argparse
import json
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "backend"))

try:
    from app.shared.monitoring.error_tracker import get_error_tracker
    from app.shared.monitoring.error_workflow import get_error_workflow, ErrorStatus, ErrorPriority, ResolutionType
    from app.shared.monitoring.error_monitor import ErrorCategory, ErrorSeverity
except ImportError as e:
    print(f"错误：无法导入监控模块: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)


class ErrorManager:
    """错误管理器"""
    
    def __init__(self):
        self.error_tracker = get_error_tracker()
        self.error_workflow = get_error_workflow()
    
    def list_errors(self, 
                   category: Optional[str] = None,
                   severity: Optional[str] = None,
                   status: Optional[str] = None,
                   assignee: Optional[str] = None,
                   days: int = 7,
                   limit: int = 50) -> List[Dict[str, Any]]:
        """列出错误"""
        
        start_time = datetime.now() - timedelta(days=days)
        
        # 搜索错误
        errors = self.error_tracker.search_errors(
            category=category,
            severity=severity,
            start_time=start_time,
            is_handled=False if status == "pending" else None,
            limit=limit
        )
        
        result = []
        for error in errors:
            error_info = error.to_error_info()
            rule = self.error_workflow._get_workflow_rule(error_info)
            
            # 过滤条件
            if assignee and rule.assignee != assignee:
                continue
            
            # 计算年龄和是否超期
            age_hours = (datetime.now() - error.created_at.replace(tzinfo=None)).total_seconds() / 3600
            escalation_deadline = self.error_workflow._calculate_escalation_deadline(rule, error.created_at)
            is_overdue = escalation_deadline and datetime.now() > escalation_deadline.replace(tzinfo=None)
            
            result.append({
                'error_id': error.error_id,
                'timestamp': error.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'category': error.category,
                'severity': error.severity,
                'error_type': error.error_type,
                'error_message': error.error_message[:100] + '...' if len(error.error_message) > 100 else error.error_message,
                'priority': rule.priority.value,
                'assignee': rule.assignee or 'unassigned',
                'status': error.resolution_status or 'new',
                'is_handled': error.is_handled,
                'age_hours': round(age_hours, 1),
                'is_overdue': bool(is_overdue),
                'module': error.module,
                'function': error.function,
            })
        
        return result
    
    def show_error_details(self, error_id: str) -> Optional[Dict[str, Any]]:
        """显示错误详情"""
        
        error = self.error_tracker.get_error_by_id(error_id)
        if not error:
            return None
        
        error_info = error.to_error_info()
        rule = self.error_workflow._get_workflow_rule(error_info)
        
        return {
            'basic_info': {
                'error_id': error.error_id,
                'timestamp': error.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'category': error.category,
                'severity': error.severity,
                'error_type': error.error_type,
                'error_message': error.error_message,
                'error_code': error.error_code,
            },
            'context': {
                'request_id': error.request_id,
                'user_id': error.user_id,
                'trace_id': error.trace_id,
                'module': error.module,
                'function': error.function,
                'line_number': error.line_number,
                'environment': error.environment,
                'version': error.version,
            },
            'workflow': {
                'priority': rule.priority.value,
                'assignee': rule.assignee,
                'status': error.resolution_status or 'new',
                'is_handled': error.is_handled,
                'resolution_notes': error.resolution_notes,
                'escalation_hours': rule.escalation_hours,
            },
            'business_context': json.loads(error.business_context) if error.business_context else {},
            'stack_trace': error.stack_trace,
            'timestamps': {
                'created_at': error.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': error.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            }
        }
    
    def update_error_status(self, 
                           error_id: str,
                           status: str,
                           assignee: Optional[str] = None,
                           resolution_type: Optional[str] = None,
                           notes: Optional[str] = None,
                           updated_by: Optional[str] = None) -> bool:
        """更新错误状态"""
        
        try:
            error_status = ErrorStatus(status)
            resolution_type_enum = ResolutionType(resolution_type) if resolution_type else None
            
            return self.error_workflow.update_error_status(
                error_id=error_id,
                new_status=error_status,
                assignee=assignee,
                resolution_type=resolution_type_enum,
                resolution_notes=notes,
                updated_by=updated_by
            )
        except ValueError as e:
            print(f"无效的状态或解决方案类型: {e}")
            return False
    
    def get_pending_errors(self, 
                          priority: Optional[str] = None,
                          assignee: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取待处理错误"""
        
        priority_enum = ErrorPriority(priority) if priority else None
        
        return self.error_workflow.get_pending_errors(
            priority=priority_enum,
            assignee=assignee
        )
    
    def get_dashboard_summary(self) -> Dict[str, Any]:
        """获取仪表板摘要"""
        
        # 工作流统计
        workflow_stats = self.error_workflow.get_workflow_statistics()
        
        # 错误统计
        error_stats = self.error_tracker.get_error_statistics()
        
        # 待处理错误
        pending_errors = self.get_pending_errors()
        overdue_errors = [e for e in pending_errors if e['is_overdue']]
        
        return {
            'overview': {
                'total_errors': workflow_stats['total_errors'],
                'pending_errors': workflow_stats['pending_errors'],
                'handled_errors': workflow_stats['handled_errors'],
                'overdue_errors': len(overdue_errors),
            },
            'priority_distribution': workflow_stats['priority_distribution'],
            'status_distribution': workflow_stats['status_distribution'],
            'category_distribution': {
                item['category']: item['count'] 
                for item in error_stats['category_distribution']
            },
            'recent_critical_errors': [
                {
                    'error_id': e['error_id'],
                    'timestamp': e['timestamp'],
                    'error_type': e['error_type'],
                    'assignee': e['assignee'],
                }
                for e in pending_errors 
                if e['priority'] in ['immediate', 'urgent']
            ][:5],
            'overdue_summary': {
                'count': len(overdue_errors),
                'by_priority': {
                    priority: len([e for e in overdue_errors if e['priority'] == priority])
                    for priority in ['immediate', 'urgent', 'high', 'medium', 'low']
                }
            }
        }


def format_error_list(errors: List[Dict[str, Any]]) -> str:
    """格式化错误列表"""
    if not errors:
        return "没有找到错误记录"
    
    lines = []
    lines.append(f"{'ID':<8} {'时间':<16} {'类型':<15} {'严重程度':<8} {'优先级':<8} {'负责人':<12} {'状态':<12} {'年龄(h)':<8} {'超期':<4}")
    lines.append("-" * 120)
    
    for error in errors:
        lines.append(
            f"{error['error_id'][:8]:<8} "
            f"{error['timestamp'][-16:]:<16} "
            f"{error['error_type'][:14]:<15} "
            f"{error['severity']:<8} "
            f"{error['priority']:<8} "
            f"{error['assignee'][:11]:<12} "
            f"{error['status']:<12} "
            f"{error['age_hours']:<8} "
            f"{'是' if error['is_overdue'] else '否':<4}"
        )
    
    return "\n".join(lines)


def format_error_details(details: Dict[str, Any]) -> str:
    """格式化错误详情"""
    lines = []
    
    # 基本信息
    lines.append("=== 基本信息 ===")
    basic = details['basic_info']
    lines.append(f"错误ID: {basic['error_id']}")
    lines.append(f"时间: {basic['timestamp']}")
    lines.append(f"分类: {basic['category']}")
    lines.append(f"严重程度: {basic['severity']}")
    lines.append(f"错误类型: {basic['error_type']}")
    lines.append(f"错误消息: {basic['error_message']}")
    if basic['error_code']:
        lines.append(f"错误代码: {basic['error_code']}")
    
    # 上下文信息
    lines.append("\n=== 上下文信息 ===")
    context = details['context']
    if context['request_id']:
        lines.append(f"请求ID: {context['request_id']}")
    if context['user_id']:
        lines.append(f"用户ID: {context['user_id']}")
    if context['trace_id']:
        lines.append(f"追踪ID: {context['trace_id']}")
    if context['module']:
        lines.append(f"模块: {context['module']}")
    if context['function']:
        lines.append(f"函数: {context['function']}")
    if context['line_number']:
        lines.append(f"行号: {context['line_number']}")
    
    # 工作流信息
    lines.append("\n=== 工作流信息 ===")
    workflow = details['workflow']
    lines.append(f"优先级: {workflow['priority']}")
    lines.append(f"负责人: {workflow['assignee'] or '未分配'}")
    lines.append(f"状态: {workflow['status']}")
    lines.append(f"是否已处理: {'是' if workflow['is_handled'] else '否'}")
    if workflow['resolution_notes']:
        lines.append(f"处理备注: {workflow['resolution_notes']}")
    
    # 业务上下文
    if details['business_context']:
        lines.append("\n=== 业务上下文 ===")
        for key, value in details['business_context'].items():
            lines.append(f"{key}: {value}")
    
    # 堆栈跟踪
    if details['stack_trace']:
        lines.append("\n=== 堆栈跟踪 ===")
        lines.append(details['stack_trace'])
    
    return "\n".join(lines)


def format_dashboard(summary: Dict[str, Any]) -> str:
    """格式化仪表板"""
    lines = []
    
    lines.append("=== 柴管家错误监控仪表板 ===")
    lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 概览
    overview = summary['overview']
    lines.append("\n📊 总体概览")
    lines.append(f"  总错误数: {overview['total_errors']}")
    lines.append(f"  待处理: {overview['pending_errors']}")
    lines.append(f"  已处理: {overview['handled_errors']}")
    lines.append(f"  超期错误: {overview['overdue_errors']}")
    
    # 优先级分布
    lines.append("\n🚨 优先级分布")
    priority_dist = summary['priority_distribution']
    for priority in ['immediate', 'urgent', 'high', 'medium', 'low']:
        count = priority_dist.get(priority, 0)
        lines.append(f"  {priority}: {count}")
    
    # 状态分布
    lines.append("\n📋 状态分布")
    status_dist = summary['status_distribution']
    for status, count in status_dist.items():
        lines.append(f"  {status}: {count}")
    
    # 最近的严重错误
    if summary['recent_critical_errors']:
        lines.append("\n🔥 最近的严重错误")
        for error in summary['recent_critical_errors']:
            lines.append(f"  - {error['error_id'][:8]} | {error['timestamp']} | {error['error_type']} | {error['assignee']}")
    
    # 超期摘要
    overdue = summary['overdue_summary']
    if overdue['count'] > 0:
        lines.append(f"\n⏰ 超期错误 (总计: {overdue['count']})")
        for priority, count in overdue['by_priority'].items():
            if count > 0:
                lines.append(f"  {priority}: {count}")
    
    return "\n".join(lines)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="柴管家错误管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 列出错误
    list_parser = subparsers.add_parser("list", help="列出错误")
    list_parser.add_argument("--category", help="错误分类")
    list_parser.add_argument("--severity", help="严重程度")
    list_parser.add_argument("--status", help="状态")
    list_parser.add_argument("--assignee", help="负责人")
    list_parser.add_argument("--days", type=int, default=7, help="天数范围")
    list_parser.add_argument("--limit", type=int, default=50, help="结果限制")
    
    # 显示错误详情
    show_parser = subparsers.add_parser("show", help="显示错误详情")
    show_parser.add_argument("error_id", help="错误ID")
    
    # 更新错误状态
    update_parser = subparsers.add_parser("update", help="更新错误状态")
    update_parser.add_argument("error_id", help="错误ID")
    update_parser.add_argument("--status", required=True, help="新状态")
    update_parser.add_argument("--assignee", help="负责人")
    update_parser.add_argument("--resolution-type", help="解决方案类型")
    update_parser.add_argument("--notes", help="处理备注")
    update_parser.add_argument("--updated-by", help="更新人")
    
    # 待处理错误
    pending_parser = subparsers.add_parser("pending", help="显示待处理错误")
    pending_parser.add_argument("--priority", help="优先级")
    pending_parser.add_argument("--assignee", help="负责人")
    
    # 仪表板
    subparsers.add_parser("dashboard", help="显示错误监控仪表板")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = ErrorManager()
    
    try:
        if args.command == "list":
            errors = manager.list_errors(
                category=args.category,
                severity=args.severity,
                status=args.status,
                assignee=args.assignee,
                days=args.days,
                limit=args.limit
            )
            print(format_error_list(errors))
        
        elif args.command == "show":
            details = manager.show_error_details(args.error_id)
            if details:
                print(format_error_details(details))
            else:
                print(f"错误不存在: {args.error_id}")
        
        elif args.command == "update":
            success = manager.update_error_status(
                error_id=args.error_id,
                status=args.status,
                assignee=args.assignee,
                resolution_type=args.resolution_type,
                notes=args.notes,
                updated_by=args.updated_by
            )
            if success:
                print(f"错误状态已更新: {args.error_id}")
            else:
                print(f"更新失败: {args.error_id}")
        
        elif args.command == "pending":
            errors = manager.get_pending_errors(
                priority=args.priority,
                assignee=args.assignee
            )
            print(format_error_list(errors))
        
        elif args.command == "dashboard":
            summary = manager.get_dashboard_summary()
            print(format_dashboard(summary))
    
    except Exception as e:
        print(f"执行命令时出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
