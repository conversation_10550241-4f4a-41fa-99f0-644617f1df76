#!/bin/bash

# 柴管家项目开发工具测试验证脚本
# 验证所有开发工具的功能是否正常

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    log_info "测试: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        log_success "✅ $test_name - 通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log_error "❌ $test_name - 失败"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 测试脚本可执行性
test_script_permissions() {
    log_header "测试脚本权限"
    
    local scripts=(
        "scripts/docker-start.sh"
        "scripts/db-manager.sh"
        "scripts/log-manager.sh"
        "scripts/performance-monitor.sh"
        "scripts/health-check.sh"
    )
    
    for script in "${scripts[@]}"; do
        run_test "脚本可执行: $script" "test -x $script"
    done
}

# 测试脚本帮助信息
test_script_help() {
    log_header "测试脚本帮助信息"
    
    run_test "数据库管理脚本帮助" "./scripts/db-manager.sh --help"
    run_test "日志管理脚本帮助" "./scripts/log-manager.sh --help"
    run_test "性能监控脚本帮助" "./scripts/performance-monitor.sh --help"
}

# 测试Docker服务状态
test_docker_services() {
    log_header "测试Docker服务状态"
    
    run_test "Docker Compose可用" "docker-compose --version"
    run_test "Docker服务运行" "docker ps"
    
    # 检查基础服务是否运行
    local services=("postgres" "redis" "rabbitmq")
    
    for service in "${services[@]}"; do
        run_test "服务运行: $service" "docker ps | grep chaiguanjia-$service"
    done
}

# 测试网络连接
test_network_connectivity() {
    log_header "测试网络连接"
    
    # 测试端口连接
    local ports=(
        "5432:PostgreSQL"
        "6379:Redis"
        "5672:RabbitMQ"
        "5050:PgAdmin"
        "8081:Redis Commander"
        "15672:RabbitMQ Management"
    )
    
    for port_info in "${ports[@]}"; do
        local port="${port_info%:*}"
        local service="${port_info#*:}"
        run_test "端口连接: $service ($port)" "nc -z localhost $port"
    done
}

# 测试Web界面访问
test_web_interfaces() {
    log_header "测试Web界面访问"
    
    # 测试HTTP响应
    local interfaces=(
        "5050:PgAdmin"
        "8081:Redis Commander"
        "15672:RabbitMQ Management"
    )
    
    for interface_info in "${interfaces[@]}"; do
        local port="${interface_info%:*}"
        local service="${interface_info#*:}"
        run_test "Web界面: $service" "curl -s -o /dev/null -w '%{http_code}' http://localhost:$port | grep -E '^(200|302|401)$'"
    done
}

# 测试配置文件
test_configuration_files() {
    log_header "测试配置文件"
    
    local configs=(
        ".vscode/settings.json:VSCode设置"
        ".vscode/launch.json:VSCode调试配置"
        ".vscode/tasks.json:VSCode任务配置"
        ".devcontainer/devcontainer.json:开发容器配置"
        "docker-compose.yml:Docker Compose配置"
        "docker-compose.dev.yml:开发环境配置"
    )
    
    for config_info in "${configs[@]}"; do
        local file="${config_info%:*}"
        local desc="${config_info#*:}"
        run_test "配置文件: $desc" "test -f $file && python -m json.tool $file >/dev/null 2>&1 || test -f $file"
    done
}

# 测试日志管理功能
test_log_management() {
    log_header "测试日志管理功能"
    
    run_test "查看服务列表" "./scripts/log-manager.sh view --lines 1"
    run_test "日志搜索功能" "./scripts/log-manager.sh search --keyword 'postgres' --lines 1"
    
    # 创建测试日志目录
    mkdir -p logs
    run_test "日志导出功能" "./scripts/log-manager.sh export --output logs/test.log --lines 10"
    
    # 清理测试文件
    rm -f logs/test.log
}

# 测试性能监控功能
test_performance_monitoring() {
    log_header "测试性能监控功能"
    
    run_test "系统概览" "./scripts/performance-monitor.sh overview"
    run_test "告警检查" "./scripts/performance-monitor.sh alerts --threshold 95"
    
    # 创建测试报告目录
    mkdir -p reports
    run_test "性能报告生成" "./scripts/performance-monitor.sh report --output reports/test.txt"
    
    # 清理测试文件
    rm -f reports/test.txt
}

# 测试数据库管理功能
test_database_management() {
    log_header "测试数据库管理功能"
    
    # 基础连接测试
    run_test "数据库连接检查" "docker exec chaiguanjia-postgres pg_isready -h localhost -p 5432"
    
    # 如果数据库用户不存在，跳过需要认证的测试
    if docker exec chaiguanjia-postgres psql -U postgres -c "SELECT 1" >/dev/null 2>&1; then
        run_test "数据库查询测试" "docker exec chaiguanjia-postgres psql -U postgres -c 'SELECT version();'"
    else
        log_warning "跳过数据库认证测试（用户未配置）"
    fi
}

# 测试文档完整性
test_documentation() {
    log_header "测试文档完整性"
    
    local docs=(
        "docs/development/README.md:开发文档"
        "docs/development/dev-tools-guide.md:开发工具指南"
        "README.md:项目README"
    )
    
    for doc_info in "${docs[@]}"; do
        local file="${doc_info%:*}"
        local desc="${doc_info#*:}"
        run_test "文档存在: $desc" "test -f $file"
    done
}

# 测试VSCode配置
test_vscode_configuration() {
    log_header "测试VSCode配置"
    
    # 检查VSCode配置文件语法
    run_test "VSCode设置语法" "python -c 'import json; json.load(open(\".vscode/settings.json\"))'"
    run_test "VSCode调试配置语法" "python -c 'import json; json.load(open(\".vscode/launch.json\"))'"
    run_test "VSCode任务配置语法" "python -c 'import json; json.load(open(\".vscode/tasks.json\"))'"
    
    # 检查开发容器配置
    run_test "开发容器配置语法" "python -c 'import json; json.load(open(\".devcontainer/devcontainer.json\"))'"
}

# 显示测试结果摘要
show_test_summary() {
    log_header "测试结果摘要"
    
    echo ""
    echo "📊 测试统计:"
    echo "  总测试数: $TOTAL_TESTS"
    echo "  通过: $PASSED_TESTS"
    echo "  失败: $FAILED_TESTS"
    echo "  成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    echo ""
    
    if [ $FAILED_TESTS -eq 0 ]; then
        log_success "🎉 所有测试通过！开发工具集成验证成功。"
        return 0
    else
        log_warning "⚠️  有 $FAILED_TESTS 个测试失败，请检查相关配置。"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
柴管家项目开发工具测试验证脚本

用法: $0 [选项]

选项:
    --quick         快速测试（跳过耗时测试）
    --verbose       详细输出
    --help, -h      显示帮助信息

测试类别:
    - 脚本权限和可执行性
    - Docker服务状态
    - 网络连接性
    - Web界面访问
    - 配置文件完整性
    - 日志管理功能
    - 性能监控功能
    - 数据库管理功能
    - 文档完整性
    - VSCode配置

EOF
}

# 主函数
main() {
    local quick_mode=false
    local verbose=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --quick)
                quick_mode=true
                shift
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_header "柴管家项目开发工具测试验证"
    log_info "开始验证开发工具集成..."
    echo ""
    
    # 执行测试
    test_script_permissions
    test_script_help
    test_docker_services
    test_network_connectivity
    test_web_interfaces
    test_configuration_files
    test_vscode_configuration
    test_documentation
    
    if [ "$quick_mode" = false ]; then
        test_log_management
        test_performance_monitoring
        test_database_management
    else
        log_info "快速模式：跳过耗时测试"
    fi
    
    echo ""
    show_test_summary
}

# 执行主函数
main "$@"
