#!/bin/bash
# 柴管家项目 CI 构建脚本
# 统一的构建脚本，支持前端和后端构建

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -ne 1 ]; then
    log_error "用法: $0 <frontend|backend>"
    exit 1
fi

BUILD_TYPE=$1

case $BUILD_TYPE in
    "frontend")
        log_info "开始前端构建..."
        
        # 进入前端目录
        cd frontend
        
        # 检查 Node.js 和 npm
        if ! command -v node &> /dev/null; then
            log_error "Node.js 未安装"
            exit 1
        fi
        
        if ! command -v npm &> /dev/null; then
            log_error "npm 未安装"
            exit 1
        fi
        
        log_info "Node.js 版本: $(node --version)"
        log_info "npm 版本: $(npm --version)"
        
        # 检查依赖是否已安装
        if [ ! -d "node_modules" ]; then
            log_error "依赖未安装，请先运行 npm ci"
            exit 1
        fi
        
        # 清理之前的构建结果
        log_info "清理之前的构建结果..."
        rm -rf dist/
        
        # 运行构建
        log_info "运行前端构建..."
        if npm run build; then
            log_success "前端构建成功"
        else
            log_error "前端构建失败"
            exit 1
        fi
        
        # 检查构建产物
        if [ -d "dist" ]; then
            log_success "构建产物生成成功"
            log_info "构建产物位置: frontend/dist/"
            
            # 显示构建产物信息
            log_info "构建产物大小:"
            du -sh dist/
            
            log_info "构建产物文件列表:"
            find dist/ -type f -name "*.js" -o -name "*.css" -o -name "*.html" | head -10
            
            # 检查关键文件
            if [ -f "dist/index.html" ]; then
                log_success "index.html 生成成功"
            else
                log_error "index.html 未生成"
                exit 1
            fi
            
            # 检查资源文件
            if [ -d "dist/assets" ]; then
                log_success "静态资源文件夹生成成功"
                log_info "静态资源数量: $(find dist/assets -type f | wc -l)"
            else
                log_warning "静态资源文件夹未生成"
            fi
        else
            log_error "构建产物未生成"
            exit 1
        fi
        
        log_success "前端构建完成！"
        ;;
        
    "backend")
        log_info "开始后端 Docker 镜像构建..."
        
        # 进入后端目录
        cd backend
        
        # 检查 Docker
        if ! command -v docker &> /dev/null; then
            log_error "Docker 未安装"
            exit 1
        fi
        
        log_info "Docker 版本: $(docker --version)"
        
        # 检查 Dockerfile
        if [ ! -f "Dockerfile" ]; then
            log_error "Dockerfile 不存在"
            exit 1
        fi
        
        # 设置镜像标签
        IMAGE_NAME="chaiguanjia-backend"
        IMAGE_TAG="${GITHUB_SHA:-latest}"
        FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
        
        log_info "构建镜像: $FULL_IMAGE_NAME"
        
        # 构建 Docker 镜像
        log_info "开始构建 Docker 镜像..."
        if docker build -t "$FULL_IMAGE_NAME" .; then
            log_success "Docker 镜像构建成功"
        else
            log_error "Docker 镜像构建失败"
            exit 1
        fi
        
        # 检查镜像
        log_info "检查构建的镜像..."
        if docker images | grep -q "$IMAGE_NAME"; then
            log_success "镜像已成功创建"
            
            # 显示镜像信息
            log_info "镜像信息:"
            docker images | grep "$IMAGE_NAME" | head -1
            
            # 获取镜像大小
            IMAGE_SIZE=$(docker images --format "table {{.Size}}" | grep -v SIZE | head -1)
            log_info "镜像大小: $IMAGE_SIZE"
        else
            log_error "镜像创建失败"
            exit 1
        fi
        
        # 运行基本健康检查
        log_info "运行镜像健康检查..."
        CONTAINER_ID=$(docker run -d -p 8000:8000 "$FULL_IMAGE_NAME")
        
        # 等待容器启动
        sleep 10
        
        # 检查容器状态
        if docker ps | grep -q "$CONTAINER_ID"; then
            log_success "容器启动成功"
            
            # 测试健康检查端点
            if curl -f http://localhost:8000/health > /dev/null 2>&1; then
                log_success "健康检查端点响应正常"
            else
                log_warning "健康检查端点无响应（可能需要更长启动时间）"
            fi
        else
            log_error "容器启动失败"
            docker logs "$CONTAINER_ID"
            exit 1
        fi
        
        # 清理测试容器
        log_info "清理测试容器..."
        docker stop "$CONTAINER_ID" > /dev/null 2>&1
        docker rm "$CONTAINER_ID" > /dev/null 2>&1
        
        # 如果是 CI 环境，保存镜像
        if [ -n "$CI" ]; then
            log_info "保存镜像用于后续步骤..."
            docker save "$FULL_IMAGE_NAME" | gzip > "../${IMAGE_NAME}-${IMAGE_TAG}.tar.gz"
            log_success "镜像已保存为 ${IMAGE_NAME}-${IMAGE_TAG}.tar.gz"
        fi
        
        log_success "后端 Docker 镜像构建完成！"
        ;;
        
    *)
        log_error "无效的构建类型: $BUILD_TYPE"
        log_error "支持的类型: frontend, backend"
        exit 1
        ;;
esac

log_success "构建完成！"
