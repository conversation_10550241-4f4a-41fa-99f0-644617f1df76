#!/usr/bin/env python3
"""
柴管家项目日志分析工具
支持日志检索、统计分析和报告生成
"""

import json
import argparse
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
import sys


class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_files = {
            'app': self.log_dir / "app.log",
            'business': self.log_dir / "business.log",
        }
    
    def search_logs(self, 
                   pattern: str,
                   log_type: str = "app",
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None,
                   level: Optional[str] = None,
                   limit: int = 100) -> List[Dict[str, Any]]:
        """搜索日志"""
        
        log_file = self.log_files.get(log_type)
        if not log_file or not log_file.exists():
            print(f"日志文件不存在: {log_file}")
            return []
        
        results = []
        pattern_re = re.compile(pattern, re.IGNORECASE)
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if len(results) >= limit:
                        break
                    
                    try:
                        log_entry = json.loads(line.strip())
                    except json.JSONDecodeError:
                        # 处理非JSON格式的日志行
                        if pattern_re.search(line):
                            results.append({
                                'line_number': line_num,
                                'raw_line': line.strip(),
                                'timestamp': None,
                                'level': None,
                                'message': line.strip()
                            })
                        continue
                    
                    # 时间过滤
                    if start_time or end_time:
                        log_time = self._parse_timestamp(log_entry.get('timestamp'))
                        if log_time:
                            if start_time and log_time < start_time:
                                continue
                            if end_time and log_time > end_time:
                                continue
                    
                    # 级别过滤
                    if level and log_entry.get('level', '').upper() != level.upper():
                        continue
                    
                    # 模式匹配
                    if self._match_pattern(log_entry, pattern_re):
                        log_entry['line_number'] = line_num
                        results.append(log_entry)
        
        except Exception as e:
            print(f"读取日志文件时出错: {e}")
        
        return results
    
    def analyze_errors(self, 
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """分析错误日志"""
        
        error_logs = self.search_logs(
            pattern="",
            level="ERROR",
            start_time=start_time,
            end_time=end_time,
            limit=10000
        )
        
        # 统计错误类型
        error_types = Counter()
        error_messages = Counter()
        error_modules = Counter()
        hourly_errors = defaultdict(int)
        
        for log in error_logs:
            # 错误类型统计
            error_type = log.get('error_type') or self._extract_error_type(log.get('message', ''))
            if error_type:
                error_types[error_type] += 1
            
            # 错误消息统计
            message = log.get('message', '')[:100]  # 截取前100字符
            error_messages[message] += 1
            
            # 模块统计
            module = log.get('module') or log.get('logger', '')
            if module:
                error_modules[module] += 1
            
            # 按小时统计
            timestamp = self._parse_timestamp(log.get('timestamp'))
            if timestamp:
                hour_key = timestamp.strftime('%Y-%m-%d %H:00')
                hourly_errors[hour_key] += 1
        
        return {
            'total_errors': len(error_logs),
            'error_types': dict(error_types.most_common(10)),
            'error_messages': dict(error_messages.most_common(10)),
            'error_modules': dict(error_modules.most_common(10)),
            'hourly_distribution': dict(sorted(hourly_errors.items())),
        }
    
    def analyze_performance(self,
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """分析性能指标"""
        
        # 搜索API请求日志
        api_logs = self.search_logs(
            pattern="API请求完成",
            start_time=start_time,
            end_time=end_time,
            limit=10000
        )
        
        response_times = []
        status_codes = Counter()
        endpoints = defaultdict(list)
        
        for log in api_logs:
            extra_fields = log.get('extra_fields', {})
            
            # 响应时间
            process_time = extra_fields.get('process_time')
            if process_time:
                response_times.append(process_time)
            
            # 状态码
            status_code = extra_fields.get('status_code')
            if status_code:
                status_codes[status_code] += 1
            
            # 端点性能
            message = log.get('message', '')
            endpoint = self._extract_endpoint(message)
            if endpoint and process_time:
                endpoints[endpoint].append(process_time)
        
        # 计算统计指标
        performance_stats = {}
        if response_times:
            response_times.sort()
            n = len(response_times)
            performance_stats = {
                'total_requests': n,
                'avg_response_time': sum(response_times) / n,
                'median_response_time': response_times[n // 2],
                'p95_response_time': response_times[int(n * 0.95)],
                'p99_response_time': response_times[int(n * 0.99)],
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
            }
        
        # 端点性能统计
        endpoint_stats = {}
        for endpoint, times in endpoints.items():
            if times:
                times.sort()
                n = len(times)
                endpoint_stats[endpoint] = {
                    'count': n,
                    'avg_time': sum(times) / n,
                    'median_time': times[n // 2],
                    'max_time': max(times),
                }
        
        return {
            'performance_stats': performance_stats,
            'status_codes': dict(status_codes),
            'top_endpoints': dict(sorted(endpoint_stats.items(), 
                                       key=lambda x: x[1]['avg_time'], 
                                       reverse=True)[:10]),
        }
    
    def analyze_business_events(self,
                               event_type: Optional[str] = None,
                               start_time: Optional[datetime] = None,
                               end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """分析业务事件"""
        
        pattern = event_type if event_type else ""
        business_logs = self.search_logs(
            pattern=pattern,
            log_type="business",
            start_time=start_time,
            end_time=end_time,
            limit=10000
        )
        
        event_types = Counter()
        event_status = Counter()
        user_activities = defaultdict(int)
        hourly_events = defaultdict(int)
        
        for log in business_logs:
            # 事件类型统计
            evt_type = log.get('event_type', '')
            if evt_type:
                event_types[evt_type] += 1
            
            # 事件状态统计
            status = log.get('status', '')
            if status:
                event_status[status] += 1
            
            # 用户活动统计
            user_id = log.get('user_id')
            if user_id:
                user_activities[user_id] += 1
            
            # 按小时统计
            timestamp = self._parse_timestamp(log.get('timestamp'))
            if timestamp:
                hour_key = timestamp.strftime('%Y-%m-%d %H:00')
                hourly_events[hour_key] += 1
        
        return {
            'total_events': len(business_logs),
            'event_types': dict(event_types.most_common(10)),
            'event_status': dict(event_status),
            'top_users': dict(Counter(user_activities).most_common(10)),
            'hourly_distribution': dict(sorted(hourly_events.items())),
        }
    
    def generate_report(self, 
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None) -> str:
        """生成日志分析报告"""
        
        if not start_time:
            start_time = datetime.now() - timedelta(hours=24)
        if not end_time:
            end_time = datetime.now()
        
        print(f"正在分析 {start_time} 到 {end_time} 的日志...")
        
        # 分析各项指标
        error_analysis = self.analyze_errors(start_time, end_time)
        performance_analysis = self.analyze_performance(start_time, end_time)
        business_analysis = self.analyze_business_events(start_time=start_time, end_time=end_time)
        
        # 生成报告
        report = f"""
# 柴管家日志分析报告

**分析时间范围**: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}
**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 错误分析

- **总错误数**: {error_analysis['total_errors']}
- **主要错误类型**:
{self._format_counter(error_analysis['error_types'])}

- **错误模块分布**:
{self._format_counter(error_analysis['error_modules'])}

## 性能分析

- **总请求数**: {performance_analysis.get('performance_stats', {}).get('total_requests', 0)}
- **平均响应时间**: {performance_analysis.get('performance_stats', {}).get('avg_response_time', 0):.3f}s
- **P95响应时间**: {performance_analysis.get('performance_stats', {}).get('p95_response_time', 0):.3f}s
- **状态码分布**:
{self._format_counter(performance_analysis['status_codes'])}

## 业务事件分析

- **总事件数**: {business_analysis['total_events']}
- **事件类型分布**:
{self._format_counter(business_analysis['event_types'])}

- **事件状态分布**:
{self._format_counter(business_analysis['event_status'])}

---
*报告由柴管家日志分析工具生成*
"""
        
        return report
    
    def _parse_timestamp(self, timestamp_str: Optional[str]) -> Optional[datetime]:
        """解析时间戳"""
        if not timestamp_str:
            return None
        
        try:
            # 处理ISO格式时间戳
            if timestamp_str.endswith('Z'):
                timestamp_str = timestamp_str[:-1] + '+00:00'
            return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        except Exception:
            return None
    
    def _match_pattern(self, log_entry: Dict[str, Any], pattern_re: re.Pattern) -> bool:
        """检查日志条目是否匹配模式"""
        searchable_fields = ['message', 'event_name', 'event_type', 'logger']
        
        for field in searchable_fields:
            value = log_entry.get(field, '')
            if isinstance(value, str) and pattern_re.search(value):
                return True
        
        return False
    
    def _extract_error_type(self, message: str) -> Optional[str]:
        """从错误消息中提取错误类型"""
        # 匹配常见的错误类型模式
        patterns = [
            r'(\w+Error):',
            r'(\w+Exception):',
            r'(\w+Timeout):',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_endpoint(self, message: str) -> Optional[str]:
        """从消息中提取API端点"""
        # 匹配API请求消息中的端点
        match = re.search(r'(GET|POST|PUT|DELETE|PATCH)\s+([^\s]+)', message)
        if match:
            return f"{match.group(1)} {match.group(2)}"
        return None
    
    def _format_counter(self, counter_dict: Dict[str, int]) -> str:
        """格式化计数器字典为可读字符串"""
        if not counter_dict:
            return "  无数据"
        
        lines = []
        for key, count in counter_dict.items():
            lines.append(f"  - {key}: {count}")
        
        return "\n".join(lines)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="柴管家日志分析工具")
    parser.add_argument("--log-dir", default="logs", help="日志目录路径")
    parser.add_argument("--command", choices=["search", "errors", "performance", "business", "report"], 
                       default="report", help="执行的命令")
    parser.add_argument("--pattern", help="搜索模式")
    parser.add_argument("--log-type", choices=["app", "business"], default="app", help="日志类型")
    parser.add_argument("--level", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], help="日志级别")
    parser.add_argument("--start-time", help="开始时间 (YYYY-MM-DD HH:MM:SS)")
    parser.add_argument("--end-time", help="结束时间 (YYYY-MM-DD HH:MM:SS)")
    parser.add_argument("--limit", type=int, default=100, help="结果限制数量")
    parser.add_argument("--event-type", help="业务事件类型")
    
    args = parser.parse_args()
    
    # 解析时间参数
    start_time = None
    end_time = None
    
    if args.start_time:
        try:
            start_time = datetime.strptime(args.start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            print("开始时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式")
            sys.exit(1)
    
    if args.end_time:
        try:
            end_time = datetime.strptime(args.end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            print("结束时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式")
            sys.exit(1)
    
    # 创建分析器
    analyzer = LogAnalyzer(args.log_dir)
    
    # 执行命令
    if args.command == "search":
        if not args.pattern:
            print("搜索命令需要指定 --pattern 参数")
            sys.exit(1)
        
        results = analyzer.search_logs(
            pattern=args.pattern,
            log_type=args.log_type,
            start_time=start_time,
            end_time=end_time,
            level=args.level,
            limit=args.limit
        )
        
        print(f"找到 {len(results)} 条匹配的日志:")
        for result in results:
            print(f"[{result.get('line_number', 'N/A')}] {result.get('timestamp', 'N/A')} {result.get('level', 'N/A')} {result.get('message', 'N/A')}")
    
    elif args.command == "errors":
        analysis = analyzer.analyze_errors(start_time, end_time)
        print(json.dumps(analysis, indent=2, ensure_ascii=False))
    
    elif args.command == "performance":
        analysis = analyzer.analyze_performance(start_time, end_time)
        print(json.dumps(analysis, indent=2, ensure_ascii=False))
    
    elif args.command == "business":
        analysis = analyzer.analyze_business_events(args.event_type, start_time, end_time)
        print(json.dumps(analysis, indent=2, ensure_ascii=False))
    
    elif args.command == "report":
        report = analyzer.generate_report(start_time, end_time)
        print(report)


if __name__ == "__main__":
    main()
