#!/bin/bash

# 柴管家项目 Docker 启动脚本
# 支持开发、测试、生产环境的一键启动

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
柴管家项目 Docker 启动脚本

用法: $0 [选项] [环境]

环境:
    dev         启动开发环境 (默认)
    prod        启动生产环境
    test        启动测试环境

选项:
    -h, --help      显示此帮助信息
    -b, --build     强制重新构建镜像
    -d, --detach    后台运行
    -f, --force     强制重新创建容器
    --no-deps       不启动依赖服务
    --scale         指定服务副本数 (格式: service=num)
    --clean         清理所有容器和数据卷
    --reset         重置开发环境（清理并重新启动）
    --status        查看服务状态
    --logs          查看服务日志

示例:
    $0 dev                          # 启动开发环境
    $0 prod -d                      # 后台启动生产环境
    $0 dev -b                       # 重新构建并启动开发环境
    $0 prod --scale backend=3       # 启动生产环境，后端3个副本
    $0 --clean                      # 清理所有资源
    $0 --reset                      # 重置开发环境

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi

    log_success "系统依赖检查通过"
}

# 检查环境文件
check_env_file() {
    local env=$1
    local env_file=".env"

    if [ "$env" = "dev" ]; then
        env_file=".env.dev"
    elif [ "$env" = "prod" ]; then
        env_file=".env.prod"
    fi

    if [ ! -f "$env_file" ]; then
        log_warning "环境文件 $env_file 不存在"
        if [ -f ".env.example" ]; then
            log_info "从 .env.example 复制环境文件..."
            cp .env.example "$env_file"
            log_warning "请编辑 $env_file 文件，配置正确的环境变量"
        else
            log_error "找不到 .env.example 文件"
            exit 1
        fi
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    mkdir -p logs/{nginx,backend,celery}
    mkdir -p data/{postgres,redis,rabbitmq}
    mkdir -p secrets

    log_success "目录创建完成"
}

# 设置文件权限
set_permissions() {
    log_info "设置文件权限..."

    chmod +x scripts/*.sh

    # 设置日志目录权限
    if [ -d "logs" ]; then
        chmod -R 755 logs/
    fi

    log_success "文件权限设置完成"
}

# 构建镜像
build_images() {
    local env=$1
    local force_build=$2

    if [ "$force_build" = true ]; then
        log_info "强制重新构建镜像..."
        docker-compose -f docker-compose.yml -f docker-compose.$env.yml build --no-cache
    else
        log_info "构建镜像..."
        docker-compose -f docker-compose.yml -f docker-compose.$env.yml build
    fi

    log_success "镜像构建完成"
}

# 等待服务健康
wait_for_service() {
    local service_name=$1
    local health_url=$2
    local max_attempts=${3:-30}
    local sleep_interval=${4:-5}

    log_info "等待 $service_name 服务启动..."

    for i in $(seq 1 $max_attempts); do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            log_success "$service_name 服务已就绪"
            return 0
        fi

        if [ $i -eq $max_attempts ]; then
            log_error "$service_name 服务启动超时"
            return 1
        fi

        log_info "等待 $service_name 服务启动... ($i/$max_attempts)"
        sleep $sleep_interval
    done
}

# 检查服务状态
check_services_status() {
    local env=$1
    local compose_files="-f docker-compose.yml -f docker-compose.$env.yml"

    log_info "检查服务状态..."

    # 检查容器状态
    local unhealthy_services=$(docker-compose $compose_files ps --services --filter "status=exited")
    if [ -n "$unhealthy_services" ]; then
        log_error "以下服务未正常启动: $unhealthy_services"
        return 1
    fi

    # 等待关键服务健康检查
    if [ "$env" = "dev" ]; then
        wait_for_service "PostgreSQL" "http://localhost:5432" 12 5 || return 1
        wait_for_service "Redis" "http://localhost:6379" 12 5 || return 1
        wait_for_service "Backend API" "http://localhost:8000/health" 20 5 || return 1
        wait_for_service "Frontend" "http://localhost:5173" 15 5 || return 1
    fi

    log_success "所有服务状态检查通过"
    return 0
}

# 显示服务信息
show_service_info() {
    local env=$1

    echo ""
    log_success "🎉 柴管家项目启动完成！"
    echo ""

    if [ "$env" = "dev" ]; then
        echo "📋 开发环境访问地址:"
        echo "  🎨 前端应用:     http://localhost:5173"
        echo "  ⚡ 后端API:      http://localhost:8000"
        echo "  📚 API文档:      http://localhost:8000/docs"
        echo "  🌸 Celery监控:   http://localhost:5555 (admin/flower123)"
        echo "  🐰 RabbitMQ管理: http://localhost:15672 (chaiguanjia/rabbitmq123)"
        echo "  🔧 PgAdmin:      http://localhost:5050 (<EMAIL>/admin123)"
        echo "  🔧 Redis管理:    http://localhost:8081"
        echo ""
        echo "🔧 常用命令:"
        echo "  查看服务状态:    docker-compose ps"
        echo "  查看服务日志:    docker-compose logs -f [service-name]"
        echo "  重启服务:        docker-compose restart [service-name]"
        echo "  停止所有服务:    docker-compose down"
        echo "  清理环境:        ./scripts/docker-start.sh --clean"
    elif [ "$env" = "prod" ]; then
        echo "🌐 生产环境访问地址:"
        echo "  应用入口: http://localhost"
        echo "  HTTPS入口: https://localhost"
    fi

    echo ""
}

# 启动服务
start_services() {
    local env=$1
    local detach=$2
    local force=$3
    local no_deps=$4
    local scale=$5

    local compose_files="-f docker-compose.yml -f docker-compose.$env.yml"
    local compose_args=""

    if [ "$detach" = true ]; then
        compose_args="$compose_args -d"
    fi

    if [ "$force" = true ]; then
        compose_args="$compose_args --force-recreate"
    fi

    if [ "$no_deps" = true ]; then
        compose_args="$compose_args --no-deps"
    fi

    if [ -n "$scale" ]; then
        compose_args="$compose_args --scale $scale"
    fi

    log_info "启动 $env 环境服务..."
    docker-compose $compose_files up $compose_args &
    local compose_pid=$!

    if [ "$detach" = true ]; then
        # 等待一段时间让服务启动
        sleep 10

        # 检查服务状态
        if check_services_status "$env"; then
            show_service_info "$env"
        else
            log_error "服务启动验证失败，请检查日志"
            docker-compose $compose_files logs --tail=50
            return 1
        fi
    else
        # 前台运行，等待用户中断
        wait $compose_pid
    fi
}

# 清理环境
clean_environment() {
    log_info "清理柴管家项目环境..."

    # 停止所有服务
    log_info "停止所有服务..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml down -v --remove-orphans 2>/dev/null || true
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml down -v --remove-orphans 2>/dev/null || true

    # 清理镜像
    log_info "清理项目镜像..."
    docker images | grep chaiguanjia | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true

    # 清理网络
    log_info "清理项目网络..."
    docker network rm chaiguanjia_chaiguanjia-network 2>/dev/null || true

    # 清理未使用的资源
    log_info "清理未使用的Docker资源..."
    docker system prune -f

    log_success "环境清理完成"
}

# 重置开发环境
reset_environment() {
    log_info "重置柴管家开发环境..."

    # 清理环境
    clean_environment

    # 等待一段时间
    sleep 2

    # 重新启动开发环境
    log_info "重新启动开发环境..."
    main "dev" "-d"
}

# 查看服务状态
show_status() {
    log_info "柴管家项目服务状态:"
    echo ""

    # 检查开发环境
    if docker-compose -f docker-compose.yml -f docker-compose.dev.yml ps | grep -q "Up"; then
        echo "📋 开发环境状态:"
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml ps
        echo ""
    fi

    # 检查生产环境
    if docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps | grep -q "Up"; then
        echo "🌐 生产环境状态:"
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps
        echo ""
    fi

    # 显示资源使用情况
    echo "💾 资源使用情况:"
    docker system df
    echo ""

    # 显示网络信息
    echo "🌐 网络信息:"
    docker network ls | grep chaiguanjia || echo "  无活动网络"
    echo ""
}

# 查看服务日志
show_logs() {
    local env=${1:-dev}
    local service=${2:-}

    local compose_files="-f docker-compose.yml -f docker-compose.$env.yml"

    if [ -n "$service" ]; then
        log_info "查看 $service 服务日志..."
        docker-compose $compose_files logs -f --tail=100 "$service"
    else
        log_info "查看所有服务日志..."
        docker-compose $compose_files logs -f --tail=50
    fi
}

# 主函数
main() {
    local env="dev"
    local build=false
    local detach=false
    local force=false
    local no_deps=false
    local scale=""

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--build)
                build=true
                shift
                ;;
            -d|--detach)
                detach=true
                shift
                ;;
            -f|--force)
                force=true
                shift
                ;;
            --no-deps)
                no_deps=true
                shift
                ;;
            --scale)
                scale="$2"
                shift 2
                ;;
            --clean)
                clean_environment
                exit 0
                ;;
            --reset)
                reset_environment
                exit 0
                ;;
            --status)
                show_status
                exit 0
                ;;
            --logs)
                show_logs "${2:-dev}" "${3:-}"
                exit 0
                ;;
            dev|prod|test)
                env="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    log_info "启动柴管家项目 - $env 环境"

    # 执行启动流程
    check_dependencies
    check_env_file "$env"
    create_directories
    set_permissions

    if [ "$build" = true ]; then
        build_images "$env" true
    fi

    start_services "$env" "$detach" "$force" "$no_deps" "$scale"

    log_success "柴管家项目启动完成！"

    # 显示访问信息
    if [ "$env" = "dev" ]; then
        echo ""
        log_info "开发环境访问地址:"
        echo "  前端应用: http://localhost:5173"
        echo "  后端API: http://localhost:8000"
        echo "  API文档: http://localhost:8000/docs"
        echo "  Celery监控: http://localhost:5555"
        echo "  RabbitMQ管理: http://localhost:15672"
        echo "  PgAdmin: http://localhost:5050"
        echo "  Redis Commander: http://localhost:8081"
    elif [ "$env" = "prod" ]; then
        echo ""
        log_info "生产环境访问地址:"
        echo "  应用入口: http://localhost"
        echo "  HTTPS入口: https://localhost"
    fi
}

# 执行主函数
main "$@"
