#!/usr/bin/env python3
"""
柴管家项目错误监控系统测试脚本
验证错误监控的各项功能是否正常工作
"""

import os
import sys
import time
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "backend"))

def test_error_monitor():
    """测试错误监控器"""
    print("🧪 测试错误监控器...")
    
    try:
        from app.shared.monitoring import get_error_monitor, ErrorCategory, ErrorSeverity
        
        monitor = get_error_monitor()
        
        # 测试异常捕获
        try:
            raise ValueError("这是一个测试错误")
        except Exception as e:
            error_info = monitor.capture_exception(
                exception=e,
                request_id="test-req-123",
                user_id="test-user-456",
                business_context={"test": True}
            )
            
            assert error_info.error_type == "ValueError"
            assert error_info.category == ErrorCategory.VALIDATION_ERROR
            assert error_info.request_id == "test-req-123"
            
        print("✅ 错误监控器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误监控器测试失败: {e}")
        return False


def test_error_tracker():
    """测试错误跟踪器"""
    print("🧪 测试错误跟踪器...")
    
    try:
        from app.shared.monitoring import get_error_tracker, get_error_monitor
        
        tracker = get_error_tracker()
        monitor = get_error_monitor()
        
        # 创建测试错误
        try:
            raise RuntimeError("这是一个测试系统错误")
        except Exception as e:
            error_info = monitor.capture_exception(
                exception=e,
                request_id="test-req-789",
                user_id="test-user-789"
            )
            
            # 记录错误
            record_id = tracker.record_error(error_info)
            assert record_id > 0
            
            # 查询错误
            retrieved_error = tracker.get_error_by_id(error_info.error_id)
            assert retrieved_error is not None
            assert retrieved_error.error_type == "RuntimeError"
            
            # 更新错误状态
            success = tracker.update_error_status(
                error_id=error_info.error_id,
                is_handled=True,
                resolution_status="resolved",
                resolution_notes="测试完成"
            )
            assert success
        
        print("✅ 错误跟踪器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误跟踪器测试失败: {e}")
        return False


def test_error_workflow():
    """测试错误工作流"""
    print("🧪 测试错误工作流...")
    
    try:
        from app.shared.monitoring import get_error_workflow, get_error_monitor, ErrorStatus, ResolutionType
        
        workflow = get_error_workflow()
        monitor = get_error_monitor()
        
        # 创建测试错误
        try:
            raise ConnectionError("这是一个测试连接错误")
        except Exception as e:
            error_info = monitor.capture_exception(
                exception=e,
                request_id="test-req-workflow",
                user_id="test-user-workflow"
            )
            
            # 处理错误工作流
            workflow_data = workflow.process_error(error_info)
            assert workflow_data['error_id'] == error_info.error_id
            assert 'priority' in workflow_data
            
            # 更新错误状态
            success = workflow.update_error_status(
                error_id=error_info.error_id,
                new_status=ErrorStatus.INVESTIGATING,
                resolution_notes="开始调查",
                updated_by="test_user"
            )
            assert success
            
            # 再次更新状态
            success = workflow.update_error_status(
                error_id=error_info.error_id,
                new_status=ErrorStatus.RESOLVED,
                resolution_type=ResolutionType.FIXED,
                resolution_notes="问题已修复",
                updated_by="test_user"
            )
            assert success
        
        print("✅ 错误工作流测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误工作流测试失败: {e}")
        return False


def test_error_handler():
    """测试错误处理器"""
    print("🧪 测试错误处理器...")
    
    try:
        from app.shared.monitoring import ErrorHandler, exception_handler
        
        handler = ErrorHandler()
        
        # 测试异常处理
        try:
            raise KeyError("这是一个测试键错误")
        except Exception as e:
            error_info = handler.handle_exception(
                exception=e,
                business_context={"test_context": "error_handler_test"}
            )
            
            assert error_info.error_type == "KeyError"
            assert error_info.business_context["test_context"] == "error_handler_test"
            
            # 测试错误响应创建
            response = handler.create_error_response(error_info, include_details=True)
            assert response.status_code == 404  # KeyError -> NOT_FOUND_ERROR
        
        # 测试装饰器
        @exception_handler(include_details=True)
        def test_function():
            raise ValueError("装饰器测试错误")
        
        result = test_function()
        # 装饰器应该返回JSONResponse而不是抛出异常
        assert hasattr(result, 'status_code')
        
        print("✅ 错误处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理器测试失败: {e}")
        return False


def test_error_statistics():
    """测试错误统计功能"""
    print("🧪 测试错误统计功能...")
    
    try:
        from app.shared.monitoring import get_error_tracker, get_error_workflow
        
        tracker = get_error_tracker()
        workflow = get_error_workflow()
        
        # 获取统计信息
        stats = tracker.get_error_statistics()
        assert 'total_errors' in stats
        assert 'category_distribution' in stats
        
        workflow_stats = workflow.get_workflow_statistics()
        assert 'total_errors' in workflow_stats
        assert 'status_distribution' in workflow_stats
        
        print("✅ 错误统计功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误统计功能测试失败: {e}")
        return False


def test_integration():
    """测试集成功能"""
    print("🧪 测试集成功能...")
    
    try:
        from app.shared.monitoring import get_error_monitor, get_error_tracker, get_error_workflow
        
        monitor = get_error_monitor()
        tracker = get_error_tracker()
        workflow = get_error_workflow()
        
        # 模拟完整的错误处理流程
        try:
            # 模拟一个数据库错误
            raise Exception("Database connection failed")
        except Exception as e:
            # 1. 监控器捕获错误
            error_info = monitor.capture_exception(
                exception=e,
                request_id="integration-test-123",
                user_id="integration-user-456",
                business_context={
                    "operation": "database_query",
                    "table": "users",
                    "query_type": "SELECT"
                }
            )
            
            # 2. 工作流处理错误
            workflow_data = workflow.process_error(error_info)
            
            # 3. 获取待处理错误
            pending_errors = workflow.get_pending_errors()
            assert len(pending_errors) > 0
            
            # 4. 搜索错误
            search_results = tracker.search_errors(
                error_type="Exception",
                limit=10
            )
            assert len(search_results) > 0
            
            # 5. 获取统计信息
            monitor_stats = monitor.get_error_statistics()
            assert monitor_stats['total_errors'] > 0
        
        print("✅ 集成功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 集成功能测试失败: {e}")
        return False


def test_tools():
    """测试工具脚本"""
    print("🧪 测试工具脚本...")
    
    try:
        # 检查脚本文件是否存在
        error_analyzer = Path("scripts/error-analyzer.py")
        error_manager = Path("scripts/error-manager.py")
        
        if not error_analyzer.exists():
            print(f"❌ 错误分析脚本不存在: {error_analyzer}")
            return False
        
        if not error_manager.exists():
            print(f"❌ 错误管理脚本不存在: {error_manager}")
            return False
        
        # 检查脚本是否可执行
        if not os.access(error_analyzer, os.X_OK):
            print(f"❌ 错误分析脚本不可执行: {error_analyzer}")
            return False
        
        if not os.access(error_manager, os.X_OK):
            print(f"❌ 错误管理脚本不可执行: {error_manager}")
            return False
        
        print("✅ 工具脚本测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 工具脚本测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始柴管家错误监控系统功能测试")
    print("=" * 50)
    
    tests = [
        ("错误监控器", test_error_monitor),
        ("错误跟踪器", test_error_tracker),
        ("错误工作流", test_error_workflow),
        ("错误处理器", test_error_handler),
        ("错误统计", test_error_statistics),
        ("集成功能", test_integration),
        ("工具脚本", test_tools),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            failed += 1
        
        # 短暂延迟，避免测试冲突
        time.sleep(0.1)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！错误监控系统功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查错误监控系统配置")
        return False


def show_system_info():
    """显示系统信息"""
    print("\n📄 错误监控系统信息:")
    
    try:
        from app.shared.monitoring import get_error_tracker, get_error_workflow
        
        tracker = get_error_tracker()
        workflow = get_error_workflow()
        
        # 显示数据库信息
        print(f"📁 错误数据库: {tracker.db_path}")
        
        # 显示统计信息
        stats = tracker.get_error_statistics()
        print(f"📊 总错误数: {stats['total_errors']}")
        
        workflow_stats = workflow.get_workflow_statistics()
        print(f"📋 待处理错误: {workflow_stats['pending_errors']}")
        print(f"✅ 已处理错误: {workflow_stats['handled_errors']}")
        
        # 显示工具脚本
        print(f"🔧 错误分析工具: scripts/error-analyzer.py")
        print(f"🛠️ 错误管理工具: scripts/error-manager.py")
        
    except Exception as e:
        print(f"❌ 获取系统信息失败: {e}")


def main():
    """主函数"""
    # 设置环境变量
    os.environ.setdefault("APP_ENV", "development")
    os.environ.setdefault("LOG_LEVEL", "INFO")
    
    # 运行测试
    success = run_all_tests()
    
    # 显示系统信息
    show_system_info()
    
    # 显示使用提示
    print("\n💡 使用提示:")
    print("1. 查看错误列表: ./scripts/error-manager.py list")
    print("2. 查看错误详情: ./scripts/error-manager.py show <error_id>")
    print("3. 更新错误状态: ./scripts/error-manager.py update <error_id> --status resolved")
    print("4. 查看待处理错误: ./scripts/error-manager.py pending")
    print("5. 查看错误仪表板: ./scripts/error-manager.py dashboard")
    print("6. 生成错误分析报告: ./scripts/error-analyzer.py --command report")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
