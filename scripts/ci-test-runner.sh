#!/bin/bash
# 柴管家项目 CI 测试运行脚本
# 统一的测试执行脚本，支持前端和后端测试

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -ne 1 ]; then
    log_error "用法: $0 <frontend|backend>"
    exit 1
fi

TEST_TYPE=$1

case $TEST_TYPE in
    "frontend")
        log_info "开始前端测试..."
        
        # 进入前端目录
        cd frontend
        
        # 检查 Node.js 和 npm
        if ! command -v node &> /dev/null; then
            log_error "Node.js 未安装"
            exit 1
        fi
        
        if ! command -v npm &> /dev/null; then
            log_error "npm 未安装"
            exit 1
        fi
        
        log_info "Node.js 版本: $(node --version)"
        log_info "npm 版本: $(npm --version)"
        
        # 检查依赖是否已安装
        if [ ! -d "node_modules" ]; then
            log_error "依赖未安装，请先运行 npm ci"
            exit 1
        fi
        
        # 清理之前的测试结果
        log_info "清理之前的测试结果..."
        rm -rf coverage/
        
        # 运行测试
        log_info "运行前端测试..."
        if npm run test:coverage; then
            log_success "前端测试通过"
        else
            log_error "前端测试失败"
            exit 1
        fi
        
        # 检查覆盖率文件
        if [ -f "coverage/lcov.info" ]; then
            log_success "测试覆盖率报告生成成功"
            log_info "覆盖率报告位置: frontend/coverage/"
        else
            log_warning "测试覆盖率报告未生成"
        fi
        
        # 显示覆盖率摘要
        if [ -f "coverage/coverage-summary.json" ]; then
            log_info "覆盖率摘要:"
            cat coverage/coverage-summary.json | grep -E '"lines"|"functions"|"branches"|"statements"' | head -4
        fi
        
        log_success "前端测试完成！"
        ;;
        
    "backend")
        log_info "开始后端测试..."
        
        # 进入后端目录
        cd backend
        
        # 检查 Python
        if ! command -v python &> /dev/null; then
            log_error "Python 未安装"
            exit 1
        fi
        
        log_info "Python 版本: $(python --version)"
        
        # 检查依赖是否已安装
        if ! python -c "import pytest" &> /dev/null; then
            log_error "测试依赖未安装，请先运行 pip install -r requirements-dev.txt"
            exit 1
        fi
        
        # 清理之前的测试结果
        log_info "清理之前的测试结果..."
        rm -rf htmlcov/
        rm -f coverage.xml
        rm -f .coverage
        
        # 设置测试环境变量
        export TESTING=true
        export DATABASE_URL="sqlite+aiosqlite:///./test.db"
        export REDIS_URL="redis://localhost:6379/1"
        
        # 运行测试
        log_info "运行后端测试..."
        if pytest --cov=app --cov-report=xml --cov-report=html --cov-report=term-missing -v; then
            log_success "后端测试通过"
        else
            log_error "后端测试失败"
            exit 1
        fi
        
        # 检查覆盖率文件
        if [ -f "coverage.xml" ]; then
            log_success "测试覆盖率报告生成成功"
            log_info "覆盖率报告位置: backend/htmlcov/"
        else
            log_warning "测试覆盖率报告未生成"
        fi
        
        # 显示覆盖率摘要
        if [ -f "htmlcov/index.html" ]; then
            log_info "HTML 覆盖率报告已生成: backend/htmlcov/index.html"
        fi
        
        # 清理测试数据库
        log_info "清理测试数据库..."
        rm -f test.db
        
        log_success "后端测试完成！"
        ;;
        
    *)
        log_error "无效的测试类型: $TEST_TYPE"
        log_error "支持的类型: frontend, backend"
        exit 1
        ;;
esac

log_success "测试运行完成！"
