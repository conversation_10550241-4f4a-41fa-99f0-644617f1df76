#!/bin/bash
# 柴管家项目 CI 代码质量检查脚本
# 确保 CI 中的检查与 pre-commit 配置保持一致

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -ne 1 ]; then
    log_error "用法: $0 <frontend|backend>"
    exit 1
fi

CHECK_TYPE=$1

case $CHECK_TYPE in
    "frontend")
        log_info "开始前端代码质量检查..."
        
        # 进入前端目录
        cd frontend
        
        # 检查 Node.js 和 npm
        if ! command -v node &> /dev/null; then
            log_error "Node.js 未安装"
            exit 1
        fi
        
        if ! command -v npm &> /dev/null; then
            log_error "npm 未安装"
            exit 1
        fi
        
        log_info "Node.js 版本: $(node --version)"
        log_info "npm 版本: $(npm --version)"
        
        # 检查依赖是否已安装
        if [ ! -d "node_modules" ]; then
            log_error "依赖未安装，请先运行 npm ci"
            exit 1
        fi
        
        # TypeScript 类型检查
        log_info "执行 TypeScript 类型检查..."
        if npm run type-check; then
            log_success "TypeScript 类型检查通过"
        else
            log_error "TypeScript 类型检查失败"
            exit 1
        fi
        
        # Prettier 格式检查
        log_info "执行 Prettier 格式检查..."
        if npm run format:check; then
            log_success "Prettier 格式检查通过"
        else
            log_error "Prettier 格式检查失败"
            log_info "运行 'npm run format' 自动修复格式问题"
            exit 1
        fi
        
        # ESLint 代码质量检查
        log_info "执行 ESLint 代码质量检查..."
        if npm run lint; then
            log_success "ESLint 代码质量检查通过"
        else
            log_error "ESLint 代码质量检查失败"
            log_info "运行 'npm run lint:fix' 自动修复部分问题"
            exit 1
        fi
        
        log_success "前端代码质量检查全部通过！"
        ;;
        
    "backend")
        log_info "开始后端代码质量检查..."
        
        # 进入后端目录
        cd backend
        
        # 检查 Python
        if ! command -v python &> /dev/null; then
            log_error "Python 未安装"
            exit 1
        fi
        
        log_info "Python 版本: $(python --version)"
        
        # 检查依赖是否已安装
        if ! python -c "import black" &> /dev/null; then
            log_error "开发依赖未安装，请先运行 pip install -r requirements-dev.txt"
            exit 1
        fi
        
        # Black 代码格式检查
        log_info "执行 Black 代码格式检查..."
        if black --check --diff .; then
            log_success "Black 代码格式检查通过"
        else
            log_error "Black 代码格式检查失败"
            log_info "运行 'black .' 自动修复格式问题"
            exit 1
        fi
        
        # isort 导入排序检查
        log_info "执行 isort 导入排序检查..."
        if isort --check-only --diff .; then
            log_success "isort 导入排序检查通过"
        else
            log_error "isort 导入排序检查失败"
            log_info "运行 'isort .' 自动修复导入排序问题"
            exit 1
        fi
        
        # flake8 代码质量检查
        log_info "执行 flake8 代码质量检查..."
        if flake8 .; then
            log_success "flake8 代码质量检查通过"
        else
            log_error "flake8 代码质量检查失败"
            exit 1
        fi
        
        # mypy 类型检查
        log_info "执行 mypy 类型检查..."
        if mypy .; then
            log_success "mypy 类型检查通过"
        else
            log_error "mypy 类型检查失败"
            exit 1
        fi
        
        # bandit 安全检查
        log_info "执行 bandit 安全检查..."
        if bandit -r . -c pyproject.toml; then
            log_success "bandit 安全检查通过"
        else
            log_error "bandit 安全检查失败"
            exit 1
        fi
        
        log_success "后端代码质量检查全部通过！"
        ;;
        
    *)
        log_error "无效的检查类型: $CHECK_TYPE"
        log_error "支持的类型: frontend, backend"
        exit 1
        ;;
esac

log_success "代码质量检查完成！"
