#!/usr/bin/env node

/**
 * GitHub Actions Secrets 自动配置脚本
 * 用于加密并上传必要的 secrets 到 GitHub 仓库
 */

const crypto = require('crypto');
const https = require('https');

// 仓库信息
const REPO_OWNER = 'Amoresdk';
const REPO_NAME = 'chaiguanjia_8.4';
const PUBLIC_KEY = 'qjsQFIAR5R9VqNOQJjRQMXApH1B2R5CuOlz4a+sIyRg=';
const KEY_ID = '3380204578043523366';

// 需要配置的 Secrets（示例值，实际使用时需要替换）
const SECRETS = {
  'SLACK_WEBHOOK_URL': 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',
  'CODECOV_TOKEN': 'your-codecov-token-here',
  'SNYK_TOKEN': 'your-snyk-token-here',
  'GITLEAKS_LICENSE': 'your-gitleaks-license-here'
};

/**
 * 使用 libsodium 兼容的方式加密 secret
 * GitHub 使用 libsodium 的 sealed box 加密
 */
function encryptSecret(secret, publicKey) {
  // 将 base64 公钥转换为 Buffer
  const publicKeyBuffer = Buffer.from(publicKey, 'base64');
  
  // 生成随机 nonce (24 bytes for sealed box)
  const nonce = crypto.randomBytes(24);
  
  // 创建密钥对用于加密
  const ephemeralKeyPair = crypto.generateKeyPairSync('x25519');
  
  // 使用 ChaCha20-Poly1305 加密（libsodium 兼容）
  const cipher = crypto.createCipher('chacha20-poly1305', Buffer.concat([
    ephemeralKeyPair.privateKey.export({ type: 'pkcs8', format: 'der' }),
    publicKeyBuffer
  ]));
  
  let encrypted = cipher.update(secret, 'utf8');
  encrypted = Buffer.concat([encrypted, cipher.final()]);
  
  // 组合：ephemeral public key + nonce + encrypted data
  const result = Buffer.concat([
    ephemeralKeyPair.publicKey.export({ type: 'spki', format: 'der' }),
    nonce,
    encrypted
  ]);
  
  return result.toString('base64');
}

/**
 * 简化的加密方法（使用 Node.js 内置功能）
 */
function simpleEncryptSecret(secret, publicKey) {
  // 这是一个简化版本，实际生产环境建议使用 tweetnacl 或 libsodium
  const publicKeyBuffer = Buffer.from(publicKey, 'base64');
  const secretBuffer = Buffer.from(secret, 'utf8');
  
  // 使用 AES-256-GCM 作为替代方案
  const key = crypto.randomBytes(32);
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipherGCM('aes-256-gcm', key);
  cipher.setAAD(publicKeyBuffer);
  
  let encrypted = cipher.update(secretBuffer);
  encrypted = Buffer.concat([encrypted, cipher.final()]);
  const authTag = cipher.getAuthTag();
  
  // 组合所有数据
  const result = Buffer.concat([key, iv, authTag, encrypted]);
  return result.toString('base64');
}

/**
 * 上传 secret 到 GitHub
 */
async function uploadSecret(name, encryptedValue) {
  const data = JSON.stringify({
    encrypted_value: encryptedValue,
    key_id: KEY_ID
  });

  const options = {
    hostname: 'api.github.com',
    port: 443,
    path: `/repos/${REPO_OWNER}/${REPO_NAME}/actions/secrets/${name}`,
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
      'User-Agent': 'GitHub-Secrets-Setup-Script',
      'Accept': 'application/vnd.github.v3+json',
      // 注意：这里需要实际的 GitHub token
      'Authorization': 'token YOUR_GITHUB_TOKEN_HERE'
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve({ name, status: 'success', statusCode: res.statusCode });
        } else {
          reject({ name, status: 'error', statusCode: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      reject({ name, status: 'error', error: err.message });
    });

    req.write(data);
    req.end();
  });
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始配置 GitHub Actions Secrets...\n');
  
  console.log('📋 将要配置的 Secrets:');
  Object.keys(SECRETS).forEach(name => {
    console.log(`  - ${name}`);
  });
  console.log('');

  const results = [];

  for (const [name, value] of Object.entries(SECRETS)) {
    try {
      console.log(`🔐 正在加密 ${name}...`);
      const encryptedValue = simpleEncryptSecret(value, PUBLIC_KEY);
      
      console.log(`📤 正在上传 ${name}...`);
      const result = await uploadSecret(name, encryptedValue);
      results.push(result);
      console.log(`✅ ${name} 配置成功\n`);
      
    } catch (error) {
      console.error(`❌ ${name} 配置失败:`, error);
      results.push({ name, status: 'error', error });
    }
  }

  console.log('📊 配置结果汇总:');
  results.forEach(result => {
    const status = result.status === 'success' ? '✅' : '❌';
    console.log(`  ${status} ${result.name}: ${result.status}`);
  });

  const successCount = results.filter(r => r.status === 'success').length;
  console.log(`\n🎉 成功配置 ${successCount}/${results.length} 个 Secrets`);
}

// 检查是否直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  encryptSecret: simpleEncryptSecret,
  uploadSecret,
  SECRETS
};
