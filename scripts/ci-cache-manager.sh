#!/bin/bash
# 柴管家项目 CI 缓存管理脚本
# 管理和优化 CI/CD 流水线中的缓存

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "柴管家项目 CI 缓存管理脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  warm-up <frontend|backend|all>  预热缓存"
    echo "  clean <frontend|backend|all>    清理缓存"
    echo "  info <frontend|backend|all>     显示缓存信息"
    echo "  optimize                        优化缓存配置"
    echo ""
    echo "示例:"
    echo "  $0 warm-up frontend    # 预热前端缓存"
    echo "  $0 clean all          # 清理所有缓存"
    echo "  $0 info backend       # 显示后端缓存信息"
    echo ""
}

# 预热前端缓存
warm_up_frontend() {
    log_info "预热前端缓存..."
    
    cd frontend
    
    # 检查 package-lock.json
    if [ ! -f "package-lock.json" ]; then
        log_error "package-lock.json 不存在"
        return 1
    fi
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm ci --prefer-offline --no-audit
    
    # 预编译 TypeScript
    log_info "预编译 TypeScript..."
    npm run type-check
    
    log_success "前端缓存预热完成"
}

# 预热后端缓存
warm_up_backend() {
    log_info "预热后端缓存..."
    
    cd backend
    
    # 检查 requirements 文件
    if [ ! -f "requirements-dev.txt" ]; then
        log_error "requirements-dev.txt 不存在"
        return 1
    fi
    
    # 安装依赖
    log_info "安装后端依赖..."
    pip install -r requirements-dev.txt
    
    # 预编译 Python 字节码
    log_info "预编译 Python 字节码..."
    python -m compileall app/
    
    log_success "后端缓存预热完成"
}

# 清理前端缓存
clean_frontend() {
    log_info "清理前端缓存..."
    
    cd frontend
    
    # 清理 node_modules
    if [ -d "node_modules" ]; then
        log_info "删除 node_modules..."
        rm -rf node_modules/
    fi
    
    # 清理构建产物
    if [ -d "dist" ]; then
        log_info "删除构建产物..."
        rm -rf dist/
    fi
    
    # 清理测试覆盖率
    if [ -d "coverage" ]; then
        log_info "删除测试覆盖率..."
        rm -rf coverage/
    fi
    
    # 清理 npm 缓存
    log_info "清理 npm 缓存..."
    npm cache clean --force
    
    log_success "前端缓存清理完成"
}

# 清理后端缓存
clean_backend() {
    log_info "清理后端缓存..."
    
    cd backend
    
    # 清理 Python 缓存
    log_info "清理 Python 缓存..."
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    # 清理测试覆盖率
    if [ -d "htmlcov" ]; then
        log_info "删除测试覆盖率..."
        rm -rf htmlcov/
    fi
    
    if [ -f "coverage.xml" ]; then
        rm -f coverage.xml
    fi
    
    if [ -f ".coverage" ]; then
        rm -f .coverage
    fi
    
    # 清理测试数据库
    if [ -f "test.db" ]; then
        log_info "删除测试数据库..."
        rm -f test.db
    fi
    
    # 清理 pip 缓存
    log_info "清理 pip 缓存..."
    pip cache purge
    
    log_success "后端缓存清理完成"
}

# 显示前端缓存信息
info_frontend() {
    log_info "前端缓存信息:"
    
    cd frontend
    
    # node_modules 大小
    if [ -d "node_modules" ]; then
        NODE_MODULES_SIZE=$(du -sh node_modules/ | cut -f1)
        echo "  node_modules 大小: $NODE_MODULES_SIZE"
    else
        echo "  node_modules: 不存在"
    fi
    
    # npm 缓存大小
    NPM_CACHE_SIZE=$(npm cache verify 2>/dev/null | grep "Cache size" | awk '{print $3 " " $4}' || echo "未知")
    echo "  npm 缓存大小: $NPM_CACHE_SIZE"
    
    # 构建产物大小
    if [ -d "dist" ]; then
        DIST_SIZE=$(du -sh dist/ | cut -f1)
        echo "  构建产物大小: $DIST_SIZE"
    else
        echo "  构建产物: 不存在"
    fi
    
    # 测试覆盖率大小
    if [ -d "coverage" ]; then
        COVERAGE_SIZE=$(du -sh coverage/ | cut -f1)
        echo "  测试覆盖率大小: $COVERAGE_SIZE"
    else
        echo "  测试覆盖率: 不存在"
    fi
}

# 显示后端缓存信息
info_backend() {
    log_info "后端缓存信息:"
    
    cd backend
    
    # Python 缓存大小
    PYCACHE_SIZE=$(find . -type d -name "__pycache__" -exec du -sh {} + 2>/dev/null | awk '{sum+=$1} END {print sum "K"}' || echo "0K")
    echo "  Python 缓存大小: $PYCACHE_SIZE"
    
    # pip 缓存大小
    PIP_CACHE_SIZE=$(pip cache info 2>/dev/null | grep "Cache size" | awk '{print $3 " " $4}' || echo "未知")
    echo "  pip 缓存大小: $PIP_CACHE_SIZE"
    
    # 测试覆盖率大小
    if [ -d "htmlcov" ]; then
        COVERAGE_SIZE=$(du -sh htmlcov/ | cut -f1)
        echo "  测试覆盖率大小: $COVERAGE_SIZE"
    else
        echo "  测试覆盖率: 不存在"
    fi
    
    # 测试数据库大小
    if [ -f "test.db" ]; then
        TEST_DB_SIZE=$(du -sh test.db | cut -f1)
        echo "  测试数据库大小: $TEST_DB_SIZE"
    else
        echo "  测试数据库: 不存在"
    fi
}

# 优化缓存配置
optimize_cache() {
    log_info "优化缓存配置..."
    
    # 创建 .npmrc 优化 npm 缓存
    if [ ! -f "frontend/.npmrc" ]; then
        log_info "创建 .npmrc 配置..."
        cat > frontend/.npmrc << EOF
# npm 缓存优化配置
cache-max=86400000
prefer-offline=true
audit=false
fund=false
EOF
    fi
    
    # 创建 pip.conf 优化 pip 缓存
    PIP_CONFIG_DIR="$HOME/.pip"
    if [ ! -d "$PIP_CONFIG_DIR" ]; then
        mkdir -p "$PIP_CONFIG_DIR"
    fi
    
    if [ ! -f "$PIP_CONFIG_DIR/pip.conf" ]; then
        log_info "创建 pip.conf 配置..."
        cat > "$PIP_CONFIG_DIR/pip.conf" << EOF
[global]
cache-dir = ~/.cache/pip
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
EOF
    fi
    
    log_success "缓存配置优化完成"
}

# 主函数
main() {
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    COMMAND=$1
    TARGET=${2:-all}
    
    case $COMMAND in
        "warm-up")
            case $TARGET in
                "frontend")
                    warm_up_frontend
                    ;;
                "backend")
                    warm_up_backend
                    ;;
                "all")
                    warm_up_frontend
                    warm_up_backend
                    ;;
                *)
                    log_error "无效的目标: $TARGET"
                    exit 1
                    ;;
            esac
            ;;
        "clean")
            case $TARGET in
                "frontend")
                    clean_frontend
                    ;;
                "backend")
                    clean_backend
                    ;;
                "all")
                    clean_frontend
                    clean_backend
                    ;;
                *)
                    log_error "无效的目标: $TARGET"
                    exit 1
                    ;;
            esac
            ;;
        "info")
            case $TARGET in
                "frontend")
                    info_frontend
                    ;;
                "backend")
                    info_backend
                    ;;
                "all")
                    info_frontend
                    info_backend
                    ;;
                *)
                    log_error "无效的目标: $TARGET"
                    exit 1
                    ;;
            esac
            ;;
        "optimize")
            optimize_cache
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "无效的命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
