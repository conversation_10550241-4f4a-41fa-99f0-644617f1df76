#!/bin/bash
# 柴管家项目 CI/CD 流水线验证脚本
# 验证 CI/CD 流水线的各个组件是否正确配置

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 执行检查并记录结果
run_check() {
    local check_name="$1"
    local check_command="$2"

    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

    log_info "检查: $check_name"

    if eval "$check_command" > /dev/null 2>&1; then
        log_success "✅ $check_name"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "❌ $check_name"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 验证文件存在
check_file_exists() {
    local file_path="$1"
    local description="$2"

    run_check "$description" "[ -f '$file_path' ]"
}

# 验证目录存在
check_dir_exists() {
    local dir_path="$1"
    local description="$2"

    run_check "$description" "[ -d '$dir_path' ]"
}

# 验证 YAML 文件语法
check_yaml_syntax() {
    local yaml_file="$1"
    local description="$2"

    if command -v yamllint &> /dev/null; then
        run_check "$description" "yamllint '$yaml_file'"
    else
        log_warning "yamllint 未安装，跳过 YAML 语法检查"
    fi
}

# 验证 JSON 文件语法
check_json_syntax() {
    local json_file="$1"
    local description="$2"

    # 尝试使用 python3，如果不存在则使用 python
    if command -v python3 &> /dev/null; then
        run_check "$description" "python3 -m json.tool '$json_file' > /dev/null"
    elif command -v python &> /dev/null; then
        run_check "$description" "python -m json.tool '$json_file' > /dev/null"
    else
        log_warning "Python 未安装，跳过 JSON 语法检查"
    fi
}

# 主验证函数
main_validation() {
    log_info "开始验证 CI/CD 流水线配置..."
    echo ""

    # ===== 验证 GitHub Actions 工作流 =====
    log_info "🔍 验证 GitHub Actions 工作流"

    check_dir_exists ".github/workflows" "GitHub Actions 工作流目录存在"
    check_file_exists ".github/workflows/ci.yml" "主 CI 工作流文件存在"
    check_file_exists ".github/workflows/security.yml" "安全扫描工作流文件存在"
    check_file_exists ".github/workflows/coverage.yml" "覆盖率报告工作流文件存在"
    check_file_exists ".github/workflows/notifications.yml" "通知工作流文件存在"

    # 验证工作流文件语法
    if [ -f ".github/workflows/ci.yml" ]; then
        check_yaml_syntax ".github/workflows/ci.yml" "主 CI 工作流 YAML 语法正确"
    fi

    if [ -f ".github/workflows/security.yml" ]; then
        check_yaml_syntax ".github/workflows/security.yml" "安全扫描工作流 YAML 语法正确"
    fi

    echo ""

    # ===== 验证 GitHub 配置 =====
    log_info "🔍 验证 GitHub 配置"

    check_file_exists ".github/pull_request_template.md" "PR 模板文件存在"
    check_dir_exists ".github/ISSUE_TEMPLATE" "Issue 模板目录存在"

    echo ""

    # ===== 验证代码质量配置 =====
    log_info "🔍 验证代码质量配置"

    check_file_exists ".pre-commit-config.yaml" "pre-commit 配置文件存在"
    check_file_exists "frontend/.eslintrc.js" "前端 ESLint 配置文件存在"
    check_file_exists "frontend/.prettierrc" "前端 Prettier 配置文件存在"
    check_file_exists "backend/pyproject.toml" "后端配置文件存在"

    # 验证配置文件语法
    if [ -f ".pre-commit-config.yaml" ]; then
        check_yaml_syntax ".pre-commit-config.yaml" "pre-commit 配置 YAML 语法正确"
    fi

    if [ -f "frontend/.prettierrc" ]; then
        check_json_syntax "frontend/.prettierrc" "Prettier 配置 JSON 语法正确"
    fi

    echo ""

    # ===== 验证测试配置 =====
    log_info "🔍 验证测试配置"

    check_file_exists "frontend/jest.config.js" "前端 Jest 配置文件存在"
    check_file_exists "frontend/src/setupTests.ts" "前端测试设置文件存在"
    check_file_exists "backend/tests/conftest.py" "后端测试配置文件存在"

    echo ""

    # ===== 验证依赖文件 =====
    log_info "🔍 验证依赖文件"

    check_file_exists "frontend/package.json" "前端 package.json 存在"

    # 检查锁文件 (支持 npm 和 yarn)
    if [ -f "frontend/package-lock.json" ]; then
        run_check "前端 package-lock.json 存在" "true"
    elif [ -f "frontend/yarn.lock" ]; then
        run_check "前端 yarn.lock 存在" "true"
    else
        log_error "前端锁文件不存在 (package-lock.json 或 yarn.lock)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
    check_file_exists "backend/requirements.txt" "后端生产依赖文件存在"
    check_file_exists "backend/requirements-dev.txt" "后端开发依赖文件存在"

    # 验证 JSON 文件语法
    if [ -f "frontend/package.json" ]; then
        check_json_syntax "frontend/package.json" "package.json 语法正确"
    fi

    echo ""

    # ===== 验证 Docker 配置 =====
    log_info "🔍 验证 Docker 配置"

    check_file_exists "docker-compose.yml" "Docker Compose 配置文件存在"
    check_file_exists "frontend/Dockerfile" "前端 Dockerfile 存在"
    check_file_exists "backend/Dockerfile" "后端 Dockerfile 存在"

    echo ""

    # ===== 验证脚本文件 =====
    log_info "🔍 验证脚本文件"

    check_file_exists "scripts/ci-quality-check.sh" "代码质量检查脚本存在"
    check_file_exists "scripts/ci-test-runner.sh" "测试运行脚本存在"
    check_file_exists "scripts/ci-build.sh" "构建脚本存在"
    check_file_exists "scripts/ci-cache-manager.sh" "缓存管理脚本存在"
    check_file_exists "scripts/setup-branch-protection.sh" "分支保护配置脚本存在"

    # 验证脚本可执行权限
    for script in scripts/*.sh; do
        if [ -f "$script" ]; then
            run_check "$(basename "$script") 具有执行权限" "[ -x '$script' ]"
        fi
    done

    echo ""

    # ===== 验证文档文件 =====
    log_info "🔍 验证文档文件"

    check_file_exists "README.md" "README 文件存在"
    check_file_exists "CONTRIBUTING.md" "贡献指南存在"
    check_dir_exists "docs" "文档目录存在"

    echo ""

    # ===== 功能性验证 =====
    log_info "🔍 功能性验证"

    # 验证前端依赖
    if [ -d "frontend/node_modules" ]; then
        run_check "前端依赖已安装" "[ -d 'frontend/node_modules' ]"
    else
        log_warning "前端依赖未安装，建议运行: cd frontend && npm ci"
    fi

    # 验证 Node.js 和 npm 版本
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        run_check "Node.js 已安装 ($NODE_VERSION)" "true"
    else
        log_error "Node.js 未安装"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi

    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        run_check "npm 已安装 ($NPM_VERSION)" "true"
    else
        log_error "npm 未安装"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi

    # 验证 Python 版本
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        run_check "Python 已安装 ($PYTHON_VERSION)" "true"
    elif command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version)
        run_check "Python 已安装 ($PYTHON_VERSION)" "true"
    else
        log_error "Python 未安装"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi

    # 验证 Docker
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version)
        run_check "Docker 已安装 ($DOCKER_VERSION)" "true"
    else
        log_warning "Docker 未安装，某些功能可能无法使用"
    fi

    echo ""

    # ===== 显示验证结果 =====
    log_info "📊 验证结果汇总"
    echo ""
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过: $PASSED_CHECKS"
    echo "失败: $FAILED_CHECKS"
    echo ""

    if [ $FAILED_CHECKS -eq 0 ]; then
        log_success "🎉 所有检查都通过了！CI/CD 流水线配置正确。"
        echo ""
        log_info "📋 下一步操作建议："
        echo "1. 提交所有变更到 Git 仓库"
        echo "2. 创建测试 PR 验证 CI 流水线"
        echo "3. 配置分支保护规则"
        echo "4. 设置必要的 GitHub Secrets"
        return 0
    else
        log_error "❌ 发现 $FAILED_CHECKS 个问题，请修复后重新验证。"
        echo ""
        log_info "📋 修复建议："
        echo "1. 检查缺失的文件和目录"
        echo "2. 验证配置文件语法"
        echo "3. 安装缺失的依赖"
        echo "4. 设置正确的文件权限"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "柴管家项目 CI/CD 流水线验证脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示帮助信息"
    echo "  -v, --verbose 显示详细输出"
    echo ""
    echo "此脚本将验证以下内容:"
    echo "- GitHub Actions 工作流配置"
    echo "- 代码质量工具配置"
    echo "- 测试配置"
    echo "- Docker 配置"
    echo "- 依赖文件"
    echo "- 脚本文件和权限"
    echo "- 开发环境工具"
    echo ""
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            main_validation
            ;;
        "")
            main_validation
            ;;
        *)
            log_error "无效的选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
