#!/bin/bash

# 柴管家项目数据库管理脚本
# 提供数据库备份、恢复、性能监控等功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
柴管家项目数据库管理工具

用法: $0 <命令> [选项]

命令:
    backup          备份数据库
    restore         恢复数据库
    migrate         执行数据库迁移
    seed            插入种子数据
    reset           重置数据库（清空并重新初始化）
    monitor         显示数据库性能监控
    query           执行SQL查询
    connect         连接到数据库

选项:
    --env ENV       指定环境 (dev/test/prod，默认: dev)
    --file FILE     指定备份/恢复文件
    --sql SQL       指定要执行的SQL语句
    --help, -h      显示帮助信息

示例:
    $0 backup --env dev                    # 备份开发环境数据库
    $0 restore --file backup.sql          # 恢复数据库
    $0 query --sql "SELECT * FROM users"  # 执行查询
    $0 monitor                             # 显示性能监控

EOF
}

# 获取数据库连接参数
get_db_params() {
    local env=${1:-dev}

    case $env in
        dev)
            DB_HOST="postgres"
            DB_PORT="5432"
            DB_NAME="chaiguanjia"
            DB_USER="chaiguanjia"
            DB_PASSWORD="chaiguanjia123"
            ;;
        test)
            DB_HOST="postgres"
            DB_PORT="5432"
            DB_NAME="chaiguanjia_test"
            DB_USER="chaiguanjia"
            DB_PASSWORD="chaiguanjia123"
            ;;
        prod)
            DB_HOST="postgres"
            DB_PORT="5432"
            DB_NAME="chaiguanjia_prod"
            DB_USER="chaiguanjia_prod"
            DB_PASSWORD="${POSTGRES_PASSWORD:-chaiguanjia123}"
            ;;
        *)
            log_error "未知环境: $env"
            exit 1
            ;;
    esac
}

# 检查数据库连接
check_db_connection() {
    local env=$1
    get_db_params "$env"

    log_info "检查数据库连接..."
    if docker exec chaiguanjia-postgres pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
        log_success "数据库连接正常"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 备份数据库
backup_database() {
    local env=${1:-dev}
    local backup_file=${2:-}

    get_db_params "$env"

    if [ -z "$backup_file" ]; then
        backup_file="backups/chaiguanjia_${env}_$(date +%Y%m%d_%H%M%S).sql"
    fi

    # 创建备份目录
    mkdir -p "$(dirname "$backup_file")"

    log_header "备份数据库: $DB_NAME"
    log_info "备份文件: $backup_file"

    # 执行备份
    if docker exec -e PGPASSWORD="$DB_PASSWORD" chaiguanjia-postgres pg_dump \
        -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --verbose --clean --if-exists --create \
        > "$backup_file"; then

        log_success "数据库备份完成: $backup_file"

        # 显示备份文件信息
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_info "备份文件大小: $file_size"

        return 0
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 恢复数据库
restore_database() {
    local env=${1:-dev}
    local backup_file=$2

    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi

    get_db_params "$env"

    log_header "恢复数据库: $DB_NAME"
    log_info "备份文件: $backup_file"

    # 确认操作
    echo -e "${YELLOW}⚠️  这将覆盖现有数据库内容！${NC}"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        return 0
    fi

    # 执行恢复
    if docker exec -i -e PGPASSWORD="$DB_PASSWORD" chaiguanjia-postgres psql \
        -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres \
        < "$backup_file"; then

        log_success "数据库恢复完成"
        return 0
    else
        log_error "数据库恢复失败"
        return 1
    fi
}

# 执行数据库迁移
migrate_database() {
    local env=${1:-dev}

    log_header "执行数据库迁移"

    # 检查后端容器是否运行
    if ! docker ps | grep -q chaiguanjia-backend; then
        log_error "后端容器未运行，请先启动开发环境"
        return 1
    fi

    # 执行迁移
    if docker exec chaiguanjia-backend alembic upgrade head; then
        log_success "数据库迁移完成"
        return 0
    else
        log_error "数据库迁移失败"
        return 1
    fi
}

# 插入种子数据
seed_database() {
    local env=${1:-dev}

    log_header "插入种子数据"

    # 检查后端容器是否运行
    if ! docker ps | grep -q chaiguanjia-backend; then
        log_error "后端容器未运行，请先启动开发环境"
        return 1
    fi

    # 执行种子数据脚本
    if docker exec chaiguanjia-backend python scripts/init_db.py; then
        log_success "种子数据插入完成"
        return 0
    else
        log_error "种子数据插入失败"
        return 1
    fi
}

# 重置数据库
reset_database() {
    local env=${1:-dev}

    get_db_params "$env"

    log_header "重置数据库: $DB_NAME"

    # 确认操作
    echo -e "${YELLOW}⚠️  这将删除所有数据并重新初始化数据库！${NC}"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        return 0
    fi

    # 删除数据库
    log_info "删除现有数据库..."
    docker exec chaiguanjia-postgres psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres \
        -c "DROP DATABASE IF EXISTS $DB_NAME;"

    # 重新创建数据库
    log_info "创建新数据库..."
    docker exec chaiguanjia-postgres psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres \
        -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;"

    # 执行迁移和种子数据
    migrate_database "$env"
    seed_database "$env"

    log_success "数据库重置完成"
}

# 显示数据库性能监控
monitor_database() {
    local env=${1:-dev}

    get_db_params "$env"

    log_header "数据库性能监控: $DB_NAME"

    # 数据库大小
    echo "📊 数据库大小:"
    docker exec -e PGPASSWORD="$DB_PASSWORD" chaiguanjia-postgres psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME')) as database_size;"

    echo ""

    # 表大小
    echo "📋 表大小统计:"
    docker exec -e PGPASSWORD="$DB_PASSWORD" chaiguanjia-postgres psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
            FROM pg_tables
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            LIMIT 10;"

    echo ""

    # 连接统计
    echo "🔗 连接统计:"
    docker exec -e PGPASSWORD="$DB_PASSWORD" chaiguanjia-postgres psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active';"

    echo ""

    # 慢查询
    echo "🐌 慢查询统计:"
    docker exec -e PGPASSWORD="$DB_PASSWORD" chaiguanjia-postgres psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "SELECT query, calls, total_time, mean_time
            FROM pg_stat_statements
            ORDER BY mean_time DESC
            LIMIT 5;" 2>/dev/null || echo "  pg_stat_statements扩展未启用"
}

# 执行SQL查询
execute_query() {
    local env=${1:-dev}
    local sql=$2

    if [ -z "$sql" ]; then
        log_error "请提供SQL语句"
        return 1
    fi

    get_db_params "$env"

    log_header "执行SQL查询"
    log_info "SQL: $sql"

    docker exec -e PGPASSWORD="$DB_PASSWORD" chaiguanjia-postgres psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "$sql"
}

# 连接到数据库
connect_database() {
    local env=${1:-dev}

    get_db_params "$env"

    log_header "连接到数据库: $DB_NAME"
    log_info "使用 \\q 退出数据库连接"

    docker exec -it -e PGPASSWORD="$DB_PASSWORD" chaiguanjia-postgres psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME"
}

# 主函数
main() {
    local command=""
    local env="dev"
    local file=""
    local sql=""

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            backup|restore|migrate|seed|reset|monitor|query|connect)
                command="$1"
                shift
                ;;
            --env)
                env="$2"
                shift 2
                ;;
            --file)
                file="$2"
                shift 2
                ;;
            --sql)
                sql="$2"
                shift 2
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 检查命令
    if [ -z "$command" ]; then
        log_error "请指定命令"
        show_help
        exit 1
    fi

    # 检查数据库连接
    if ! check_db_connection "$env"; then
        log_error "请先启动数据库服务"
        exit 1
    fi

    # 执行命令
    case $command in
        backup)
            backup_database "$env" "$file"
            ;;
        restore)
            if [ -z "$file" ]; then
                log_error "请指定备份文件 --file"
                exit 1
            fi
            restore_database "$env" "$file"
            ;;
        migrate)
            migrate_database "$env"
            ;;
        seed)
            seed_database "$env"
            ;;
        reset)
            reset_database "$env"
            ;;
        monitor)
            monitor_database "$env"
            ;;
        query)
            if [ -z "$sql" ]; then
                log_error "请指定SQL语句 --sql"
                exit 1
            fi
            execute_query "$env" "$sql"
            ;;
        connect)
            connect_database "$env"
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
