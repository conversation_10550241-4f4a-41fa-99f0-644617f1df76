#!/bin/bash

# 柴管家项目健康检查脚本
# 检查所有服务的健康状态和连通性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 检查服务是否运行
check_service_running() {
    local service_name=$1
    local container_name=$2
    
    if docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        log_success "$service_name 容器正在运行"
        return 0
    else
        log_error "$service_name 容器未运行"
        return 1
    fi
}

# 检查HTTP服务健康状态
check_http_health() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_response "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        log_success "$service_name HTTP健康检查通过 ($url)"
        return 0
    else
        log_error "$service_name HTTP健康检查失败 ($url) - 状态码: $response"
        if [ -f /tmp/health_response ]; then
            echo "响应内容: $(cat /tmp/health_response)"
        fi
        return 1
    fi
}

# 检查TCP端口连通性
check_tcp_port() {
    local service_name=$1
    local host=$2
    local port=$3
    
    if timeout 5 bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        log_success "$service_name TCP端口连通 ($host:$port)"
        return 0
    else
        log_error "$service_name TCP端口不通 ($host:$port)"
        return 1
    fi
}

# 检查数据库连接
check_database() {
    log_header "检查PostgreSQL数据库"
    
    # 检查容器状态
    if ! check_service_running "PostgreSQL" "chaiguanjia-postgres"; then
        return 1
    fi
    
    # 检查TCP连接
    if ! check_tcp_port "PostgreSQL" "localhost" "5432"; then
        return 1
    fi
    
    # 检查数据库连接
    if docker exec chaiguanjia-postgres pg_isready -U chaiguanjia -d chaiguanjia >/dev/null 2>&1; then
        log_success "PostgreSQL数据库连接正常"
    else
        log_error "PostgreSQL数据库连接失败"
        return 1
    fi
    
    # 检查数据库版本
    local db_version=$(docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia -t -c "SELECT version();" 2>/dev/null | head -1 | xargs)
    if [ -n "$db_version" ]; then
        log_info "数据库版本: $db_version"
    fi
    
    return 0
}

# 检查Redis缓存
check_redis() {
    log_header "检查Redis缓存"
    
    # 检查容器状态
    if ! check_service_running "Redis" "chaiguanjia-redis"; then
        return 1
    fi
    
    # 检查TCP连接
    if ! check_tcp_port "Redis" "localhost" "6379"; then
        return 1
    fi
    
    # 检查Redis连接
    if docker exec chaiguanjia-redis redis-cli ping >/dev/null 2>&1; then
        log_success "Redis缓存连接正常"
    else
        log_error "Redis缓存连接失败"
        return 1
    fi
    
    # 检查Redis信息
    local redis_info=$(docker exec chaiguanjia-redis redis-cli info server 2>/dev/null | grep "redis_version" | cut -d: -f2 | tr -d '\r')
    if [ -n "$redis_info" ]; then
        log_info "Redis版本: $redis_info"
    fi
    
    return 0
}

# 检查RabbitMQ消息队列
check_rabbitmq() {
    log_header "检查RabbitMQ消息队列"
    
    # 检查容器状态
    if ! check_service_running "RabbitMQ" "chaiguanjia-rabbitmq"; then
        return 1
    fi
    
    # 检查TCP连接
    if ! check_tcp_port "RabbitMQ" "localhost" "5672"; then
        return 1
    fi
    
    # 检查管理界面
    if ! check_tcp_port "RabbitMQ Management" "localhost" "15672"; then
        return 1
    fi
    
    # 检查RabbitMQ状态
    if docker exec chaiguanjia-rabbitmq rabbitmq-diagnostics ping >/dev/null 2>&1; then
        log_success "RabbitMQ消息队列连接正常"
    else
        log_error "RabbitMQ消息队列连接失败"
        return 1
    fi
    
    return 0
}

# 检查后端API
check_backend() {
    log_header "检查后端API服务"
    
    # 检查容器状态
    if ! check_service_running "Backend API" "chaiguanjia-backend"; then
        return 1
    fi
    
    # 检查HTTP健康状态
    if ! check_http_health "Backend API" "http://localhost:8000/health"; then
        return 1
    fi
    
    # 检查API文档
    if ! check_http_health "API文档" "http://localhost:8000/docs"; then
        log_warning "API文档访问失败，但不影响核心功能"
    fi
    
    # 获取API详细健康信息
    local health_info=$(curl -s "http://localhost:8000/health" 2>/dev/null)
    if [ -n "$health_info" ]; then
        echo "$health_info" | python3 -m json.tool 2>/dev/null | head -20
    fi
    
    return 0
}

# 检查前端应用
check_frontend() {
    log_header "检查前端应用"
    
    # 检查容器状态
    if ! check_service_running "Frontend" "chaiguanjia-frontend"; then
        return 1
    fi
    
    # 检查HTTP服务
    if ! check_http_health "Frontend" "http://localhost:5173"; then
        return 1
    fi
    
    return 0
}

# 检查Celery服务
check_celery() {
    log_header "检查Celery异步任务"
    
    # 检查Worker
    if check_service_running "Celery Worker" "chaiguanjia-celery-worker"; then
        log_success "Celery Worker运行正常"
    else
        log_warning "Celery Worker未运行"
    fi
    
    # 检查Beat
    if check_service_running "Celery Beat" "chaiguanjia-celery-beat"; then
        log_success "Celery Beat运行正常"
    else
        log_warning "Celery Beat未运行"
    fi
    
    # 检查Flower监控
    if check_service_running "Celery Flower" "chaiguanjia-celery-flower"; then
        if check_http_health "Celery Flower" "http://localhost:5555"; then
            log_success "Celery Flower监控正常"
        fi
    else
        log_warning "Celery Flower未运行"
    fi
    
    return 0
}

# 显示系统资源使用情况
show_system_resources() {
    log_header "系统资源使用情况"
    
    echo "Docker容器状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep chaiguanjia || echo "  无运行中的容器"
    
    echo ""
    echo "Docker资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -10
    
    echo ""
    echo "磁盘使用情况:"
    docker system df
}

# 生成健康报告
generate_health_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="/tmp/chaiguanjia_health_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "柴管家项目健康检查报告"
        echo "生成时间: $timestamp"
        echo "========================================"
        echo ""
        
        # 重新运行所有检查并记录结果
        main --quiet
        
    } > "$report_file"
    
    log_info "健康报告已生成: $report_file"
}

# 主函数
main() {
    local quiet_mode=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --quiet|-q)
                quiet_mode=true
                shift
                ;;
            --report|-r)
                generate_health_report
                exit 0
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --quiet, -q    静默模式，只显示错误"
                echo "  --report, -r   生成健康报告"
                echo "  --help, -h     显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    if [ "$quiet_mode" = false ]; then
        echo -e "${CYAN}"
        echo "🏥 柴管家项目健康检查"
        echo "========================================"
        echo -e "${NC}"
    fi
    
    local failed_checks=0
    
    # 执行所有健康检查
    check_database || ((failed_checks++))
    echo ""
    
    check_redis || ((failed_checks++))
    echo ""
    
    check_rabbitmq || ((failed_checks++))
    echo ""
    
    check_backend || ((failed_checks++))
    echo ""
    
    check_frontend || ((failed_checks++))
    echo ""
    
    check_celery || ((failed_checks++))
    echo ""
    
    if [ "$quiet_mode" = false ]; then
        show_system_resources
        echo ""
    fi
    
    # 显示总结
    if [ $failed_checks -eq 0 ]; then
        log_success "🎉 所有服务健康检查通过！"
        exit 0
    else
        log_error "❌ $failed_checks 个服务检查失败"
        log_info "请检查失败的服务并查看日志: docker-compose logs [service-name]"
        exit 1
    fi
}

# 执行主函数
main "$@"
