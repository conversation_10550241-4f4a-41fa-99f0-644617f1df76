#!/bin/bash

# 柴管家项目日志管理脚本
# 提供日志查看、搜索、过滤、聚合等功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
柴管家项目日志管理工具

用法: $0 <命令> [选项]

命令:
    view            查看服务日志
    search          搜索日志内容
    filter          过滤日志级别
    aggregate       聚合多服务日志
    export          导出日志到文件
    clean           清理旧日志
    monitor         实时监控日志

选项:
    --service SVC   指定服务名称 (backend/frontend/postgres/redis/rabbitmq/celery-worker/celery-beat/nginx)
    --level LEVEL   指定日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL)
    --keyword KEY   搜索关键字
    --since TIME    开始时间 (如: 1h, 30m, 2024-01-01)
    --until TIME    结束时间
    --lines NUM     显示行数 (默认: 100)
    --follow        实时跟踪日志
    --output FILE   输出到文件
    --help, -h      显示帮助信息

示例:
    $0 view --service backend --lines 50          # 查看后端最近50行日志
    $0 search --keyword "error" --since 1h        # 搜索最近1小时的错误
    $0 filter --level ERROR --service backend     # 过滤后端错误日志
    $0 aggregate --since 30m                      # 聚合最近30分钟所有服务日志
    $0 monitor --service backend --follow         # 实时监控后端日志

EOF
}

# 获取所有服务列表
get_services() {
    docker-compose ps --services 2>/dev/null | grep -E "(backend|frontend|postgres|redis|rabbitmq|celery|nginx)" || echo ""
}

# 检查服务是否存在
check_service() {
    local service=$1
    local services=$(get_services)
    
    if [ -z "$service" ]; then
        return 0  # 允许空服务（查看所有服务）
    fi
    
    if echo "$services" | grep -q "^${service}$"; then
        return 0
    else
        log_error "服务不存在: $service"
        log_info "可用服务: $(echo $services | tr '\n' ' ')"
        return 1
    fi
}

# 格式化时间参数
format_time() {
    local time_str=$1
    
    if [ -z "$time_str" ]; then
        echo ""
        return
    fi
    
    # 如果是相对时间（如1h, 30m），转换为绝对时间
    if [[ "$time_str" =~ ^[0-9]+[smhd]$ ]]; then
        case "${time_str: -1}" in
            s) seconds="${time_str%s}" ;;
            m) seconds=$((${time_str%m} * 60)) ;;
            h) seconds=$((${time_str%h} * 3600)) ;;
            d) seconds=$((${time_str%d} * 86400)) ;;
        esac
        date -d "@$(($(date +%s) - seconds))" '+%Y-%m-%dT%H:%M:%S'
    else
        # 假设是绝对时间格式
        echo "$time_str"
    fi
}

# 查看服务日志
view_logs() {
    local service=$1
    local lines=${2:-100}
    local follow=${3:-false}
    local since=$4
    local until=$5
    
    if ! check_service "$service"; then
        return 1
    fi
    
    log_header "查看服务日志"
    
    local docker_args=()
    
    # 添加时间过滤
    if [ -n "$since" ]; then
        local since_formatted=$(format_time "$since")
        if [ -n "$since_formatted" ]; then
            docker_args+=("--since" "$since_formatted")
        fi
    fi
    
    if [ -n "$until" ]; then
        local until_formatted=$(format_time "$until")
        if [ -n "$until_formatted" ]; then
            docker_args+=("--until" "$until_formatted")
        fi
    fi
    
    # 添加行数限制
    docker_args+=("--tail" "$lines")
    
    # 添加实时跟踪
    if [ "$follow" = true ]; then
        docker_args+=("-f")
    fi
    
    # 执行日志查看
    if [ -n "$service" ]; then
        log_info "查看 $service 服务日志 (最近 $lines 行)"
        docker-compose logs "${docker_args[@]}" "$service"
    else
        log_info "查看所有服务日志 (最近 $lines 行)"
        docker-compose logs "${docker_args[@]}"
    fi
}

# 搜索日志内容
search_logs() {
    local keyword=$1
    local service=$2
    local since=$3
    local until=$4
    local lines=${5:-100}
    
    if [ -z "$keyword" ]; then
        log_error "请提供搜索关键字"
        return 1
    fi
    
    if ! check_service "$service"; then
        return 1
    fi
    
    log_header "搜索日志内容: $keyword"
    
    local docker_args=()
    
    # 添加时间过滤
    if [ -n "$since" ]; then
        local since_formatted=$(format_time "$since")
        if [ -n "$since_formatted" ]; then
            docker_args+=("--since" "$since_formatted")
        fi
    fi
    
    if [ -n "$until" ]; then
        local until_formatted=$(format_time "$until")
        if [ -n "$until_formatted" ]; then
            docker_args+=("--until" "$until_formatted")
        fi
    fi
    
    docker_args+=("--tail" "$lines")
    
    # 执行搜索
    if [ -n "$service" ]; then
        log_info "在 $service 服务中搜索: $keyword"
        docker-compose logs "${docker_args[@]}" "$service" 2>&1 | grep -i --color=always "$keyword" || log_warning "未找到匹配的日志"
    else
        log_info "在所有服务中搜索: $keyword"
        docker-compose logs "${docker_args[@]}" 2>&1 | grep -i --color=always "$keyword" || log_warning "未找到匹配的日志"
    fi
}

# 过滤日志级别
filter_logs() {
    local level=$1
    local service=$2
    local since=$3
    local until=$4
    local lines=${5:-100}
    
    if [ -z "$level" ]; then
        log_error "请提供日志级别"
        return 1
    fi
    
    if ! check_service "$service"; then
        return 1
    fi
    
    log_header "过滤日志级别: $level"
    
    local docker_args=()
    
    # 添加时间过滤
    if [ -n "$since" ]; then
        local since_formatted=$(format_time "$since")
        if [ -n "$since_formatted" ]; then
            docker_args+=("--since" "$since_formatted")
        fi
    fi
    
    if [ -n "$until" ]; then
        local until_formatted=$(format_time "$until")
        if [ -n "$until_formatted" ]; then
            docker_args+=("--until" "$until_formatted")
        fi
    fi
    
    docker_args+=("--tail" "$lines")
    
    # 构建级别过滤正则表达式
    local level_pattern=""
    case "$level" in
        DEBUG|debug)
            level_pattern="DEBUG|debug"
            ;;
        INFO|info)
            level_pattern="INFO|info"
            ;;
        WARNING|warning|WARN|warn)
            level_pattern="WARNING|warning|WARN|warn"
            ;;
        ERROR|error)
            level_pattern="ERROR|error"
            ;;
        CRITICAL|critical|FATAL|fatal)
            level_pattern="CRITICAL|critical|FATAL|fatal"
            ;;
        *)
            level_pattern="$level"
            ;;
    esac
    
    # 执行过滤
    if [ -n "$service" ]; then
        log_info "过滤 $service 服务的 $level 级别日志"
        docker-compose logs "${docker_args[@]}" "$service" 2>&1 | grep -E --color=always "$level_pattern" || log_warning "未找到匹配的日志"
    else
        log_info "过滤所有服务的 $level 级别日志"
        docker-compose logs "${docker_args[@]}" 2>&1 | grep -E --color=always "$level_pattern" || log_warning "未找到匹配的日志"
    fi
}

# 聚合多服务日志
aggregate_logs() {
    local since=$1
    local until=$2
    local lines=${3:-100}
    
    log_header "聚合多服务日志"
    
    local services=$(get_services)
    if [ -z "$services" ]; then
        log_error "没有找到运行中的服务"
        return 1
    fi
    
    local docker_args=()
    
    # 添加时间过滤
    if [ -n "$since" ]; then
        local since_formatted=$(format_time "$since")
        if [ -n "$since_formatted" ]; then
            docker_args+=("--since" "$since_formatted")
        fi
    fi
    
    if [ -n "$until" ]; then
        local until_formatted=$(format_time "$until")
        if [ -n "$until_formatted" ]; then
            docker_args+=("--until" "$until_formatted")
        fi
    fi
    
    docker_args+=("--tail" "$lines")
    
    log_info "聚合服务: $(echo $services | tr '\n' ' ')"
    
    # 聚合所有服务日志并按时间排序
    docker-compose logs "${docker_args[@]}" | sort -k1,2
}

# 导出日志到文件
export_logs() {
    local output_file=$1
    local service=$2
    local since=$3
    local until=$4
    local lines=${5:-1000}
    
    if [ -z "$output_file" ]; then
        output_file="logs/chaiguanjia_logs_$(date +%Y%m%d_%H%M%S).log"
    fi
    
    # 创建输出目录
    mkdir -p "$(dirname "$output_file")"
    
    log_header "导出日志到文件: $output_file"
    
    local docker_args=()
    
    # 添加时间过滤
    if [ -n "$since" ]; then
        local since_formatted=$(format_time "$since")
        if [ -n "$since_formatted" ]; then
            docker_args+=("--since" "$since_formatted")
        fi
    fi
    
    if [ -n "$until" ]; then
        local until_formatted=$(format_time "$until")
        if [ -n "$until_formatted" ]; then
            docker_args+=("--until" "$until_formatted")
        fi
    fi
    
    docker_args+=("--tail" "$lines")
    
    # 导出日志
    if [ -n "$service" ]; then
        log_info "导出 $service 服务日志"
        docker-compose logs "${docker_args[@]}" "$service" > "$output_file"
    else
        log_info "导出所有服务日志"
        docker-compose logs "${docker_args[@]}" > "$output_file"
    fi
    
    local file_size=$(du -h "$output_file" | cut -f1)
    log_success "日志导出完成: $output_file (大小: $file_size)"
}

# 清理旧日志
clean_logs() {
    local days=${1:-7}
    
    log_header "清理旧日志"
    
    # 确认操作
    echo -e "${YELLOW}⚠️  这将清理 $days 天前的容器日志！${NC}"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        return 0
    fi
    
    # 清理Docker日志
    log_info "清理Docker容器日志..."
    docker system prune -f --filter "until=${days}d"
    
    # 清理本地日志文件
    if [ -d "logs" ]; then
        log_info "清理本地日志文件..."
        find logs -name "*.log" -mtime +$days -delete 2>/dev/null || true
    fi
    
    log_success "日志清理完成"
}

# 实时监控日志
monitor_logs() {
    local service=$1
    local keyword=$2
    local level=$3
    
    if ! check_service "$service"; then
        return 1
    fi
    
    log_header "实时监控日志"
    
    if [ -n "$service" ]; then
        log_info "监控 $service 服务日志 (Ctrl+C 停止)"
    else
        log_info "监控所有服务日志 (Ctrl+C 停止)"
    fi
    
    # 构建过滤管道
    local filter_cmd="cat"
    
    if [ -n "$keyword" ]; then
        filter_cmd="$filter_cmd | grep -i --color=always '$keyword'"
    fi
    
    if [ -n "$level" ]; then
        local level_pattern=""
        case "$level" in
            DEBUG|debug) level_pattern="DEBUG|debug" ;;
            INFO|info) level_pattern="INFO|info" ;;
            WARNING|warning|WARN|warn) level_pattern="WARNING|warning|WARN|warn" ;;
            ERROR|error) level_pattern="ERROR|error" ;;
            CRITICAL|critical|FATAL|fatal) level_pattern="CRITICAL|critical|FATAL|fatal" ;;
            *) level_pattern="$level" ;;
        esac
        filter_cmd="$filter_cmd | grep -E --color=always '$level_pattern'"
    fi
    
    # 执行监控
    if [ -n "$service" ]; then
        eval "docker-compose logs -f --tail=10 '$service' | $filter_cmd"
    else
        eval "docker-compose logs -f --tail=10 | $filter_cmd"
    fi
}

# 主函数
main() {
    local command=""
    local service=""
    local level=""
    local keyword=""
    local since=""
    local until=""
    local lines="100"
    local follow=false
    local output=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            view|search|filter|aggregate|export|clean|monitor)
                command="$1"
                shift
                ;;
            --service)
                service="$2"
                shift 2
                ;;
            --level)
                level="$2"
                shift 2
                ;;
            --keyword)
                keyword="$2"
                shift 2
                ;;
            --since)
                since="$2"
                shift 2
                ;;
            --until)
                until="$2"
                shift 2
                ;;
            --lines)
                lines="$2"
                shift 2
                ;;
            --follow)
                follow=true
                shift
                ;;
            --output)
                output="$2"
                shift 2
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查命令
    if [ -z "$command" ]; then
        log_error "请指定命令"
        show_help
        exit 1
    fi
    
    # 执行命令
    case $command in
        view)
            view_logs "$service" "$lines" "$follow" "$since" "$until"
            ;;
        search)
            if [ -z "$keyword" ]; then
                log_error "请指定搜索关键字 --keyword"
                exit 1
            fi
            search_logs "$keyword" "$service" "$since" "$until" "$lines"
            ;;
        filter)
            if [ -z "$level" ]; then
                log_error "请指定日志级别 --level"
                exit 1
            fi
            filter_logs "$level" "$service" "$since" "$until" "$lines"
            ;;
        aggregate)
            aggregate_logs "$since" "$until" "$lines"
            ;;
        export)
            export_logs "$output" "$service" "$since" "$until" "$lines"
            ;;
        clean)
            clean_logs "7"
            ;;
        monitor)
            monitor_logs "$service" "$keyword" "$level"
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
