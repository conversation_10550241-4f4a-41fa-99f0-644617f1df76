#!/bin/bash

# 柴管家项目 - 无代理Docker构建脚本
# 解决Docker构建时的网络连接问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，请启动Docker Desktop"
        exit 1
    fi
}

# 构建镜像（无代理）
build_without_proxy() {
    local service=$1
    
    log_info "构建 $service 服务（无代理模式）..."
    
    # 临时清除代理环境变量
    unset http_proxy
    unset https_proxy
    unset HTTP_PROXY
    unset HTTPS_PROXY
    unset all_proxy
    unset ALL_PROXY
    
    # 使用 --build-arg 明确禁用代理
    docker-compose build \
        --build-arg http_proxy= \
        --build-arg https_proxy= \
        --build-arg HTTP_PROXY= \
        --build-arg HTTPS_PROXY= \
        --build-arg no_proxy=localhost,127.0.0.1 \
        --build-arg NO_PROXY=localhost,127.0.0.1 \
        "$service"
}

# 主函数
main() {
    local service=${1:-"backend"}
    
    log_info "开始无代理构建 Docker 镜像..."
    
    check_docker
    
    case $service in
        backend)
            build_without_proxy "backend"
            ;;
        frontend)
            build_without_proxy "frontend"
            ;;
        celery-worker)
            build_without_proxy "celery-worker"
            ;;
        celery-beat)
            build_without_proxy "celery-beat"
            ;;
        all)
            log_info "构建所有服务..."
            build_without_proxy "backend"
            build_without_proxy "frontend"
            build_without_proxy "celery-worker"
            build_without_proxy "celery-beat"
            ;;
        *)
            log_error "未知服务: $service"
            echo "用法: $0 [backend|frontend|celery-worker|celery-beat|all]"
            exit 1
            ;;
    esac
    
    log_success "构建完成！"
}

# 执行主函数
main "$@"
