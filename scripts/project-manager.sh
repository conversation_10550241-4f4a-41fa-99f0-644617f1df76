#!/bin/bash

# 柴管家项目 - 项目管理脚本
# 统一管理整个项目的启动、停止、构建等操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 检查Docker环境
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，请启动Docker Desktop"
        exit 1
    fi
    log_success "Docker环境检查通过"
}

# 检查Node.js环境
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 18+"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2)
    local major_version=$(echo $node_version | cut -d'.' -f1)
    
    if [ "$major_version" -lt 18 ]; then
        log_error "Node.js版本过低，需要18+，当前版本: $node_version"
        exit 1
    fi
    
    log_success "Node.js环境检查通过: v$node_version"
}

# 启动基础设施服务
start_infrastructure() {
    log_header "启动基础设施服务"
    
    check_docker
    
    log_info "启动PostgreSQL、Redis、RabbitMQ..."
    docker-compose up postgres redis rabbitmq -d
    
    log_info "等待服务启动..."
    sleep 5
    
    # 检查服务状态
    if docker-compose ps postgres | grep -q "Up"; then
        log_success "PostgreSQL 启动成功"
    else
        log_error "PostgreSQL 启动失败"
        return 1
    fi
    
    if docker-compose ps redis | grep -q "Up"; then
        log_success "Redis 启动成功"
    else
        log_error "Redis 启动失败"
        return 1
    fi
    
    if docker-compose ps rabbitmq | grep -q "Up"; then
        log_success "RabbitMQ 启动成功"
    else
        log_error "RabbitMQ 启动失败"
        return 1
    fi
}

# 启动开发环境
start_dev() {
    log_header "启动完整开发环境"
    
    # 启动基础设施
    start_infrastructure
    
    # 启动前端开发服务器
    log_info "启动前端开发服务器..."
    ./scripts/frontend-manager.sh dev &
    FRONTEND_PID=$!
    
    log_info "等待前端服务器启动..."
    sleep 3
    
    log_success "开发环境启动完成！"
    echo ""
    echo "🌐 前端地址: http://localhost:5173"
    echo "🗄️  数据库: localhost:5432 (chaiguanjia/chaiguanjia123)"
    echo "🔴 Redis: localhost:6379"
    echo "🐰 RabbitMQ: localhost:15672 (guest/guest)"
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    trap 'stop_dev' INT
    wait $FRONTEND_PID
}

# 停止开发环境
stop_dev() {
    log_header "停止开发环境"
    
    log_info "停止前端服务器..."
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    log_info "停止基础设施服务..."
    docker-compose down
    
    log_success "开发环境已停止"
    exit 0
}

# 启动生产环境
start_prod() {
    log_header "启动生产环境"
    
    check_docker
    
    # 构建镜像
    log_info "构建生产镜像..."
    ./scripts/docker-build-no-proxy.sh all
    
    # 启动所有服务
    log_info "启动所有生产服务..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    
    log_success "生产环境启动完成！"
    echo ""
    echo "🌐 应用地址: http://localhost:8000"
    echo "🗄️  数据库: localhost:5432"
    echo "🔴 Redis: localhost:6379"
    echo "🐰 RabbitMQ: localhost:15672"
    echo "🌸 Flower: localhost:5555"
    echo "🔧 pgAdmin: localhost:5050"
}

# 停止所有服务
stop_all() {
    log_header "停止所有服务"
    
    log_info "停止Docker容器..."
    docker-compose down
    
    log_info "停止前端开发服务器..."
    pkill -f "vite" 2>/dev/null || true
    
    log_success "所有服务已停止"
}

# 查看服务状态
show_status() {
    log_header "服务状态"
    
    echo "📦 Docker容器状态:"
    docker-compose ps
    
    echo ""
    echo "🌐 网络端口状态:"
    echo "  5173: 前端开发服务器"
    echo "  8000: 后端API服务器"
    echo "  5432: PostgreSQL数据库"
    echo "  6379: Redis缓存"
    echo "  5672: RabbitMQ消息队列"
    echo "  15672: RabbitMQ管理界面"
    echo "  5555: Celery Flower监控"
    echo "  5050: pgAdmin数据库管理"
    
    echo ""
    echo "🔍 端口占用检查:"
    for port in 5173 8000 5432 6379 5672 15672 5555 5050; do
        if lsof -i :$port >/dev/null 2>&1; then
            echo "  ✅ $port: 已占用"
        else
            echo "  ❌ $port: 未占用"
        fi
    done
}

# 重启服务
restart_service() {
    local service=${1:-all}
    
    log_header "重启服务: $service"
    
    if [ "$service" = "all" ]; then
        stop_all
        sleep 2
        start_dev
    else
        docker-compose restart "$service"
        log_success "服务 $service 重启完成"
    fi
}

# 查看日志
show_logs() {
    local service=${1:-}
    
    if [ -z "$service" ]; then
        log_info "显示所有服务日志..."
        docker-compose logs -f
    else
        log_info "显示服务 $service 的日志..."
        docker-compose logs -f "$service"
    fi
}

# 显示帮助信息
show_help() {
    echo "柴管家项目管理脚本"
    echo ""
    echo "用法: $0 <command> [options]"
    echo ""
    echo "命令:"
    echo "  dev              启动完整开发环境"
    echo "  prod             启动生产环境"
    echo "  stop             停止所有服务"
    echo "  status           查看服务状态"
    echo "  restart [service] 重启服务"
    echo "  logs [service]   查看日志"
    echo "  infra            仅启动基础设施服务"
    echo "  help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev           # 启动开发环境"
    echo "  $0 prod          # 启动生产环境"
    echo "  $0 restart postgres  # 重启PostgreSQL"
    echo "  $0 logs backend  # 查看后端日志"
}

# 主函数
main() {
    local command=${1:-help}
    
    case $command in
        dev)
            start_dev
            ;;
        prod)
            start_prod
            ;;
        stop)
            stop_all
            ;;
        status)
            show_status
            ;;
        restart)
            restart_service "$2"
            ;;
        logs)
            show_logs "$2"
            ;;
        infra)
            start_infrastructure
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
