#!/bin/bash
# 柴管家项目分支保护配置脚本
# 配置 GitHub 分支保护规则

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必需的环境变量
check_requirements() {
    if [ -z "$GITHUB_TOKEN" ]; then
        log_error "GITHUB_TOKEN 环境变量未设置"
        log_info "请设置 GitHub Personal Access Token:"
        log_info "export GITHUB_TOKEN=your_token_here"
        exit 1
    fi
    
    if [ -z "$GITHUB_REPOSITORY" ]; then
        log_error "GITHUB_REPOSITORY 环境变量未设置"
        log_info "请设置仓库名称 (格式: owner/repo):"
        log_info "export GITHUB_REPOSITORY=owner/repo"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装"
        log_info "请安装 jq: brew install jq (macOS) 或 apt-get install jq (Ubuntu)"
        exit 1
    fi
}

# 配置主分支保护
setup_main_branch_protection() {
    log_info "配置主分支 (main) 保护规则..."
    
    local protection_config='{
        "required_status_checks": {
            "strict": true,
            "contexts": [
                "代码质量检查 (frontend)",
                "代码质量检查 (backend)",
                "自动化测试 (frontend)",
                "自动化测试 (backend)",
                "构建和打包 (frontend)",
                "构建和打包 (backend)",
                "CI 流水线成功"
            ]
        },
        "enforce_admins": false,
        "required_pull_request_reviews": {
            "required_approving_review_count": 1,
            "dismiss_stale_reviews": true,
            "require_code_owner_reviews": false,
            "require_last_push_approval": false
        },
        "restrictions": null,
        "allow_force_pushes": false,
        "allow_deletions": false,
        "block_creations": false,
        "required_conversation_resolution": true
    }'
    
    local response=$(curl -s -w "%{http_code}" \
        -X PUT \
        -H "Authorization: token $GITHUB_TOKEN" \
        -H "Accept: application/vnd.github.v3+json" \
        -d "$protection_config" \
        "https://api.github.com/repos/$GITHUB_REPOSITORY/branches/main/protection")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "主分支保护规则配置成功"
    else
        log_error "主分支保护规则配置失败 (HTTP $http_code)"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# 配置开发分支保护
setup_develop_branch_protection() {
    log_info "配置开发分支 (develop) 保护规则..."
    
    local protection_config='{
        "required_status_checks": {
            "strict": false,
            "contexts": [
                "代码质量检查 (frontend)",
                "代码质量检查 (backend)",
                "自动化测试 (frontend)",
                "自动化测试 (backend)"
            ]
        },
        "enforce_admins": false,
        "required_pull_request_reviews": {
            "required_approving_review_count": 1,
            "dismiss_stale_reviews": false,
            "require_code_owner_reviews": false,
            "require_last_push_approval": false
        },
        "restrictions": null,
        "allow_force_pushes": false,
        "allow_deletions": false,
        "block_creations": false,
        "required_conversation_resolution": false
    }'
    
    local response=$(curl -s -w "%{http_code}" \
        -X PUT \
        -H "Authorization: token $GITHUB_TOKEN" \
        -H "Accept: application/vnd.github.v3+json" \
        -d "$protection_config" \
        "https://api.github.com/repos/$GITHUB_REPOSITORY/branches/develop/protection")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "开发分支保护规则配置成功"
    else
        log_warning "开发分支保护规则配置失败 (HTTP $http_code)"
        log_info "这可能是因为 develop 分支不存在，请先创建该分支"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    fi
}

# 检查分支保护状态
check_branch_protection() {
    local branch=$1
    log_info "检查 $branch 分支保护状态..."
    
    local response=$(curl -s -w "%{http_code}" \
        -H "Authorization: token $GITHUB_TOKEN" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/repos/$GITHUB_REPOSITORY/branches/$branch/protection")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "$branch 分支已启用保护"
        echo "$body" | jq '{
            required_status_checks: .required_status_checks.contexts,
            required_reviews: .required_pull_request_reviews.required_approving_review_count,
            enforce_admins: .enforce_admins,
            allow_force_pushes: .allow_force_pushes,
            allow_deletions: .allow_deletions
        }' 2>/dev/null || echo "无法解析保护规则详情"
    elif [ "$http_code" = "404" ]; then
        log_warning "$branch 分支未启用保护或分支不存在"
    else
        log_error "检查 $branch 分支保护状态失败 (HTTP $http_code)"
        echo "$body"
    fi
}

# 显示帮助信息
show_help() {
    echo "柴管家项目分支保护配置脚本"
    echo ""
    echo "用法: $0 <命令>"
    echo ""
    echo "命令:"
    echo "  setup-main      配置主分支保护规则"
    echo "  setup-develop   配置开发分支保护规则"
    echo "  setup-all       配置所有分支保护规则"
    echo "  check-main      检查主分支保护状态"
    echo "  check-develop   检查开发分支保护状态"
    echo "  check-all       检查所有分支保护状态"
    echo ""
    echo "环境变量:"
    echo "  GITHUB_TOKEN       GitHub Personal Access Token (必需)"
    echo "  GITHUB_REPOSITORY  仓库名称，格式: owner/repo (必需)"
    echo ""
    echo "示例:"
    echo "  export GITHUB_TOKEN=ghp_xxxxxxxxxxxx"
    echo "  export GITHUB_REPOSITORY=owner/chaiguanjia"
    echo "  $0 setup-all"
    echo ""
}

# 主函数
main() {
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    check_requirements
    
    COMMAND=$1
    
    case $COMMAND in
        "setup-main")
            setup_main_branch_protection
            ;;
        "setup-develop")
            setup_develop_branch_protection
            ;;
        "setup-all")
            setup_main_branch_protection
            setup_develop_branch_protection
            ;;
        "check-main")
            check_branch_protection "main"
            ;;
        "check-develop")
            check_branch_protection "develop"
            ;;
        "check-all")
            check_branch_protection "main"
            echo ""
            check_branch_protection "develop"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "无效的命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
