#!/bin/bash

# 柴管家项目环境管理脚本
# 提供环境重置、清理、备份等功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
柴管家项目环境管理工具

用法: $0 <命令> [选项]

命令:
    clean           清理所有容器和资源
    reset           重置开发环境（清理后重新启动）
    backup          备份数据库和配置
    restore         恢复数据库和配置
    logs            查看服务日志
    status          查看环境状态
    update          更新镜像和依赖

选项:
    --force, -f     强制执行，不询问确认
    --env ENV       指定环境 (dev/prod，默认: dev)
    --service SVC   指定服务名称
    --help, -h      显示帮助信息

示例:
    $0 clean                    # 清理所有资源
    $0 reset --force            # 强制重置环境
    $0 backup --env dev         # 备份开发环境数据
    $0 logs --service backend   # 查看后端服务日志

EOF
}

# 确认操作
confirm_action() {
    local message=$1
    local force=$2
    
    if [ "$force" = true ]; then
        return 0
    fi
    
    echo -e "${YELLOW}⚠️  $message${NC}"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        return 0
    else
        log_info "操作已取消"
        exit 0
    fi
}

# 清理环境
clean_environment() {
    local force=$1
    local env=${2:-dev}
    
    confirm_action "这将删除所有容器、镜像、数据卷和网络。数据将丢失！" "$force"
    
    log_header "清理柴管家项目环境"
    
    # 停止所有服务
    log_info "停止所有服务..."
    docker-compose -f docker-compose.yml -f docker-compose.$env.yml down -v --remove-orphans 2>/dev/null || true
    
    # 清理项目相关镜像
    log_info "清理项目镜像..."
    docker images | grep -E "(chaiguanjia|chaiguanjia_)" | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true
    
    # 清理悬空镜像
    log_info "清理悬空镜像..."
    docker image prune -f
    
    # 清理网络
    log_info "清理项目网络..."
    docker network ls | grep chaiguanjia | awk '{print $1}' | xargs -r docker network rm 2>/dev/null || true
    
    # 清理数据卷
    log_info "清理数据卷..."
    docker volume ls | grep chaiguanjia | awk '{print $2}' | xargs -r docker volume rm 2>/dev/null || true
    
    # 清理系统资源
    log_info "清理未使用的Docker资源..."
    docker system prune -f --volumes
    
    log_success "环境清理完成"
}

# 重置环境
reset_environment() {
    local force=$1
    local env=${2:-dev}
    
    log_header "重置柴管家项目环境"
    
    # 清理环境
    clean_environment "$force" "$env"
    
    # 等待一段时间
    sleep 3
    
    # 重新启动环境
    log_info "重新启动 $env 环境..."
    ./scripts/docker-start.sh "$env" -d
    
    log_success "环境重置完成"
}

# 备份数据
backup_data() {
    local env=${1:-dev}
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    
    log_header "备份柴管家项目数据"
    
    # 创建备份目录
    mkdir -p "$backup_dir"
    
    # 备份数据库
    log_info "备份PostgreSQL数据库..."
    if docker ps | grep -q chaiguanjia-postgres; then
        docker exec chaiguanjia-postgres pg_dump -U chaiguanjia chaiguanjia > "$backup_dir/database.sql"
        log_success "数据库备份完成: $backup_dir/database.sql"
    else
        log_warning "PostgreSQL容器未运行，跳过数据库备份"
    fi
    
    # 备份配置文件
    log_info "备份配置文件..."
    cp -r .env* "$backup_dir/" 2>/dev/null || true
    cp -r infrastructure/ "$backup_dir/" 2>/dev/null || true
    
    # 备份用户上传文件（如果存在）
    if [ -d "uploads" ]; then
        log_info "备份用户上传文件..."
        cp -r uploads/ "$backup_dir/"
    fi
    
    # 创建备份信息文件
    cat > "$backup_dir/backup_info.txt" << EOF
柴管家项目备份信息
备份时间: $(date)
环境: $env
Docker版本: $(docker --version)
Docker Compose版本: $(docker-compose --version)
EOF
    
    log_success "备份完成: $backup_dir"
}

# 恢复数据
restore_data() {
    local backup_dir=$1
    local force=$2
    
    if [ ! -d "$backup_dir" ]; then
        log_error "备份目录不存在: $backup_dir"
        exit 1
    fi
    
    confirm_action "这将恢复数据并覆盖现有数据" "$force"
    
    log_header "恢复柴管家项目数据"
    
    # 恢复数据库
    if [ -f "$backup_dir/database.sql" ]; then
        log_info "恢复PostgreSQL数据库..."
        if docker ps | grep -q chaiguanjia-postgres; then
            docker exec -i chaiguanjia-postgres psql -U chaiguanjia chaiguanjia < "$backup_dir/database.sql"
            log_success "数据库恢复完成"
        else
            log_error "PostgreSQL容器未运行，无法恢复数据库"
        fi
    fi
    
    # 恢复配置文件
    if [ -d "$backup_dir/infrastructure" ]; then
        log_info "恢复配置文件..."
        cp -r "$backup_dir/infrastructure/" ./
        log_success "配置文件恢复完成"
    fi
    
    # 恢复用户上传文件
    if [ -d "$backup_dir/uploads" ]; then
        log_info "恢复用户上传文件..."
        cp -r "$backup_dir/uploads/" ./
        log_success "用户文件恢复完成"
    fi
    
    log_success "数据恢复完成"
}

# 查看日志
view_logs() {
    local service=$1
    local env=${2:-dev}
    local lines=${3:-100}
    
    local compose_files="-f docker-compose.yml -f docker-compose.$env.yml"
    
    if [ -n "$service" ]; then
        log_info "查看 $service 服务日志 (最近 $lines 行)..."
        docker-compose $compose_files logs -f --tail="$lines" "$service"
    else
        log_info "查看所有服务日志 (最近 $lines 行)..."
        docker-compose $compose_files logs -f --tail="$lines"
    fi
}

# 查看状态
view_status() {
    local env=${1:-dev}
    
    log_header "柴管家项目状态"
    
    # 运行健康检查
    if [ -f "scripts/health-check.sh" ]; then
        ./scripts/health-check.sh
    else
        log_warning "健康检查脚本不存在"
        
        # 简单状态检查
        echo "Docker容器状态:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep chaiguanjia || echo "  无运行中的容器"
    fi
}

# 更新环境
update_environment() {
    local env=${1:-dev}
    local force=$2
    
    log_header "更新柴管家项目环境"
    
    # 拉取最新代码
    log_info "拉取最新代码..."
    git pull origin main || log_warning "Git拉取失败，请手动更新代码"
    
    # 重新构建镜像
    log_info "重新构建镜像..."
    docker-compose -f docker-compose.yml -f docker-compose.$env.yml build --no-cache
    
    # 重启服务
    log_info "重启服务..."
    docker-compose -f docker-compose.yml -f docker-compose.$env.yml up -d
    
    log_success "环境更新完成"
}

# 主函数
main() {
    local command=""
    local force=false
    local env="dev"
    local service=""
    local backup_dir=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            clean|reset|backup|restore|logs|status|update)
                command="$1"
                shift
                ;;
            --force|-f)
                force=true
                shift
                ;;
            --env)
                env="$2"
                shift 2
                ;;
            --service)
                service="$2"
                shift 2
                ;;
            --backup-dir)
                backup_dir="$2"
                shift 2
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                if [ -z "$command" ]; then
                    log_error "未知命令: $1"
                    show_help
                    exit 1
                else
                    # 可能是restore命令的备份目录参数
                    if [ "$command" = "restore" ] && [ -z "$backup_dir" ]; then
                        backup_dir="$1"
                    fi
                fi
                shift
                ;;
        esac
    done
    
    # 检查命令
    if [ -z "$command" ]; then
        log_error "请指定命令"
        show_help
        exit 1
    fi
    
    # 执行命令
    case $command in
        clean)
            clean_environment "$force" "$env"
            ;;
        reset)
            reset_environment "$force" "$env"
            ;;
        backup)
            backup_data "$env"
            ;;
        restore)
            if [ -z "$backup_dir" ]; then
                log_error "请指定备份目录"
                exit 1
            fi
            restore_data "$backup_dir" "$force"
            ;;
        logs)
            view_logs "$service" "$env"
            ;;
        status)
            view_status "$env"
            ;;
        update)
            update_environment "$env" "$force"
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
