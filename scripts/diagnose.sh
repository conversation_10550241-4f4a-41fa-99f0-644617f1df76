#!/bin/bash
# Docker故障诊断脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 诊断单个服务
diagnose_service() {
    local service=$1
    echo ""
    log_header "诊断服务: $service"
    echo "------------------------"
    
    # 检查容器状态
    echo "📊 容器状态:"
    docker-compose ps $service
    
    # 查看最近日志
    echo ""
    echo "📝 最近日志 (最后20行):"
    docker-compose logs --tail 20 $service
    
    # 检查资源使用
    echo ""
    echo "📈 资源使用:"
    docker stats --no-stream $service 2>/dev/null || echo "容器未运行"
    
    # 检查网络连接
    local container_name="chaiguanjia-$service"
    if docker ps --format "{{.Names}}" | grep -q "^$container_name$"; then
        echo ""
        echo "🌐 网络连接测试:"
        case $service in
            "backend")
                docker exec $container_name ping -c 1 postgres > /dev/null 2>&1 && echo "✅ 数据库连接正常" || echo "❌ 数据库连接失败"
                docker exec $container_name ping -c 1 redis > /dev/null 2>&1 && echo "✅ Redis连接正常" || echo "❌ Redis连接失败"
                docker exec $container_name ping -c 1 rabbitmq > /dev/null 2>&1 && echo "✅ RabbitMQ连接正常" || echo "❌ RabbitMQ连接失败"
                ;;
            "celery-worker"|"celery-beat"|"celery-flower")
                docker exec $container_name ping -c 1 rabbitmq > /dev/null 2>&1 && echo "✅ RabbitMQ连接正常" || echo "❌ RabbitMQ连接失败"
                docker exec $container_name ping -c 1 redis > /dev/null 2>&1 && echo "✅ Redis连接正常" || echo "❌ Redis连接失败"
                ;;
            "frontend")
                docker exec $container_name ping -c 1 backend > /dev/null 2>&1 && echo "✅ 后端连接正常" || echo "❌ 后端连接失败"
                ;;
        esac
        
        # 检查环境变量
        echo ""
        echo "🔧 关键环境变量:"
        case $service in
            "backend"|"celery-worker"|"celery-beat")
                docker exec $container_name env | grep -E "(DATABASE_URL|REDIS_URL|CELERY_)" | head -5
                ;;
            "celery-flower")
                docker exec $container_name env | grep -E "(CELERY_|FLOWER_)" | head -5
                ;;
        esac
    else
        echo ""
        echo "❌ 容器未运行，无法进行网络测试"
    fi
}

# 诊断网络问题
diagnose_network() {
    log_header "网络诊断"
    
    echo "🌐 Docker网络列表:"
    docker network ls
    
    echo ""
    echo "🔍 项目网络详情:"
    docker network inspect chaiguanjia_84_chaiguanjia-network 2>/dev/null || echo "项目网络不存在"
    
    echo ""
    echo "📡 端口占用情况:"
    echo "检查关键端口..."
    for port in 80 5173 8000 5432 6379 5672 15672 5555; do
        if lsof -i :$port > /dev/null 2>&1; then
            echo "端口 $port: 已占用"
            lsof -i :$port | head -2
        else
            echo "端口 $port: 空闲"
        fi
    done
}

# 诊断构建问题
diagnose_build() {
    log_header "构建问题诊断"
    
    echo "🔨 Docker构建缓存:"
    docker builder ls
    
    echo ""
    echo "📦 镜像列表:"
    docker images | grep -E "(chaiguanjia|<none>)" | head -10
    
    echo ""
    echo "🗑️ 悬空镜像:"
    docker images -f "dangling=true"
    
    echo ""
    echo "💾 磁盘使用:"
    docker system df
}

# 诊断性能问题
diagnose_performance() {
    log_header "性能诊断"
    
    echo "📊 容器资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
    
    echo ""
    echo "💽 系统资源:"
    echo "内存使用:"
    free -h
    echo ""
    echo "磁盘使用:"
    df -h | grep -E "(/$|/var)"
    
    echo ""
    echo "🔄 Docker守护进程状态:"
    docker version --format "{{.Server.Version}}"
    docker info | grep -E "(CPUs|Total Memory|Docker Root Dir)"
}

# 生成诊断报告
generate_diagnostic_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="/tmp/chaiguanjia_diagnostic_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "柴管家项目诊断报告"
        echo "生成时间: $timestamp"
        echo "========================================"
        echo ""
        
        # 基本信息
        echo "系统信息:"
        uname -a
        echo ""
        
        echo "Docker版本:"
        docker --version
        docker-compose --version
        echo ""
        
        # 容器状态
        echo "容器状态:"
        docker-compose ps
        echo ""
        
        # 网络诊断
        diagnose_network
        echo ""
        
        # 构建诊断
        diagnose_build
        echo ""
        
        # 性能诊断
        diagnose_performance
        echo ""
        
    } > "$report_file"
    
    log_info "诊断报告已生成: $report_file"
}

# 显示修复建议
show_fix_suggestions() {
    local service=$1
    
    echo ""
    log_header "常用修复命令"
    
    if [ -n "$service" ] && [ "$service" != "all" ]; then
        echo "针对服务 '$service' 的修复命令:"
        echo "  重启服务: docker-compose restart $service"
        echo "  重新构建: docker-compose build $service"
        echo "  强制重建: docker-compose build --no-cache $service"
        echo "  查看详细日志: docker-compose logs -f $service"
        echo "  进入容器调试: docker exec -it chaiguanjia-$service /bin/bash"
        echo "  删除并重建: docker-compose rm -f $service && docker-compose up -d $service"
    else
        echo "通用修复命令:"
        echo "  重启所有服务: docker-compose restart"
        echo "  重新构建所有服务: docker-compose build"
        echo "  强制重建: docker-compose build --no-cache"
        echo "  查看所有日志: docker-compose logs"
        echo "  完全重置: docker-compose down && docker-compose up -d"
        echo "  清理系统: docker system prune -a"
    fi
    
    echo ""
    echo "网络问题修复:"
    echo "  重置网络: docker-compose down && docker network prune && docker-compose up -d"
    echo "  检查代理设置: docker info | grep -i proxy"
    echo "  清理构建缓存: docker builder prune -a"
    
    echo ""
    echo "权限问题修复:"
    echo "  修复文件权限: sudo chown -R \$USER:\$USER ."
    echo "  重置Docker权限: sudo usermod -aG docker \$USER"
}

# 主函数
main() {
    local service_name=${1:-"all"}
    local action=${2:-"diagnose"}
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --network|-n)
                diagnose_network
                exit 0
                ;;
            --build|-b)
                diagnose_build
                exit 0
                ;;
            --performance|-p)
                diagnose_performance
                exit 0
                ;;
            --report|-r)
                generate_diagnostic_report
                exit 0
                ;;
            --help|-h)
                echo "用法: $0 [服务名] [选项]"
                echo ""
                echo "服务名:"
                echo "  all (默认)    诊断所有服务"
                echo "  backend       诊断后端服务"
                echo "  frontend      诊断前端服务"
                echo "  postgres      诊断数据库"
                echo "  redis         诊断缓存"
                echo "  rabbitmq      诊断消息队列"
                echo "  celery-worker 诊断Celery Worker"
                echo "  celery-beat   诊断Celery Beat"
                echo "  celery-flower 诊断Celery Flower"
                echo ""
                echo "选项:"
                echo "  --network, -n     只诊断网络问题"
                echo "  --build, -b       只诊断构建问题"
                echo "  --performance, -p 只诊断性能问题"
                echo "  --report, -r      生成完整诊断报告"
                echo "  --help, -h        显示帮助信息"
                exit 0
                ;;
            *)
                service_name=$1
                shift
                ;;
        esac
    done
    
    echo -e "${CYAN}"
    echo "🔍 Docker故障诊断工具"
    echo "========================================"
    echo -e "${NC}"
    
    if [ "$service_name" = "all" ]; then
        # 诊断所有服务
        services=$(docker-compose config --services 2>/dev/null || echo "backend frontend postgres redis rabbitmq celery-worker celery-beat celery-flower")
        for service in $services; do
            diagnose_service $service
        done
        
        # 额外的系统诊断
        diagnose_network
        diagnose_performance
    else
        # 诊断指定服务
        diagnose_service $service_name
    fi
    
    # 显示修复建议
    show_fix_suggestions $service_name
    
    echo ""
    log_info "诊断完成！如需生成详细报告，请运行: $0 --report"
}

# 执行主函数
main "$@"
