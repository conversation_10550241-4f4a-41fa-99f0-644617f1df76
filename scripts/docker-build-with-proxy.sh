#!/bin/bash

# 柴管家项目 - Docker构建脚本（支持代理）
# 解决Docker构建时的网络连接问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测代理配置
detect_proxy() {
    log_info "检测代理配置..."
    
    # 检查常见代理端口
    local proxy_ports=(7890 1087 8080 3128)
    local proxy_host=""
    local proxy_port=""
    
    for port in "${proxy_ports[@]}"; do
        if nc -z 127.0.0.1 $port 2>/dev/null; then
            proxy_host="127.0.0.1"
            proxy_port=$port
            log_success "检测到代理服务: $proxy_host:$proxy_port"
            break
        fi
    done
    
    if [[ -z "$proxy_host" ]]; then
        log_warning "未检测到本地代理服务"
        return 1
    fi
    
    echo "$proxy_host:$proxy_port"
}

# 构建Docker镜像（方案一：host网络模式）
build_with_host_network() {
    local service=$1
    local dockerfile_path=$2
    
    log_info "使用host网络模式构建 $service..."
    
    # 设置代理环境变量
    export http_proxy=http://127.0.0.1:7890
    export https_proxy=http://127.0.0.1:7890
    export HTTP_PROXY=http://127.0.0.1:7890
    export HTTPS_PROXY=http://127.0.0.1:7890
    export no_proxy=localhost,127.0.0.1
    export NO_PROXY=localhost,127.0.0.1
    
    # 使用host网络模式构建
    docker build \
        --network=host \
        --build-arg http_proxy=$http_proxy \
        --build-arg https_proxy=$https_proxy \
        --build-arg HTTP_PROXY=$HTTP_PROXY \
        --build-arg HTTPS_PROXY=$HTTPS_PROXY \
        --build-arg no_proxy=$no_proxy \
        --build-arg NO_PROXY=$NO_PROXY \
        -t chaiguanjia-$service:latest \
        -f $dockerfile_path \
        $(dirname $dockerfile_path)
}

# 构建Docker镜像（方案二：使用host.docker.internal）
build_with_host_internal() {
    local service=$1
    local dockerfile_path=$2
    
    log_info "使用host.docker.internal构建 $service..."
    
    # 在macOS上使用host.docker.internal访问宿主机
    local proxy_url="http://host.docker.internal:7890"
    
    docker build \
        --add-host=host.docker.internal:host-gateway \
        --build-arg http_proxy=$proxy_url \
        --build-arg https_proxy=$proxy_url \
        --build-arg HTTP_PROXY=$proxy_url \
        --build-arg HTTPS_PROXY=$proxy_url \
        --build-arg no_proxy=localhost,127.0.0.1 \
        --build-arg NO_PROXY=localhost,127.0.0.1 \
        -t chaiguanjia-$service:latest \
        -f $dockerfile_path \
        $(dirname $dockerfile_path)
}

# 构建Docker镜像（方案三：无代理模式）
build_without_proxy() {
    local service=$1
    local dockerfile_path=$2
    
    log_info "无代理模式构建 $service..."
    
    # 清除代理环境变量
    unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY
    
    docker build \
        --build-arg http_proxy= \
        --build-arg https_proxy= \
        --build-arg HTTP_PROXY= \
        --build-arg HTTPS_PROXY= \
        -t chaiguanjia-$service:latest \
        -f $dockerfile_path \
        $(dirname $dockerfile_path)
}

# 主构建函数
build_service() {
    local service=$1
    local method=${2:-"auto"}
    
    # 确定Dockerfile路径
    local dockerfile_path=""
    case $service in
        backend)
            dockerfile_path="backend/Dockerfile"
            ;;
        frontend)
            dockerfile_path="frontend/Dockerfile"
            ;;
        celery-worker)
            dockerfile_path="backend/Dockerfile.celery"
            ;;
        celery-beat)
            dockerfile_path="backend/Dockerfile.celery"
            ;;
        *)
            log_error "未知服务: $service"
            return 1
            ;;
    esac
    
    if [[ ! -f "$dockerfile_path" ]]; then
        log_error "Dockerfile不存在: $dockerfile_path"
        return 1
    fi
    
    # 根据方法选择构建策略
    case $method in
        host)
            build_with_host_network $service $dockerfile_path
            ;;
        host-internal)
            build_with_host_internal $service $dockerfile_path
            ;;
        no-proxy)
            build_without_proxy $service $dockerfile_path
            ;;
        auto)
            log_info "自动选择构建方法..."
            if detect_proxy >/dev/null 2>&1; then
                log_info "检测到代理，尝试host网络模式..."
                if build_with_host_network $service $dockerfile_path; then
                    log_success "host网络模式构建成功"
                else
                    log_warning "host网络模式失败，尝试host.docker.internal..."
                    if build_with_host_internal $service $dockerfile_path; then
                        log_success "host.docker.internal模式构建成功"
                    else
                        log_warning "代理模式都失败，尝试无代理模式..."
                        build_without_proxy $service $dockerfile_path
                    fi
                fi
            else
                log_info "未检测到代理，使用无代理模式..."
                build_without_proxy $service $dockerfile_path
            fi
            ;;
        *)
            log_error "未知构建方法: $method"
            return 1
            ;;
    esac
}

# 验证构建结果
verify_build() {
    local service=$1
    
    log_info "验证 $service 镜像构建结果..."
    
    if docker images | grep -q "chaiguanjia-$service"; then
        log_success "$service 镜像构建成功"
        docker images | grep "chaiguanjia-$service"
        return 0
    else
        log_error "$service 镜像构建失败"
        return 1
    fi
}

# 主函数
main() {
    local service=${1:-"backend"}
    local method=${2:-"auto"}
    
    log_info "开始构建 Docker 镜像..."
    log_info "服务: $service"
    log_info "方法: $method"
    
    # 检查Docker是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，请启动Docker Desktop"
        exit 1
    fi
    
    case $service in
        all)
            log_info "构建所有服务..."
            for svc in backend frontend celery-worker celery-beat; do
                log_info "构建服务: $svc"
                if build_service $svc $method && verify_build $svc; then
                    log_success "$svc 构建完成"
                else
                    log_error "$svc 构建失败"
                    exit 1
                fi
            done
            ;;
        backend|frontend|celery-worker|celery-beat)
            if build_service $service $method && verify_build $service; then
                log_success "$service 构建完成"
            else
                log_error "$service 构建失败"
                exit 1
            fi
            ;;
        *)
            log_error "未知服务: $service"
            echo "用法: $0 [backend|frontend|celery-worker|celery-beat|all] [host|host-internal|no-proxy|auto]"
            exit 1
            ;;
    esac
    
    log_success "Docker镜像构建完成！"
}

# 执行主函数
main "$@"
