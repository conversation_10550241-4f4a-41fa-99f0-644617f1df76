#!/bin/bash

# 柴管家项目 - Docker镜像源配置脚本
# 解决Docker Hub连接问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置Docker镜像源
setup_docker_mirror() {
    log_info "配置Docker镜像源..."
    
    # 检查Docker Desktop是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker Desktop未运行，请先启动Docker Desktop"
        exit 1
    fi
    
    # 创建或更新daemon.json
    local docker_config_dir="$HOME/.docker"
    local daemon_config="$docker_config_dir/daemon.json"
    
    # 创建配置目录
    mkdir -p "$docker_config_dir"
    
    # 备份现有配置
    if [ -f "$daemon_config" ]; then
        cp "$daemon_config" "$daemon_config.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "已备份现有Docker配置"
    fi
    
    # 创建新的daemon.json配置
    cat > "$daemon_config" << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://ccr.ccs.tencentyun.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}
EOF
    
    log_success "Docker镜像源配置完成"
    log_warning "请重启Docker Desktop以使配置生效"
    
    echo ""
    echo "配置的镜像源："
    echo "  - 中科大镜像: https://docker.mirrors.ustc.edu.cn"
    echo "  - 网易镜像: https://hub-mirror.c.163.com"
    echo "  - 百度镜像: https://mirror.baidubce.com"
    echo "  - 腾讯镜像: https://ccr.ccs.tencentyun.com"
}

# 验证镜像源配置
verify_mirror_config() {
    log_info "验证Docker镜像源配置..."
    
    if docker info | grep -A 10 "Registry Mirrors" | grep -q "https://"; then
        log_success "Docker镜像源配置验证成功"
        docker info | grep -A 10 "Registry Mirrors"
    else
        log_warning "Docker镜像源配置可能未生效，请检查Docker Desktop设置"
    fi
}

# 测试镜像拉取
test_image_pull() {
    log_info "测试镜像拉取..."
    
    # 测试拉取一个小镜像
    if docker pull hello-world:latest; then
        log_success "镜像拉取测试成功"
        docker rmi hello-world:latest >/dev/null 2>&1 || true
    else
        log_error "镜像拉取测试失败"
        return 1
    fi
}

# 主函数
main() {
    local action=${1:-setup}
    
    case $action in
        setup)
            setup_docker_mirror
            echo ""
            log_info "请按以下步骤操作："
            echo "1. 重启Docker Desktop"
            echo "2. 运行: $0 verify"
            echo "3. 运行: $0 test"
            ;;
        verify)
            verify_mirror_config
            ;;
        test)
            test_image_pull
            ;;
        *)
            echo "用法: $0 [setup|verify|test]"
            echo ""
            echo "命令:"
            echo "  setup   - 配置Docker镜像源"
            echo "  verify  - 验证配置是否生效"
            echo "  test    - 测试镜像拉取"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
