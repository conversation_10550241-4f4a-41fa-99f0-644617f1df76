#!/bin/bash

# 柴管家项目性能监控脚本
# 提供系统资源监控、服务性能分析、告警等功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
柴管家项目性能监控工具

用法: $0 <命令> [选项]

命令:
    overview        显示系统概览
    containers      监控容器资源使用
    services        监控服务性能
    database        监控数据库性能
    network         监控网络状态
    disk            监控磁盘使用
    alerts          检查告警条件
    report          生成性能报告
    watch           实时监控

选项:
    --service SVC   指定服务名称
    --interval SEC  监控间隔 (默认: 5秒)
    --duration MIN  监控持续时间 (默认: 持续监控)
    --output FILE   输出报告到文件
    --threshold NUM 告警阈值 (百分比)
    --help, -h      显示帮助信息

示例:
    $0 overview                                # 显示系统概览
    $0 containers --interval 10               # 每10秒监控容器资源
    $0 services --service backend              # 监控后端服务性能
    $0 watch --duration 30                    # 实时监控30分钟
    $0 alerts --threshold 80                  # 检查80%阈值告警

EOF
}

# 获取容器状态
get_container_stats() {
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# 获取系统资源使用情况
get_system_stats() {
    echo "=== 系统资源使用情况 ==="
    
    # CPU使用率
    local cpu_usage=$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
    echo "CPU使用率: ${cpu_usage}%"
    
    # 内存使用情况
    local memory_info=$(vm_stat | grep -E "(free|active|inactive|wired|compressed)")
    local page_size=$(vm_stat | grep "page size" | awk '{print $8}')
    echo "内存使用情况:"
    echo "$memory_info"
    
    # 磁盘使用情况
    echo ""
    echo "磁盘使用情况:"
    df -h | grep -E "(Filesystem|/dev/)"
    
    # 负载平均值
    echo ""
    echo "系统负载:"
    uptime
}

# 监控容器资源使用
monitor_containers() {
    local interval=${1:-5}
    local duration=${2:-0}
    
    log_header "监控容器资源使用"
    log_info "监控间隔: ${interval}秒"
    
    local start_time=$(date +%s)
    local count=0
    
    while true; do
        clear
        echo "=== 容器资源监控 ($(date)) ==="
        echo ""
        
        # 显示容器统计信息
        get_container_stats
        
        echo ""
        echo "=== 系统资源概览 ==="
        
        # Docker系统信息
        docker system df
        
        echo ""
        echo "按 Ctrl+C 停止监控"
        
        # 检查持续时间
        if [ "$duration" -gt 0 ]; then
            local current_time=$(date +%s)
            local elapsed=$((current_time - start_time))
            if [ $elapsed -ge $((duration * 60)) ]; then
                log_info "监控时间已达到 ${duration} 分钟，停止监控"
                break
            fi
        fi
        
        sleep "$interval"
        count=$((count + 1))
    done
}

# 监控服务性能
monitor_services() {
    local service=$1
    local interval=${2:-5}
    
    log_header "监控服务性能"
    
    if [ -n "$service" ]; then
        log_info "监控服务: $service"
    else
        log_info "监控所有服务"
    fi
    
    while true; do
        clear
        echo "=== 服务性能监控 ($(date)) ==="
        echo ""
        
        # 显示服务状态
        echo "=== 服务状态 ==="
        docker-compose ps
        
        echo ""
        echo "=== 服务资源使用 ==="
        
        if [ -n "$service" ]; then
            # 监控特定服务
            local container_name=$(docker-compose ps -q "$service" 2>/dev/null)
            if [ -n "$container_name" ]; then
                docker stats --no-stream "$container_name"
                
                echo ""
                echo "=== 服务日志 (最近10行) ==="
                docker-compose logs --tail=10 "$service"
            else
                log_error "服务 $service 未运行"
            fi
        else
            # 监控所有服务
            get_container_stats
        fi
        
        echo ""
        echo "按 Ctrl+C 停止监控"
        
        sleep "$interval"
    done
}

# 监控数据库性能
monitor_database() {
    log_header "监控数据库性能"
    
    # 检查PostgreSQL容器是否运行
    if ! docker ps | grep -q chaiguanjia-postgres; then
        log_error "PostgreSQL容器未运行"
        return 1
    fi
    
    echo "=== PostgreSQL性能监控 ==="
    echo ""
    
    # 数据库连接数
    echo "📊 当前连接数:"
    docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia \
        -c "SELECT count(*) as connections FROM pg_stat_activity;" 2>/dev/null || echo "  无法获取连接数"
    
    echo ""
    
    # 数据库大小
    echo "💾 数据库大小:"
    docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia \
        -c "SELECT pg_size_pretty(pg_database_size('chaiguanjia')) as size;" 2>/dev/null || echo "  无法获取数据库大小"
    
    echo ""
    
    # 表大小统计
    echo "📋 表大小统计 (前5个):"
    docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia \
        -c "SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size 
            FROM pg_tables 
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog') 
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
            LIMIT 5;" 2>/dev/null || echo "  无法获取表大小统计"
    
    echo ""
    
    # 活跃查询
    echo "🔍 活跃查询:"
    docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia \
        -c "SELECT pid, usename, application_name, client_addr, state, query_start, query 
            FROM pg_stat_activity 
            WHERE state = 'active' AND query NOT LIKE '%pg_stat_activity%' 
            ORDER BY query_start;" 2>/dev/null || echo "  无法获取活跃查询"
    
    echo ""
    
    # 锁信息
    echo "🔒 锁信息:"
    docker exec chaiguanjia-postgres psql -U chaiguanjia -d chaiguanjia \
        -c "SELECT mode, count(*) FROM pg_locks GROUP BY mode ORDER BY count DESC;" 2>/dev/null || echo "  无法获取锁信息"
}

# 监控网络状态
monitor_network() {
    log_header "监控网络状态"
    
    echo "=== Docker网络信息 ==="
    docker network ls
    
    echo ""
    echo "=== 容器网络连接 ==="
    
    # 获取所有运行中的容器
    local containers=$(docker ps --format "{{.Names}}")
    
    for container in $containers; do
        if [[ "$container" == *"chaiguanjia"* ]]; then
            echo "📡 $container:"
            docker exec "$container" netstat -tuln 2>/dev/null | head -10 || echo "  无法获取网络信息"
            echo ""
        fi
    done
    
    echo "=== 端口映射 ==="
    docker ps --format "table {{.Names}}\t{{.Ports}}"
}

# 监控磁盘使用
monitor_disk() {
    log_header "监控磁盘使用"
    
    echo "=== 系统磁盘使用 ==="
    df -h
    
    echo ""
    echo "=== Docker磁盘使用 ==="
    docker system df -v
    
    echo ""
    echo "=== 项目目录大小 ==="
    du -sh . 2>/dev/null || echo "无法获取项目目录大小"
    
    echo ""
    echo "=== 日志文件大小 ==="
    if [ -d "logs" ]; then
        du -sh logs/* 2>/dev/null || echo "无日志文件"
    else
        echo "无日志目录"
    fi
}

# 检查告警条件
check_alerts() {
    local threshold=${1:-80}
    
    log_header "检查告警条件 (阈值: ${threshold}%)"
    
    local alerts=()
    
    # 检查容器CPU使用率
    echo "🔍 检查容器CPU使用率..."
    local high_cpu_containers=$(docker stats --no-stream --format "{{.Container}} {{.CPUPerc}}" | \
        awk -v threshold="$threshold" '$2+0 > threshold {print $1 " (" $2 ")"}')
    
    if [ -n "$high_cpu_containers" ]; then
        alerts+=("高CPU使用率容器: $high_cpu_containers")
    fi
    
    # 检查容器内存使用率
    echo "🔍 检查容器内存使用率..."
    local high_mem_containers=$(docker stats --no-stream --format "{{.Container}} {{.MemPerc}}" | \
        awk -v threshold="$threshold" '$2+0 > threshold {print $1 " (" $2 ")"}')
    
    if [ -n "$high_mem_containers" ]; then
        alerts+=("高内存使用率容器: $high_mem_containers")
    fi
    
    # 检查磁盘使用率
    echo "🔍 检查磁盘使用率..."
    local high_disk_usage=$(df -h | awk -v threshold="$threshold" 'NR>1 && $5+0 > threshold {print $6 " (" $5 ")"}')
    
    if [ -n "$high_disk_usage" ]; then
        alerts+=("高磁盘使用率: $high_disk_usage")
    fi
    
    # 检查服务健康状态
    echo "🔍 检查服务健康状态..."
    local unhealthy_services=$(docker-compose ps | grep -v "Up" | grep -v "Name" | awk '{print $1}')
    
    if [ -n "$unhealthy_services" ]; then
        alerts+=("不健康的服务: $unhealthy_services")
    fi
    
    # 显示告警结果
    echo ""
    if [ ${#alerts[@]} -eq 0 ]; then
        log_success "✅ 所有检查通过，无告警"
    else
        log_warning "⚠️  发现 ${#alerts[@]} 个告警:"
        for alert in "${alerts[@]}"; do
            echo "  - $alert"
        done
    fi
}

# 生成性能报告
generate_report() {
    local output_file=${1:-"reports/performance_report_$(date +%Y%m%d_%H%M%S).txt"}
    
    # 创建报告目录
    mkdir -p "$(dirname "$output_file")"
    
    log_header "生成性能报告: $output_file"
    
    {
        echo "柴管家项目性能报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "=== 系统概览 ==="
        get_system_stats
        echo ""
        
        echo "=== 容器状态 ==="
        docker-compose ps
        echo ""
        
        echo "=== 容器资源使用 ==="
        get_container_stats
        echo ""
        
        echo "=== Docker系统信息 ==="
        docker system df
        echo ""
        
        echo "=== 网络状态 ==="
        docker network ls
        echo ""
        
        echo "=== 磁盘使用 ==="
        df -h
        echo ""
        
        echo "=== 告警检查 ==="
        check_alerts 80
        echo ""
        
        echo "报告生成完成: $(date)"
        
    } > "$output_file"
    
    local file_size=$(du -h "$output_file" | cut -f1)
    log_success "性能报告已生成: $output_file (大小: $file_size)"
}

# 显示系统概览
show_overview() {
    log_header "系统概览"
    
    echo "=== 服务状态 ==="
    docker-compose ps
    
    echo ""
    echo "=== 资源使用概览 ==="
    get_container_stats
    
    echo ""
    get_system_stats
    
    echo ""
    echo "=== 快速健康检查 ==="
    check_alerts 90
}

# 实时监控
watch_performance() {
    local interval=${1:-5}
    local duration=${2:-0}
    
    log_header "实时性能监控"
    log_info "监控间隔: ${interval}秒"
    
    if [ "$duration" -gt 0 ]; then
        log_info "监控持续时间: ${duration}分钟"
    fi
    
    local start_time=$(date +%s)
    
    while true; do
        clear
        echo "=== 实时性能监控 ($(date)) ==="
        echo ""
        
        # 显示概览信息
        show_overview
        
        echo ""
        echo "按 Ctrl+C 停止监控"
        
        # 检查持续时间
        if [ "$duration" -gt 0 ]; then
            local current_time=$(date +%s)
            local elapsed=$((current_time - start_time))
            if [ $elapsed -ge $((duration * 60)) ]; then
                log_info "监控时间已达到 ${duration} 分钟，停止监控"
                break
            fi
            
            local remaining=$((duration * 60 - elapsed))
            echo "剩余时间: $((remaining / 60))分$((remaining % 60))秒"
        fi
        
        sleep "$interval"
    done
}

# 主函数
main() {
    local command=""
    local service=""
    local interval="5"
    local duration="0"
    local output=""
    local threshold="80"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            overview|containers|services|database|network|disk|alerts|report|watch)
                command="$1"
                shift
                ;;
            --service)
                service="$2"
                shift 2
                ;;
            --interval)
                interval="$2"
                shift 2
                ;;
            --duration)
                duration="$2"
                shift 2
                ;;
            --output)
                output="$2"
                shift 2
                ;;
            --threshold)
                threshold="$2"
                shift 2
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查命令
    if [ -z "$command" ]; then
        log_error "请指定命令"
        show_help
        exit 1
    fi
    
    # 执行命令
    case $command in
        overview)
            show_overview
            ;;
        containers)
            monitor_containers "$interval" "$duration"
            ;;
        services)
            monitor_services "$service" "$interval"
            ;;
        database)
            monitor_database
            ;;
        network)
            monitor_network
            ;;
        disk)
            monitor_disk
            ;;
        alerts)
            check_alerts "$threshold"
            ;;
        report)
            generate_report "$output"
            ;;
        watch)
            watch_performance "$interval" "$duration"
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
