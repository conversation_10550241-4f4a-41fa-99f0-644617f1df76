---
type: "always_apply"
description: "Example description"
---
# 柴管家项目结构速查手册

> � **快速参考** | 团队开发必备 | 5分钟速查

## 📋 核心架构原则

- **模块化单体架构**：API层 → 业务层 → 数据层 → 基础设施层
- **严格分层**：禁止跨层直接调用
- **统一标准**：所有模块遵循相同结构

## 📁 目录结构速查

### 根目录（仅限以下内容）
```
chaiguanjia/
├── frontend/          ├── backend/           ├── infrastructure/
├── docs/             ├── tests/             ├── .github/
├── README.md         ├── .gitignore         └── docker-compose.yml
```

### 前端结构
```
frontend/src/
├── components/       ├── pages/            ├── hooks/
├── services/         ├── stores/           ├── utils/
├── types/           ├── assets/           ├── layouts/
├── contexts/        └── constants/
```

### 后端结构
```
backend/app/
├── config/          ├── core/             ├── middleware/
├── plugins/         ├── modules/          ├── shared/
└── api/
```

### 业务模块标准结构（必须严格遵循）
```
modules/{module_name}/
├── api/             # routers.py, schemas.py, dependencies.py
├── services/        # {module_name}_service.py
├── models/          # {module_name}.py
└── tests/           # test_api.py, test_services.py, features/
```

## 🏷️ 文件命名速查表

| 类型 | 规则 | 示例 |
|-----|------|------|
| Python文件 | snake_case | `user_service.py` |
| Python类 | PascalCase | `UserService` |
| Python函数 | snake_case | `get_user_by_id()` |
| Python常量 | UPPER_SNAKE_CASE | `MAX_RETRY_COUNT` |
| React组件 | PascalCase | `UserProfile.tsx` |
| TypeScript文件 | camelCase | `userService.ts` |
| 测试文件 | test_前缀 | `test_user_service.py` |

### 固定文件名
- API路由：`routers.py`
- 数据模型：`models.py` 或 `{entity}.py`
- 请求响应：`schemas.py`
- 依赖注入：`dependencies.py`

## ⚡ 新模块创建速查

### 1. 创建目录
```bash
mkdir -p backend/app/modules/{module_name}/{api,services,models,tests/features}
```

### 2. 创建文件
```bash
touch backend/app/modules/{module_name}/__init__.py
touch backend/app/modules/{module_name}/api/{__init__.py,routers.py,schemas.py,dependencies.py}
touch backend/app/modules/{module_name}/services/{__init__.py,{module_name}_service.py}
touch backend/app/modules/{module_name}/models/{__init__.py,{module_name}.py}
touch backend/app/modules/{module_name}/tests/{__init__.py,test_api.py,test_services.py}
```

## 📝 代码模板速查

### API层模板
```python
# routers.py
from fastapi import APIRouter
from .schemas import CreateSchema, ResponseSchema

router = APIRouter(prefix="/endpoint", tags=["tag"])

@router.post("/", response_model=ResponseSchema)
async def create_item(data: CreateSchema):
    pass
```

### 业务层模板
```python
# {module}_service.py
class ModuleService:
    def __init__(self):
        self.db = get_db_session()

    async def create_item(self, data: dict):
        pass
```

### 数据层模板
```python
# {module}.py
from sqlalchemy import Column, Integer, String
from ..shared.database.base import Base

class Entity(Base):
    __tablename__ = "table_name"
    id = Column(Integer, primary_key=True)
```

## ✅ 代码审查检查清单

### 目录结构
- [ ] 新目录是否符合标准结构
- [ ] 是否包含必需的`__init__.py`
- [ ] 业务模块是否遵循三层架构

### 文件命名
- [ ] Python文件使用snake_case
- [ ] 类名使用PascalCase
- [ ] 测试文件以test_开头

### 代码组织
- [ ] API层只包含路由和请求处理
- [ ] 业务层包含核心逻辑
- [ ] 数据层只包含模型定义
- [ ] 无跨层直接调用

## ❌ 常见违规快速识别

### 错误示例
```python
# ❌ 错误：API层直接操作数据模型
@router.get("/users/{id}")
def get_user(id: int):
    user = User.query.filter_by(id=id).first()  # 违规！
    return user

# ❌ 错误：驼峰命名
class userService:  # 违规！应该是UserService
    def getUserById(self, userId):  # 违规！应该是get_user_by_id
        pass
```

### 正确示例
```python
# ✅ 正确：通过业务层调用
@router.get("/users/{id}")
def get_user(id: int):
    service = UserService()
    return service.get_user_by_id(id)

# ✅ 正确：规范命名
class UserService:
    def get_user_by_id(self, user_id: int):
        pass
```

## 🚨 强制执行

- **代码审查**：违规代码不允许合并
- **自动检查**：CI/CD自动验证结构
- **零容忍**：所有违规必须立即修正

---
**版本**: v1.0 | **更新**: 2024-08-05 | **维护**: 柴管家团队
