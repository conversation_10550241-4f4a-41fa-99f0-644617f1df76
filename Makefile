# 柴管家项目 Makefile
# 提供常用的开发命令

.PHONY: help install install-dev setup-pre-commit
.PHONY: lint lint-frontend lint-backend format format-frontend format-backend
.PHONY: test test-frontend test-backend type-check
.PHONY: clean clean-frontend clean-backend
.PHONY: dev build docker-up docker-down

# 默认目标
help: ## 显示帮助信息
	@echo "柴管家项目开发命令"
	@echo "==================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# ===== 安装和设置 =====
install: ## 安装所有依赖
	@echo "安装前端依赖..."
	cd frontend && npm install
	@echo "安装后端依赖..."
	cd backend && pip install -r requirements-dev.txt

install-dev: install setup-pre-commit ## 安装开发环境（包含 pre-commit）

setup-pre-commit: ## 设置 pre-commit hooks
	@echo "安装 pre-commit..."
	pip install pre-commit
	@echo "设置 pre-commit hooks..."
	pre-commit install
	pre-commit install --hook-type commit-msg
	@echo "运行首次检查..."
	pre-commit run --all-files || true

# ===== 代码质量检查 =====
lint: lint-frontend lint-backend ## 运行所有代码检查

lint-frontend: ## 检查前端代码
	@echo "检查前端代码..."
	cd frontend && npm run lint
	cd frontend && npm run type-check

lint-backend: ## 检查后端代码
	@echo "检查后端代码..."
	cd backend && flake8 .
	cd backend && mypy .
	cd backend && bandit -r . -c pyproject.toml

format: format-frontend format-backend ## 格式化所有代码

format-frontend: ## 格式化前端代码
	@echo "格式化前端代码..."
	cd frontend && npm run format

format-backend: ## 格式化后端代码
	@echo "格式化后端代码..."
	cd backend && black .
	cd backend && isort .

# ===== 测试 =====
test: test-frontend test-backend ## 运行所有测试

test-frontend: ## 运行前端测试
	@echo "运行前端测试..."
	cd frontend && npm run test

test-backend: ## 运行后端测试
	@echo "运行后端测试..."
	cd backend && pytest

type-check: ## 运行类型检查
	@echo "检查前端类型..."
	cd frontend && npm run type-check
	@echo "检查后端类型..."
	cd backend && mypy .

# ===== 清理 =====
clean: clean-frontend clean-backend ## 清理所有构建文件

clean-frontend: ## 清理前端构建文件
	@echo "清理前端文件..."
	cd frontend && rm -rf node_modules dist coverage .eslintcache

clean-backend: ## 清理后端构建文件
	@echo "清理后端文件..."
	cd backend && rm -rf __pycache__ .pytest_cache .mypy_cache htmlcov .coverage
	find backend -name "*.pyc" -delete
	find backend -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# ===== 开发服务器 =====
dev: ## 启动开发服务器
	@echo "启动开发环境..."
	docker-compose up -d

build: ## 构建项目
	@echo "构建前端..."
	cd frontend && npm run build
	@echo "构建后端..."
	cd backend && python -m build

# ===== Docker 命令 =====
docker-up: ## 启动 Docker 服务
	docker-compose up -d

docker-down: ## 停止 Docker 服务
	docker-compose down

docker-logs: ## 查看 Docker 日志
	docker-compose logs -f

docker-rebuild: ## 重新构建并启动 Docker 服务
	docker-compose down
	docker-compose build --no-cache
	docker-compose up -d

# ===== 数据库命令 =====
db-migrate: ## 运行数据库迁移
	cd backend && alembic upgrade head

db-revision: ## 创建新的数据库迁移
	cd backend && alembic revision --autogenerate -m "$(msg)"

# ===== 安全检查 =====
security: ## 运行安全检查
	@echo "检查 Python 依赖安全性..."
	cd backend && safety check
	@echo "检查前端依赖安全性..."
	cd frontend && npm audit

# ===== 完整检查 =====
check-all: lint test type-check security ## 运行所有检查（CI 使用）

# ===== 项目初始化 =====
init: clean install-dev ## 初始化项目（首次使用）
	@echo "项目初始化完成！"
	@echo "运行 'make dev' 启动开发环境"
