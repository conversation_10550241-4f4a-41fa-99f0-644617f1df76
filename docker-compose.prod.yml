# 柴管家项目生产环境 Docker Compose 配置
# 专门用于生产环境的配置覆盖

version: '3.8'

services:
  # ================================
  # 后端生产环境配置
  # ================================
  backend:
    build:
      target: production
    environment:
      DEBUG: "false"
      LOG_LEVEL: INFO
      ENABLE_DOCS: "false"
      ENABLE_DEBUG_TOOLBAR: "false"
      ENABLE_RELOAD: "false"
    volumes:
      - backend-logs:/app/logs
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # ================================
  # 前端生产环境配置
  # ================================
  frontend:
    build:
      target: production
    environment:
      NODE_ENV: production
    volumes:
      - frontend-logs:/var/log/nginx
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # ================================
  # 数据库生产环境配置
  # ================================
  postgres:
    environment:
      POSTGRES_DB: chaiguanjia_prod
      POSTGRES_USER: chaiguanjia_prod
      # 生产环境密码应从外部文件或密钥管理系统获取
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
    volumes:
      - postgres-prod-data:/var/lib/postgresql/data
      - postgres-backups:/backups
      - ./infrastructure/docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    secrets:
      - postgres_password
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3

  # ================================
  # Redis 生产环境配置
  # ================================
  redis:
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      REDIS_PASSWORD_FILE: /run/secrets/redis_password
    volumes:
      - redis-prod-data:/data
      - ./infrastructure/docker/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    secrets:
      - redis_password
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # ================================
  # RabbitMQ 生产环境配置
  # ================================
  rabbitmq:
    environment:
      RABBITMQ_DEFAULT_USER: chaiguanjia_prod
      RABBITMQ_DEFAULT_PASS_FILE: /run/secrets/rabbitmq_password
      RABBITMQ_DEFAULT_VHOST: chaiguanjia_prod
    volumes:
      - rabbitmq-prod-data:/var/lib/rabbitmq
      - ./infrastructure/docker/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
    secrets:
      - rabbitmq_password
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # ================================
  # Celery Worker 生产环境配置
  # ================================
  celery-worker:
    environment:
      LOG_LEVEL: INFO
    volumes:
      - celery-logs:/app/logs
    command: ["celery", "-A", "app.core.celery", "worker", "--loglevel=info", "--concurrency=4", "--max-tasks-per-child=1000"]
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # ================================
  # Celery Beat 生产环境配置
  # ================================
  celery-beat:
    environment:
      LOG_LEVEL: INFO
    volumes:
      - celery-beat-data:/app/celerybeat-schedule
      - celery-logs:/app/logs
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # ================================
  # Nginx 生产环境配置
  # ================================
  nginx:
    volumes:
      - ./infrastructure/docker/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./infrastructure/docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx-prod-logs:/var/log/nginx
      - ssl-certs:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

# ================================
# 生产环境密钥管理
# ================================
secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
  redis_password:
    file: ./secrets/redis_password.txt
  rabbitmq_password:
    file: ./secrets/rabbitmq_password.txt

# ================================
# 生产环境数据卷
# ================================
volumes:
  postgres-prod-data:
    driver: local
  postgres-backups:
    driver: local
  redis-prod-data:
    driver: local
  rabbitmq-prod-data:
    driver: local
  backend-logs:
    driver: local
  frontend-logs:
    driver: local
  celery-logs:
    driver: local
  celery-beat-data:
    driver: local
  nginx-prod-logs:
    driver: local
  ssl-certs:
    driver: local
