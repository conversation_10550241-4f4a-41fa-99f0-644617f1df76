# 柴管家 (ChaiGuanJia)

> 🤖 智能私域运营中枢系统 | 为个人 IP 运营者打造的 AI 驱动客服平台

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![CI Pipeline](https://github.com/Amoresdk/chaiguanjia_8.4/actions/workflows/ci.yml/badge.svg)](https://github.com/Amoresdk/chaiguanjia_8.4/actions/workflows/ci.yml)
[![Security Scan](https://github.com/Amoresdk/chaiguanjia_8.4/actions/workflows/security.yml/badge.svg)](https://github.com/Amoresdk/chaiguanjia_8.4/actions/workflows/security.yml)
[![Coverage](https://codecov.io/gh/Amoresdk/chaiguanjia_8.4/branch/main/graph/badge.svg)](https://codecov.io/gh/Amoresdk/chaiguanjia_8.4)
[![Status](https://img.shields.io/badge/Status-MVP%20Development-orange.svg)](https://github.com)
[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-red.svg)](https://fastapi.tiangolo.com/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x+-blue.svg)](https://www.typescriptlang.org/)
[![Docker](https://img.shields.io/badge/Docker-Optimized-blue.svg)](https://www.docker.com/)

## 📖 项目简介

柴管家是一个基于 AI 的智能私域运营中枢系统，专为个人 IP 运营者和知识类创作者设计。通过聚合多平台消息和 AI 智能处理，帮助运营者实现高效的私域流量管理，解决多平台消息分散、重复性咨询繁重、用户关系维护困难等核心痛点。

### 🎯 核心价值

**为个人 IP 运营者打造一站式私域运营解决方案**

- **🔄 统一信息入口**：将分散在各个平台的消息流聚合到一个工作台
- **🤖 智能化处理**：通过 AI 减少 80%的重复性咨询工作量
- **👥 人机协作**：在保证效率的同时确保服务质量和安全性
- **📊 数据驱动**：提供统一的用户视图和运营洞察

### 🌟 核心特性

- **� 多平台接入**：支持微信、抖音、小红书、知识星球等主流平台
- **� 统一消息流**：所有平台消息聚合到统一工作台，无需切换应用
- **🧠 AI 副驾式回复**：实时意图识别，智能回复建议，提升回复质量
- **⚡ AI 智能托管**：置信度机制确保安全，低置信度自动转人工
- **� 知识库管理**：FAQ 智能匹配，支持动态学习和优化
- **� 工作流引擎**：可编排的自动化任务，支持复杂业务流程
- **� 用户画像**：跨平台用户数据整合，精准用户洞察
- **📈 运营分析**：数据驱动的运营决策支持

### 🏗️ 技术架构

**模块化单体架构** - 平衡开发效率与系统复杂度

#### 前端技术栈

- **React** 18.x - 用户界面框架
- **TypeScript** 5.x - 类型安全开发
- **Vite** - 快速构建工具
- **Zustand** - 轻量级状态管理
- **Tailwind CSS** - 现代化样式框架

#### 后端技术栈

- **Python** 3.11+ - 主要开发语言
- **FastAPI** 0.104+ - 高性能 API 框架
- **SQLAlchemy** 2.0 - ORM 框架
- **Pydantic** - 数据验证和序列化

#### 数据存储

- **PostgreSQL** 15+ - 主数据库
- **Redis** 7+ - 缓存和会话存储

#### 消息队列与 AI 服务

- **RabbitMQ** 3.12+ - 异步消息处理
- **通义千问 API** - 主要 AI 对话服务
- **智能路由** - 多模型负载均衡

#### 基础设施

- **Docker** 24+ - 容器化部署
- **Docker Compose** - 服务编排
- **Nginx** 1.24+ - 反向代理和负载均衡

#### 开发工具

- **代码质量**：ESLint + Prettier + Black + pytest
- **类型检查**：TypeScript + mypy
- **测试框架**：Jest + React Testing Library + pytest
- **CI/CD**：GitHub Actions

### �️ 系统架构图

```mermaid
graph TB
    subgraph "柴管家智能私域运营中枢"
        subgraph "用户接入层"
            WEB[Web前端应用<br/>React + TypeScript]
            API[API网关<br/>Nginx + FastAPI]
        end

        subgraph "业务模块层"
            CHANNEL[渠道管理模块<br/>多平台接入]
            MESSAGE[消息处理模块<br/>消息路由分发]
            AI[AI智能服务模块<br/>对话生成]
            KNOWLEDGE[知识管理模块<br/>知识库检索]
            USER[用户管理模块<br/>权限认证]
            WORKFLOW[工作流引擎模块<br/>业务流程]
        end

        subgraph "基础设施层"
            DB[(PostgreSQL<br/>主数据库)]
            CACHE[(Redis<br/>缓存)]
            MQ[RabbitMQ<br/>消息队列]
            AI_API[通义千问 API<br/>AI服务]
        end

        WEB --> API
        API --> CHANNEL
        API --> MESSAGE
        API --> AI
        API --> KNOWLEDGE
        API --> USER
        API --> WORKFLOW

        CHANNEL --> MQ
        MESSAGE --> MQ
        AI --> MQ
        KNOWLEDGE --> MQ
        USER --> MQ
        WORKFLOW --> MQ

        CHANNEL --> DB
        MESSAGE --> DB
        AI --> CACHE
        KNOWLEDGE --> DB
        USER --> DB
        WORKFLOW --> DB

        AI --> AI_API
    end

    style WEB fill:#e3f2fd
    style CHANNEL fill:#f3e5f5
    style MESSAGE fill:#e8f5e8
    style AI fill:#fff3e0
    style KNOWLEDGE fill:#fce4ec
    style USER fill:#e0f2f1
    style WORKFLOW fill:#f1f8e9
    style DB fill:#ffecb3
    style CACHE fill:#ffcdd2
    style MQ fill:#d1c4e9
    style AI_API fill:#c8e6c9
```

### 🔄 核心业务流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Platform as 第三方平台
    participant Gateway as API网关
    participant Channel as 渠道管理
    participant Message as 消息处理
    participant AI as AI服务
    participant Knowledge as 知识库
    participant MQ as RabbitMQ

    Note over User,Knowledge: 智能客服处理流程

    User->>Platform: 发送消息
    Platform->>Gateway: 推送消息
    Gateway->>Channel: 路由到渠道
    Channel->>MQ: 发布消息事件
    MQ->>Message: 消息处理
    Message->>AI: 请求AI分析
    AI->>Knowledge: 检索知识库
    Knowledge-->>AI: 返回相关知识
    AI-->>Message: 生成回复(置信度)

    alt 置信度 >= 0.8
        Message->>MQ: 发布自动回复事件
        MQ->>Channel: 推送回复
        Channel->>Platform: 发送回复
        Platform-->>User: 用户收到回复
    else 置信度 < 0.8
        Message->>Gateway: 转人工处理
        Gateway-->>User: 人工客服介入
    end
```

### �🎯 项目状态

**当前阶段：MVP 开发中** 🚧

#### 已完成功能 ✅

- **基础架构**：模块化单体架构设计完成
- **开发环境**：Docker 容器化环境完全配置
- **CI/CD 流水线**：GitHub Actions 自动化测试和部署
- **代码规范**：完整的代码质量检查体系
- **项目文档**：详细的架构设计和开发指南

#### 开发中功能 🚧

- **用户管理模块**：用户注册、登录、权限管理
- **渠道管理模块**：多平台账号接入和管理
- **消息处理模块**：统一消息流处理
- **AI 服务模块**：智能回复和置信度评估
- **知识库模块**：FAQ 管理和智能匹配

#### 计划功能 📋

- **工作流引擎**：可编排的自动化任务
- **运营分析**：数据统计和效果分析
- **高级 AI 功能**：多模型路由和学习优化

### 🧪 开发环境状态

> **环境版本**: v0.1.0
> **最后更新**: 2024-08-07
> **开发状态**: 所有基础服务正常运行，支持热重载开发

## 🚀 快速开始

### 📋 环境要求

确保您的开发环境满足以下要求：

- **Docker Desktop**: 24.0+ ([下载地址](https://www.docker.com/get-started))
- **Node.js**: 18.0+ ([下载地址](https://nodejs.org/))
- **Python**: 3.11+ (可选，用于本地开发)
- **Git**: 最新版本

### ⚡ 一键启动开发环境

```bash
# 1. 克隆项目
git clone https://github.com/Amoresdk/chaiguanjia_8.4.git
cd chaiguanjia_8.4

# 2. 配置环境变量
cp .env.example .env
# 根据需要修改.env文件中的配置

# 3. 启动完整开发环境
docker-compose up -d

# 4. 验证服务状态
docker-compose ps

# 5. 运行健康检查
./scripts/health-check.sh
```

### 🔧 开发环境配置

项目提供了完整的容器化开发环境，支持：

- **热重载开发**：前后端代码修改自动重启
- **数据库迁移**：自动应用数据库结构变更
- **依赖管理**：自动安装和管理所有依赖
- **服务编排**：一键启动所有必需服务

### 🌐 网络配置说明

✅ **Docker 网络配置已完全优化**，解决了所有构建和运行问题：

1. **代理配置问题**：项目已配置 build-arg 来处理代理设置
2. **镜像源优化**：使用国内镜像源提高构建速度
3. **网络问题修复**：100%解决容器间通信和外网访问问题
4. **详细指南**：查看 `项目文档/运维指南/Docker避坑指南.md`

### 🌐 服务访问地址

启动成功后，您可以访问以下地址：

| 服务                 | 地址                         | 认证信息                   | 状态      |
| -------------------- | ---------------------------- | -------------------------- | --------- |
| 🎨 **前端应用**      | http://localhost:5173        | -                          | ✅ 运行中 |
| 🔧 **API 文档**      | http://localhost:8000/docs   | -                          | ✅ 运行中 |
| 🔧 **API 健康检查**  | http://localhost:8000/health | -                          | ✅ 运行中 |
| 🌸 **Flower 监控**   | http://localhost:5555        | admin:flower123            | ✅ 运行中 |
| 🐰 **RabbitMQ 管理** | http://localhost:15672       | chaiguanjia:rabbitmq123    | ✅ 运行中 |
| 🗄️ **PostgreSQL**    | localhost:5432               | chaiguanjia:chaiguanjia123 | ✅ 运行中 |
| 🔴 **Redis**         | localhost:6379               | :redis123                  | ✅ 运行中 |
| 🌐 **Nginx 代理**    | http://localhost:80          | -                          | ✅ 运行中 |

### ✅ 服务验证步骤

启动后请按以下步骤验证所有服务：

```bash
# 1. 检查所有容器状态
docker-compose ps

# 2. 运行完整健康检查
./scripts/health-check.sh

# 3. 测试前端访问
curl http://localhost:5173/

# 4. 测试后端API
curl http://localhost:8000/health

# 5. 测试Flower监控（需要认证）
curl -u admin:flower123 http://localhost:5555/

# 6. 检查Celery任务状态
docker exec chaiguanjia-celery-worker celery -A app.core.celery inspect ping
```

### 🛠️ 运维工具

项目提供了完整的运维工具来简化开发和故障排查：

#### 🎯 项目总控制

```bash
./scripts/project-manager.sh dev      # 🚀 启动完整开发环境
./scripts/project-manager.sh status   # 📊 查看所有服务状态
./scripts/project-manager.sh stop     # ⏹️ 停止所有服务
./scripts/project-manager.sh logs     # 📋 查看服务日志
```

#### 🔍 健康检查与诊断

```bash
./scripts/health-check.sh             # 🏥 完整健康检查
./scripts/diagnose.sh                 # 🔍 故障诊断工具
./scripts/diagnose.sh backend         # 🔍 诊断特定服务
./scripts/diagnose.sh --network       # 🌐 网络问题诊断
./scripts/diagnose.sh --report        # 📊 生成诊断报告
```

#### 📋 日志管理与分析

```bash
./scripts/log-query.sh tail            # 📄 实时查看应用日志
./scripts/log-query.sh search "ERROR"  # 🔍 搜索错误信息
./scripts/log-query.sh errors -n 100   # ❌ 查看最近100条错误
./scripts/log-query.sh business        # 📊 查看业务日志
./scripts/log-query.sh stats           # 📈 显示日志统计
./scripts/log-query.sh analyze         # 📊 生成详细分析报告
./scripts/test-logging.py              # 🧪 测试日志系统功能
```

#### 🚨 错误监控与管理

```bash
./scripts/error-manager.py list        # 📋 查看错误列表
./scripts/error-manager.py show <id>   # 🔍 查看错误详情
./scripts/error-manager.py pending     # ⏳ 查看待处理错误
./scripts/error-manager.py dashboard   # 📊 错误监控仪表板
./scripts/error-analyzer.py --command report  # 📈 生成错误分析报告
./scripts/test-error-monitoring.py     # 🧪 测试错误监控功能
```

#### 🧹 环境清理

```bash
./scripts/cleanup.sh                  # 🧹 交互式清理菜单
./scripts/cleanup.sh --light          # 🧹 轻度清理（停止容器）
./scripts/cleanup.sh --medium         # 🧹 中度清理（删除容器和镜像）
./scripts/cleanup.sh --backup         # 💾 备份重要数据
```

#### 🗄️ 数据库管理

```bash
./scripts/db-manager.sh backup        # 💾 备份数据库
./scripts/db-manager.sh monitor       # 📊 实时监控数据库
./scripts/db-manager.sh connect       # 🔗 连接数据库终端
```

#### 🎨 前端管理

```bash
./scripts/frontend-manager.sh dev     # 🚀 启动开发服务器
./scripts/frontend-manager.sh build   # 🏗️ 构建生产版本
./scripts/frontend-manager.sh test    # 🧪 运行测试套件
```

## 📁 项目结构

```
chaiguanjia_8.4/
├── 📱 frontend/                    # React前端应用
│   ├── src/
│   │   ├── components/            # 通用组件
│   │   ├── pages/                 # 页面组件
│   │   ├── hooks/                 # 自定义Hooks
│   │   ├── services/              # API服务
│   │   ├── stores/                # 状态管理(Zustand)
│   │   ├── utils/                 # 工具函数
│   │   ├── types/                 # TypeScript类型定义
│   │   ├── assets/                # 静态资源
│   │   ├── layouts/               # 布局组件
│   │   ├── contexts/              # React Context
│   │   └── constants/             # 常量定义
│   ├── package.json
│   └── vite.config.ts
├── 🔧 backend/                     # FastAPI后端应用
│   ├── app/
│   │   ├── main.py               # FastAPI应用入口
│   │   ├── config/               # 配置管理
│   │   ├── core/                 # 核心功能
│   │   ├── middleware/           # 中间件管理
│   │   ├── modules/              # 业务模块
│   │   │   ├── user_management/  # 用户管理模块
│   │   │   ├── channel_management/ # 渠道管理模块
│   │   │   ├── message_processing/ # 消息处理模块
│   │   │   ├── ai_services/      # AI服务模块
│   │   │   ├── knowledge_management/ # 知识管理模块
│   │   │   └── workflow_engine/  # 工作流引擎模块
│   │   ├── shared/               # 共享组件
│   │   └── api/                  # API路由聚合
│   ├── requirements.txt
│   └── pyproject.toml
├── 🏗️ infrastructure/              # 基础设施配置
│   ├── docker/                   # Docker配置
│   └── gateway/                  # API网关配置
├── 📜 scripts/                    # 项目管理脚本
│   ├── project-manager.sh        # 项目总控制脚本
│   ├── health-check.sh           # 健康检查脚本
│   ├── diagnose.sh               # 故障诊断脚本
│   ├── cleanup.sh                # 环境清理脚本
│   ├── db-manager.sh             # 数据库管理脚本
│   └── frontend-manager.sh       # 前端管理脚本
├── 📚 项目文档/                   # 项目文档
│   ├── 架构愿景与原则.md
│   ├── 柴管家系统架构.md
│   ├── 柴管家 - 产品需求文档.md
│   ├── 开发方案/
│   ├── 运维指南/
│   └── 项目规范/
├── 📚 docs/                       # 开发文档
│   ├── api/                      # API文档
│   ├── architecture/             # 架构文档
│   ├── deployment/               # 部署文档
│   └── development/              # 开发指南
├── 🧪 tests/                      # 端到端测试
├── 📋 user_stories/               # 用户故事
├── � .github/                    # GitHub配置
│   └── workflows/                # CI/CD工作流
├── docker-compose.yml             # 开发环境配置
├── docker-compose.prod.yml        # 生产环境配置
└── README.md                      # 项目说明
```

### 🏗️ 模块化架构设计

每个业务模块采用统一的**三层架构**：

```
modules/{module_name}/
├── api/                          # API层
│   ├── routers.py               # 路由定义
│   ├── schemas.py               # 请求/响应模型
│   └── dependencies.py         # 依赖注入
├── services/                    # 业务层
│   └── {module_name}_service.py # 业务逻辑
├── models/                      # 数据层
│   └── {module_name}.py         # 数据模型
└── tests/                       # 测试层
    ├── test_api.py              # API测试
    ├── test_services.py         # 业务逻辑测试
    └── features/                # BDD行为测试
```

## 🔧 故障排除

### 🚀 快速故障排查

✅ **项目已解决所有已知 Docker 问题**，如遇到新问题请使用以下工具：

```bash
# 1. 运行完整诊断
./scripts/diagnose.sh

# 2. 检查服务健康状态
./scripts/health-check.sh

# 3. 查看详细的故障排查指南
cat 项目文档/运维指南/Docker避坑指南.md
```

### 常见问题及解决方案

1. **Docker 构建失败**

   ```bash
   # 使用优化的构建脚本（已解决代理问题）
   docker-compose build

   # 如仍有问题，使用诊断工具
   ./scripts/diagnose.sh --build
   ```

2. **服务无法启动**

   ```bash
   # 运行服务诊断
   ./scripts/diagnose.sh [service_name]

   # 查看服务日志
   docker-compose logs -f [service_name]
   ```

3. **网络连接问题**

   ```bash
   # 网络诊断（已优化网络配置）
   ./scripts/diagnose.sh --network

   # 重置网络（如需要）
   docker-compose down && docker-compose up -d
   ```

4. **性能问题**

   ```bash
   # 性能监控
   ./scripts/diagnose.sh --performance

   # 清理系统资源
   ./scripts/cleanup.sh --medium
   ```

5. **Celery 任务问题**

   ```bash
   # 检查Celery Worker状态
   docker exec chaiguanjia-celery-worker celery -A app.core.celery inspect ping

   # 查看Flower监控界面
   open *************************************
   ```

### 📚 详细故障排查指南

- 📖 [Docker 避坑指南](项目文档/运维指南/Docker避坑指南.md) - 完整的问题解决方案
- 🔧 [Docker 工具快速参考](项目文档/运维指南/Docker工具快速参考.md) - 命令速查手册
- 🌐 [Docker 网络配置指南](项目文档/运维指南/Docker网络配置指南.md) - 网络配置详解

## 🎯 开发规范

### 📋 代码规范

项目严格遵循以下代码规范：

#### 命名规范

- **Python 文件**: snake_case (`user_service.py`)
- **Python 类**: PascalCase (`UserService`)
- **Python 函数**: snake_case (`get_user_by_id()`)
- **Python 常量**: UPPER_SNAKE_CASE (`MAX_RETRY_COUNT`)
- **React 组件**: PascalCase (`UserProfile.tsx`)
- **TypeScript 文件**: camelCase (`userService.ts`)

#### 项目结构规范

- **模块化单体架构**：API 层 → 业务层 → 数据层 → 基础设施层
- **严格分层**：禁止跨层直接调用
- **统一标准**：所有模块遵循相同结构

#### 代码质量工具

- **前端**: ESLint + Prettier + TypeScript 严格模式
- **后端**: Black + isort + flake8 + mypy
- **提交规范**: Conventional Commits
- **Git Hooks**: pre-commit 自动检查

### 🧪 测试策略

```bash
# 前端测试
./scripts/frontend-manager.sh test

# 前端测试覆盖率
./scripts/frontend-manager.sh test:coverage

# 后端测试
cd backend && pytest

# 后端测试覆盖率
cd backend && pytest --cov=app --cov-report=html

# 集成测试
./scripts/test-dev-environment.sh
```

### 🔄 开发流程

1. **创建功能分支**

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **遵循代码规范**

   - 使用 pre-commit hooks 自动检查
   - 确保所有测试通过
   - 保持代码覆盖率 > 80%

3. **提交代码**

   ```bash
   git commit -m "feat: add user authentication module"
   ```

4. **创建 Pull Request**
   - 填写 PR 模板
   - 等待 CI/CD 检查通过
   - 请求代码审查

## 🚀 部署

### 🏭 生产环境部署

**当前状态：开发环境就绪，生产部署配置完善**

```bash
# 1. 备份重要数据
./scripts/cleanup.sh --backup

# 2. 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 3. 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 4. 验证部署状态
./scripts/health-check.sh

# 5. 监控服务状态
open *************************************  # Flower监控
```

### 🔧 环境变量配置

复制并修改环境变量文件：

```bash
cp .env.example .env
```

#### 核心配置项

```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/chaiguanjia
REDIS_URL=redis://localhost:6379

# AI服务配置
TONGYI_API_KEY=your_tongyi_api_key
TONGYI_API_URL=https://dashscope.aliyuncs.com/api/v1/

# 消息队列配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672

# 安全配置
SECRET_KEY=your_secret_key_here
CORS_ORIGINS=["http://localhost:3000","http://localhost:5173"]

# 监控配置
FLOWER_BASIC_AUTH=admin:flower123
```

### 🔍 监控和健康检查

项目集成了渐进式监控体系：

#### MVP 阶段监控

- **健康检查端点**: `/health`, `/metrics`
- **应用内置监控**: FastAPI 集成基础指标
- **结构化日志**: JSON 格式文件日志
- **基础告警**: 邮件/微信通知

#### 成长阶段扩展

- **Prometheus + Grafana**: 高级监控和可视化
- **ELK Stack**: 日志聚合和分析
- **APM 工具**: 应用性能监控

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

### 📋 贡献流程

1. **Fork 项目**

   ```bash
   # 在GitHub上Fork项目到你的账户
   ```

2. **克隆到本地**

   ```bash
   git clone https://github.com/your-username/chaiguanjia_8.4.git
   cd chaiguanjia_8.4
   ```

3. **创建功能分支**

   ```bash
   git checkout -b feature/your-feature-name
   ```

4. **开发和测试**

   - 遵循项目代码规范
   - 编写相应的测试用例
   - 确保所有测试通过

5. **提交更改**

   ```bash
   git commit -m "feat: add your amazing feature"
   ```

6. **推送分支**

   ```bash
   git push origin feature/your-feature-name
   ```

7. **创建 Pull Request**
   - 填写详细的 PR 描述
   - 关联相关的 Issue
   - 等待代码审查

### � 贡献类型

- 🐛 **Bug 修复**: 修复已知问题
- ✨ **新功能**: 添加新的功能特性
- 📚 **文档**: 改进项目文档
- 🎨 **代码优化**: 提升代码质量
- 🧪 **测试**: 增加测试覆盖率

## �📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## � 联系我们

- **项目仓库**: [GitHub](https://github.com/Amoresdk/chaiguanjia_8.4)
- **问题反馈**: [Issues](https://github.com/Amoresdk/chaiguanjia_8.4/issues)
- **功能建议**: [Discussions](https://github.com/Amoresdk/chaiguanjia_8.4/discussions)

## 📊 项目信息

- **版本**: v0.1.0 (MVP 开发中)
- **开发语言**: Python 3.11+, TypeScript 5.x
- **架构模式**: 模块化单体架构
- **部署方式**: Docker 容器化
- **开发团队**: 柴管家开发团队

## 🎉 致谢

感谢所有为项目贡献代码、文档和建议的开发者们！

特别感谢以下技术和工具：

- [FastAPI](https://fastapi.tiangolo.com/) - 高性能 Python Web 框架
- [React](https://reactjs.org/) - 用户界面库
- [Docker](https://www.docker.com/) - 容器化平台
- [通义千问](https://tongyi.aliyun.com/) - AI 对话服务

---

<div align="center">
  <p>如果这个项目对您有帮助，请给我们一个 ⭐️</p>
  <p>Made with ❤️ by 柴管家团队</p>
  <p>🤖 <strong>智能私域运营中枢 | AI-Powered Customer Service Platform</strong> 🤖</p>
</div>
