# 柴管家项目贡献指南

欢迎参与柴管家项目的开发！本文档将指导您如何为项目做出贡献。

## 🚀 快速开始

### 环境要求

- **Node.js**: 18.0+
- **Python**: 3.11+
- **Docker**: 20.10+
- **Git**: 2.30+

### 开发环境搭建

1. **克隆仓库**
   ```bash
   git clone https://github.com/your-org/chaiguanjia.git
   cd chaiguanjia
   ```

2. **启动开发环境**
   ```bash
   # 一键启动所有服务
   docker-compose up -d
   
   # 查看服务状态
   docker-compose ps
   ```

3. **验证环境**
   ```bash
   # 前端开发服务器: http://localhost:3000
   # 后端API服务器: http://localhost:8000
   # API文档: http://localhost:8000/docs
   ```

## 📋 贡献流程

### 1. 准备工作

#### Fork 和 Clone
```bash
# 1. 在GitHub上Fork项目
# 2. 克隆您的Fork
git clone https://github.com/YOUR_USERNAME/chaiguanjia.git
cd chaiguanjia

# 3. 添加上游仓库
git remote add upstream https://github.com/original-org/chaiguanjia.git
```

#### 同步最新代码
```bash
git checkout develop
git pull upstream develop
git push origin develop
```

### 2. 创建功能分支

```bash
# 从develop分支创建功能分支
git checkout develop
git checkout -b feature/功能描述

# 分支命名规范:
# feature/用户认证系统
# fix/登录页面样式问题
# docs/API文档更新
# refactor/用户服务重构
```

### 3. 开发和提交

#### 代码开发
- 遵循项目的代码规范和架构原则
- 编写必要的单元测试
- 确保所有测试通过
- 更新相关文档

#### 提交规范
严格遵循 [Conventional Commits](./docs/COMMIT_CONVENTION.md) 规范：

```bash
# 提交格式
git commit -m "type(scope): description"

# 示例
git commit -m "feat(auth): 添加用户登录功能"
git commit -m "fix(api): 修复用户信息获取接口错误"
git commit -m "docs(readme): 更新安装说明"
```

### 4. 推送和创建PR

```bash
# 推送到您的Fork
git push origin feature/功能描述

# 在GitHub上创建Pull Request
# 目标分支: develop (功能开发)
# 目标分支: main (紧急修复)
```

### 5. 代码审查

- 等待自动化检查通过
- 响应审查者的反馈
- 根据建议修改代码
- 解决所有对话

### 6. 合并

- 所有检查通过后，维护者将合并您的PR
- 删除功能分支
- 同步最新的develop分支

## 🎯 开发规范

### 代码风格

#### 前端 (React + TypeScript)
- 使用 **ESLint** + **Prettier** 自动格式化
- 遵循 **Airbnb** 代码规范
- 使用 **TypeScript 严格模式**
- 组件使用 **PascalCase** 命名
- 文件使用 **camelCase** 命名

#### 后端 (Python + FastAPI)
- 使用 **Black** 自动格式化
- 使用 **isort** 排序导入
- 使用 **flake8** 语法检查
- 使用 **mypy** 类型检查
- 遵循 **PEP 8** 规范

### 项目结构

严格遵循 [项目结构规范](./docs/PROJECT_STRUCTURE.md)：

```
chaiguanjia/
├── frontend/src/
│   ├── components/     # React组件
│   ├── pages/         # 页面组件
│   ├── services/      # API服务
│   └── utils/         # 工具函数
├── backend/app/
│   ├── modules/       # 业务模块
│   ├── core/          # 核心配置
│   └── shared/        # 共享组件
└── docs/              # 项目文档
```

### 测试要求

#### 单元测试
- **前端**: Jest + React Testing Library
- **后端**: pytest + coverage
- **覆盖率要求**: ≥ 80%

#### 集成测试
- API接口测试
- 数据库集成测试
- 服务间通信测试

#### E2E测试
- 关键用户流程测试
- 跨浏览器兼容性测试

### 文档要求

- **代码注释**: 解释复杂逻辑和业务规则
- **API文档**: 使用FastAPI自动生成
- **README更新**: 新功能需要更新使用说明
- **CHANGELOG**: 重要变更需要记录

## 🔍 质量检查

### 自动化检查

每次提交都会触发以下检查：

#### 代码质量
- [ ] ESLint检查通过
- [ ] Black格式化通过
- [ ] TypeScript编译通过
- [ ] Python类型检查通过

#### 测试检查
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 代码覆盖率 ≥ 80%

#### 安全检查
- [ ] 依赖漏洞扫描通过
- [ ] 代码安全分析通过

#### 构建检查
- [ ] 前端构建成功
- [ ] 后端构建成功
- [ ] Docker镜像构建成功

### 本地检查

提交前请运行本地检查：

```bash
# 前端检查
cd frontend
npm run lint
npm run type-check
npm run test
npm run build

# 后端检查
cd backend
black --check .
isort --check-only .
flake8 .
mypy .
pytest --cov=app tests/

# 全项目检查
docker-compose run --rm test
```

## 🐛 问题报告

### Bug报告

使用 [Bug报告模板](.github/ISSUE_TEMPLATE/bug_report.md)：

1. **环境信息**: 操作系统、浏览器、版本等
2. **重现步骤**: 详细的操作步骤
3. **期望行为**: 应该发生什么
4. **实际行为**: 实际发生了什么
5. **截图/日志**: 相关的错误信息

### 功能请求

使用 [功能请求模板](.github/ISSUE_TEMPLATE/feature_request.md)：

1. **问题描述**: 当前的痛点
2. **解决方案**: 建议的解决方案
3. **替代方案**: 其他可能的解决方案
4. **附加信息**: 相关的背景信息

## 📞 获取帮助

### 沟通渠道

- **GitHub Issues**: 技术问题和功能讨论
- **GitHub Discussions**: 一般性讨论和问答
- **项目文档**: 查看详细的技术文档

### 常见问题

#### 开发环境问题
- **Docker启动失败**: 检查Docker版本和权限
- **端口冲突**: 修改docker-compose.yml中的端口映射
- **依赖安装失败**: 清理缓存后重新安装

#### 代码提交问题
- **提交信息格式错误**: 查看提交规范文档
- **代码格式检查失败**: 运行自动格式化工具
- **测试失败**: 检查测试用例和代码逻辑

## 🏆 贡献者认可

### 贡献类型

我们认可以下类型的贡献：

- 💻 **代码贡献**: 新功能、Bug修复、性能优化
- 📖 **文档贡献**: 文档改进、翻译、示例
- 🎨 **设计贡献**: UI/UX设计、图标、插图
- 🐛 **测试贡献**: 测试用例、Bug报告、质量保证
- 💡 **想法贡献**: 功能建议、架构讨论、最佳实践

### 贡献者列表

所有贡献者都会在项目README中得到认可。

## 📜 行为准则

### 我们的承诺

为了营造开放和友好的环境，我们承诺：

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 不可接受的行为

- 使用性别化语言或图像，以及不受欢迎的性关注或性骚扰
- 恶意评论、人身攻击或政治攻击
- 公开或私下骚扰
- 未经明确许可发布他人的私人信息
- 在专业环境中可能被认为不当的其他行为

## 📄 许可证

通过贡献代码，您同意您的贡献将在与项目相同的许可证下获得许可。

---

**感谢您对柴管家项目的贡献！** 🎉
