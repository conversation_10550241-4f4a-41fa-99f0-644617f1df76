# 柴管家项目日志轮转配置

# 全局配置
weekly
rotate 4
compress
delaycompress
missingok
notifempty
create 644 root root

# 应用日志轮转
/var/log/chaiguanjia/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 chaiguanjia chaiguanjia
    postrotate
        # 重新加载应用以使用新的日志文件
        /usr/bin/docker-compose exec backend pkill -USR1 uvicorn || true
    endscript
}

# Nginx访问日志
/var/log/nginx/access.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 644 nginx nginx
    postrotate
        /usr/bin/docker-compose exec nginx nginx -s reload || true
    endscript
}

# Nginx错误日志
/var/log/nginx/error.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 644 nginx nginx
    postrotate
        /usr/bin/docker-compose exec nginx nginx -s reload || true
    endscript
}

# PostgreSQL日志
/var/log/postgresql/*.log {
    weekly
    rotate 8
    compress
    delaycompress
    missingok
    notifempty
    create 644 postgres postgres
}

# Redis日志
/var/log/redis/*.log {
    weekly
    rotate 4
    compress
    delaycompress
    missingok
    notifempty
    create 644 redis redis
}

# Celery日志
/var/log/celery/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 chaiguanjia chaiguanjia
    postrotate
        # 重启Celery worker以使用新的日志文件
        /usr/bin/docker-compose restart celery-worker celery-beat || true
    endscript
}
