{"Servers": {"1": {"Name": "柴管家开发数据库", "Group": "开发环境", "Host": "postgres", "Port": 5432, "MaintenanceDB": "chaiguanjia", "Username": "chaiguanjia", "PassFile": "/pgpass", "SSLMode": "prefer", "SSLCert": "<STORAGE_DIR>/.postgresql/postgresql.crt", "SSLKey": "<STORAGE_DIR>/.postgresql/postgresql.key", "SSLRootCert": "<STORAGE_DIR>/.postgresql/root.crt", "SSLCrl": "<STORAGE_DIR>/.postgresql/root.crl", "SSLCompression": 0, "Timeout": 10, "UseSSHTunnel": 0, "TunnelHost": "", "TunnelPort": "22", "TunnelUsername": "", "TunnelAuthentication": 0, "BGColor": "#3498db", "FGColor": "#ffffff", "Service": "", "Comment": "柴管家项目开发环境数据库连接"}, "2": {"Name": "柴管家测试数据库", "Group": "测试环境", "Host": "postgres", "Port": 5432, "MaintenanceDB": "chaiguanjia_test", "Username": "chaiguanjia", "PassFile": "/pgpass", "SSLMode": "prefer", "BGColor": "#f39c12", "FGColor": "#ffffff", "Comment": "柴管家项目测试环境数据库连接"}}}