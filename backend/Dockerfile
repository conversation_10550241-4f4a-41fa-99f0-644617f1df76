# 柴管家后端 FastAPI 容器化配置
# 多阶段构建：开发环境 + 生产环境

# ================================
# 基础镜像阶段
# ================================
FROM python:3.11-slim AS base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
  PYTHONDONTWRITEBYTECODE=1 \
  PIP_NO_CACHE_DIR=1 \
  PIP_DISABLE_PIP_VERSION_CHECK=1

# 代理配置（构建时传入）
ARG http_proxy
ARG https_proxy
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG no_proxy=localhost,127.0.0.1
ARG NO_PROXY=localhost,127.0.0.1

# 设置代理环境变量（仅在构建时生效）
ENV http_proxy=$http_proxy \
  https_proxy=$https_proxy \
  HTTP_PROXY=$HTTP_PROXY \
  HTTPS_PROXY=$HTTPS_PROXY \
  no_proxy=$no_proxy \
  NO_PROXY=$NO_PROXY

# 安装系统依赖（使用预安装的包）
RUN apt-get update && apt-get install -y \
  gcc \
  libc6-dev \
  libpq-dev \
  curl \
  && rm -rf /var/lib/apt/lists/* || echo "Package installation failed, continuing..."

# 创建应用用户
RUN groupadd -r chaiguanjia && useradd -r -g chaiguanjia chaiguanjia

# 设置工作目录
WORKDIR /app

# ================================
# 依赖安装阶段
# ================================
FROM base AS dependencies

# 复制依赖文件
COPY requirements.txt requirements-dev.txt ./

# 安装Python依赖
RUN unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY && \
  pip install --upgrade pip && \
  pip install -r requirements.txt

# ================================
# 开发环境阶段
# ================================
FROM dependencies AS development

# 安装开发依赖
RUN pip install -r requirements-dev.txt

# 复制应用代码
COPY . .

# 复制启动脚本
COPY scripts/start-dev.sh /usr/local/bin/start-dev.sh
RUN chmod +x /usr/local/bin/start-dev.sh

# 更改文件所有权
RUN chown -R chaiguanjia:chaiguanjia /app

# 切换到应用用户
USER chaiguanjia

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# 启动命令（开发模式 - 支持热重载和数据库初始化）
CMD ["/usr/local/bin/start-dev.sh"]

# ================================
# 生产环境阶段
# ================================
FROM dependencies AS production

# 复制应用代码
COPY . .

# 更改文件所有权
RUN chown -R chaiguanjia:chaiguanjia /app

# 切换到应用用户
USER chaiguanjia

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# 启动命令（生产模式）
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
