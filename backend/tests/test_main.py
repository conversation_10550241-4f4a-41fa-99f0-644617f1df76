"""
柴管家后端主应用测试
测试 FastAPI 应用的基本功能
"""

import pytest
from fastapi.testclient import TestClient


class TestMainApp:
    """主应用测试类"""

    def test_health_check(self, client: TestClient):
        """测试健康检查端点"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data

    def test_root_endpoint(self, client: TestClient):
        """测试根端点"""
        response = client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "柴管家" in data["message"]

    def test_docs_endpoint(self, client: TestClient):
        """测试 API 文档端点"""
        response = client.get("/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]

    def test_openapi_endpoint(self, client: TestClient):
        """测试 OpenAPI 规范端点"""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert data["info"]["title"] == "柴管家 API"

    @pytest.mark.integration
    def test_metrics_endpoint(self, client: TestClient):
        """测试指标端点"""
        response = client.get("/metrics")
        assert response.status_code == 200
        
        # 检查 Prometheus 格式的指标
        content = response.text
        assert "# HELP" in content or "# TYPE" in content

    def test_cors_headers(self, client: TestClient):
        """测试 CORS 头部"""
        response = client.options("/", headers={
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "GET",
        })
        
        # 检查 CORS 头部
        assert "access-control-allow-origin" in response.headers
        assert "access-control-allow-methods" in response.headers

    def test_404_error(self, client: TestClient):
        """测试 404 错误处理"""
        response = client.get("/nonexistent-endpoint")
        assert response.status_code == 404
        
        data = response.json()
        assert "detail" in data

    def test_method_not_allowed(self, client: TestClient):
        """测试方法不允许错误"""
        response = client.post("/health")
        assert response.status_code == 405
        
        data = response.json()
        assert "detail" in data


@pytest.mark.unit
class TestUtilityFunctions:
    """工具函数测试类"""

    def test_environment_variables(self):
        """测试环境变量设置"""
        import os
        assert os.getenv("TESTING") == "true"

    def test_database_url_format(self):
        """测试数据库 URL 格式"""
        from app.core.config import settings
        assert settings.database_url is not None
        assert isinstance(settings.database_url, str)

    def test_redis_url_format(self):
        """测试 Redis URL 格式"""
        from app.core.config import settings
        assert settings.redis_url is not None
        assert isinstance(settings.redis_url, str)
