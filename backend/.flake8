[flake8]
# 最大行长度（与 Black 保持一致）
max-line-length = 88

# 最大复杂度
max-complexity = 10

# 排除目录
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .env,
    env,
    dist,
    build,
    migrations,
    .mypy_cache,
    .pytest_cache,
    .coverage,
    htmlcov,
    *.egg-info

# 忽略的错误代码
ignore = 
    # E203: 切片中的空格（与 Black 冲突）
    E203,
    # E501: 行太长（由 Black 处理）
    E501,
    # W503: 二元运算符前的换行（与 Black 冲突）
    W503,
    # E231: 逗号后缺少空格（由 Black 处理）
    E231,

# 每个文件的最大错误数
per-file-ignores = 
    # 测试文件允许更宽松的规则
    tests/*:F401,F811,F841
    # __init__.py 文件允许未使用的导入
    __init__.py:F401
    # 配置文件允许星号导入
    */settings.py:F403,F405
    */config.py:F403,F405

# 选择的错误代码
select = 
    # pycodestyle 错误
    E,
    # pycodestyle 警告
    W,
    # pyflakes
    F,
    # flake8-bugbear
    B,
    # flake8-comprehensions
    C4,
    # flake8-simplify
    SIM,

# 导入顺序检查
import-order-style = google
application-import-names = app

# 文档字符串检查
docstring-convention = google
require-return-section-when-returning-value = true
require-return-section-when-returning-nothing = false
