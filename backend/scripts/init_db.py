#!/usr/bin/env python3
"""数据库初始化脚本 - 创建表结构和初始数据."""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.shared.database.base import Base

# 导入所有模型以确保它们被注册
from app.modules.user_management.models.user import User


async def create_database_tables():
    """创建数据库表结构."""
    # 获取数据库URL
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        postgres_user = os.getenv("POSTGRES_USER", "chaiguanjia")
        postgres_password = os.getenv("POSTGRES_PASSWORD", "chaiguanjia123")
        postgres_host = os.getenv("POSTGRES_HOST", "postgres")
        postgres_port = os.getenv("POSTGRES_PORT", "5432")
        postgres_db = os.getenv("POSTGRES_DB", "chaiguanjia")
        
        database_url = f"postgresql+asyncpg://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/{postgres_db}"
    
    print(f"连接数据库: {database_url.replace(postgres_password, '***')}")
    
    # 创建异步引擎
    engine = create_async_engine(database_url, echo=True)
    
    try:
        # 创建所有表
        async with engine.begin() as conn:
            print("创建数据库表结构...")
            await conn.run_sync(Base.metadata.create_all)
            print("数据库表结构创建完成")
        
        return True
        
    except Exception as e:
        print(f"创建数据库表失败: {e}")
        return False
        
    finally:
        await engine.dispose()


async def create_initial_data():
    """创建初始数据."""
    # 获取数据库URL
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        postgres_user = os.getenv("POSTGRES_USER", "chaiguanjia")
        postgres_password = os.getenv("POSTGRES_PASSWORD", "chaiguanjia123")
        postgres_host = os.getenv("POSTGRES_HOST", "postgres")
        postgres_port = os.getenv("POSTGRES_PORT", "5432")
        postgres_db = os.getenv("POSTGRES_DB", "chaiguanjia")
        
        database_url = f"postgresql+asyncpg://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/{postgres_db}"
    
    # 创建异步引擎和会话
    engine = create_async_engine(database_url)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    try:
        async with async_session() as session:
            # 检查是否已有管理员用户
            from sqlalchemy import select
            result = await session.execute(
                select(User).where(User.username == "admin")
            )
            admin_user = result.scalar_one_or_none()
            
            if not admin_user:
                print("创建默认管理员用户...")
                
                # 创建管理员用户
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    full_name="系统管理员",
                    password_hash="$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QJw/2Oy6W",  # password: admin123
                    is_active=True,
                    is_verified=True,
                    is_superuser=True
                )
                session.add(admin_user)
                
                # 创建测试用户
                test_user = User(
                    username="testuser",
                    email="<EMAIL>",
                    full_name="测试用户",
                    password_hash="$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QJw/2Oy6W",  # password: test123
                    is_active=True,
                    is_verified=True,
                    is_superuser=False
                )
                session.add(test_user)
                
                await session.commit()
                print("默认用户创建完成")
            else:
                print("管理员用户已存在，跳过创建")
        
        return True
        
    except Exception as e:
        print(f"创建初始数据失败: {e}")
        return False
        
    finally:
        await engine.dispose()


async def main():
    """主函数."""
    print("开始初始化数据库...")
    
    # 创建表结构
    if not await create_database_tables():
        print("数据库表创建失败")
        sys.exit(1)
    
    # 创建初始数据
    if not await create_initial_data():
        print("初始数据创建失败")
        sys.exit(1)
    
    print("数据库初始化完成！")


if __name__ == "__main__":
    asyncio.run(main())
