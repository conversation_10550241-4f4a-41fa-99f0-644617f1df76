#!/bin/bash

# 柴管家后端开发环境启动脚本
# 包含数据库初始化、迁移和应用启动

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待数据库就绪
wait_for_database() {
    log_info "等待数据库连接..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if python -c "
import asyncpg
import asyncio
import os

async def check_db():
    try:
        postgres_user = os.getenv('POSTGRES_USER', 'chaiguanjia')
        postgres_password = os.getenv('POSTGRES_PASSWORD', 'chaiguanjia123')
        postgres_host = os.getenv('POSTGRES_HOST', 'postgres')
        postgres_port = os.getenv('POSTGRES_PORT', '5432')
        postgres_db = os.getenv('POSTGRES_DB', 'chaiguanjia')
        
        conn = await asyncpg.connect(
            user=postgres_user,
            password=postgres_password,
            host=postgres_host,
            port=postgres_port,
            database=postgres_db
        )
        await conn.close()
        return True
    except Exception as e:
        print(f'Database connection failed: {e}')
        return False

result = asyncio.run(check_db())
exit(0 if result else 1)
"; then
            log_success "数据库连接成功"
            return 0
        fi
        
        log_info "等待数据库连接... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "数据库连接超时"
    return 1
}

# 运行数据库迁移
run_migrations() {
    log_info "检查并运行数据库迁移..."
    
    # 检查是否有迁移文件
    if [ -d "alembic/versions" ] && [ "$(ls -A alembic/versions)" ]; then
        log_info "运行Alembic迁移..."
        alembic upgrade head
        log_success "数据库迁移完成"
    else
        log_info "没有发现迁移文件，初始化数据库..."
        python scripts/init_db.py
        log_success "数据库初始化完成"
    fi
}

# 检查应用健康状态
check_app_health() {
    log_info "检查应用健康状态..."
    
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:8000/health > /dev/null 2>&1; then
            log_success "应用健康检查通过"
            return 0
        fi
        
        log_info "等待应用启动... ($attempt/$max_attempts)"
        sleep 3
        attempt=$((attempt + 1))
    done
    
    log_warning "应用健康检查超时，但继续运行"
    return 0
}

# 主函数
main() {
    log_info "启动柴管家后端开发环境..."
    
    # 等待数据库就绪
    if ! wait_for_database; then
        log_error "数据库连接失败，退出"
        exit 1
    fi
    
    # 运行数据库迁移
    if ! run_migrations; then
        log_error "数据库迁移失败，退出"
        exit 1
    fi
    
    # 启动应用
    log_info "启动FastAPI应用..."
    log_info "开发模式：热重载已启用"
    log_info "API文档：http://localhost:8000/docs"
    log_info "健康检查：http://localhost:8000/health"
    
    # 在后台启动应用
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level info &
    local app_pid=$!
    
    # 等待应用启动
    sleep 5
    
    # 检查应用健康状态
    check_app_health
    
    log_success "柴管家后端服务启动完成！"
    
    # 等待应用进程
    wait $app_pid
}

# 信号处理
trap 'log_info "收到停止信号，正在关闭..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
