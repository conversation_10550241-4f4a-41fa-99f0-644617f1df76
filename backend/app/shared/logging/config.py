"""
柴管家项目日志配置模块
定义不同环境下的日志配置策略
"""

import os
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class Environment(str, Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class LogConfig(BaseModel):
    """日志配置模型"""

    # 基础配置
    level: LogLevel = Field(default=LogLevel.INFO, description="日志级别")
    format_type: str = Field(default="json", description="日志格式类型")

    # 文件配置
    log_dir: Path = Field(default=Path("logs"), description="日志目录")
    log_file: str = Field(default="app.log", description="日志文件名")
    max_file_size: int = Field(default=10 * 1024 * 1024, description="单个日志文件最大大小(字节)")
    backup_count: int = Field(default=5, description="日志文件备份数量")

    # 控制台配置
    console_enabled: bool = Field(default=True, description="是否启用控制台输出")
    console_level: LogLevel = Field(default=LogLevel.INFO, description="控制台日志级别")

    # 业务日志配置
    business_log_enabled: bool = Field(default=True, description="是否启用业务日志")
    business_log_file: str = Field(default="business.log", description="业务日志文件名")

    # 性能配置
    async_logging: bool = Field(default=True, description="是否启用异步日志")
    buffer_size: int = Field(default=1000, description="日志缓冲区大小")

    # 环境特定配置
    environment: Environment = Field(default=Environment.DEVELOPMENT, description="运行环境")

    class Config:
        use_enum_values = True


def get_environment_config(env: Environment) -> Dict[str, Any]:
    """获取环境特定的日志配置"""

    configs = {
        Environment.DEVELOPMENT: {
            "level": LogLevel.DEBUG,
            "console_enabled": True,
            "console_level": LogLevel.DEBUG,
            "format_type": "structured",  # 开发环境使用可读格式
            "async_logging": False,  # 开发环境同步日志便于调试
        },
        Environment.TESTING: {
            "level": LogLevel.WARNING,
            "console_enabled": False,
            "console_level": LogLevel.ERROR,
            "format_type": "json",
            "async_logging": False,
        },
        Environment.STAGING: {
            "level": LogLevel.INFO,
            "console_enabled": True,
            "console_level": LogLevel.INFO,
            "format_type": "json",
            "async_logging": True,
        },
        Environment.PRODUCTION: {
            "level": LogLevel.INFO,
            "console_enabled": False,
            "console_level": LogLevel.ERROR,
            "format_type": "json",
            "async_logging": True,
            "max_file_size": 50 * 1024 * 1024,  # 生产环境更大的文件
            "backup_count": 10,
        }
    }

    return configs.get(env, configs[Environment.DEVELOPMENT])


def get_log_config() -> LogConfig:
    """获取当前环境的日志配置"""

    # 从环境变量获取配置
    env_name = os.getenv("APP_ENV", "development").lower()
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    log_dir = os.getenv("LOG_DIR", "logs")

    # 确定环境类型
    try:
        environment = Environment(env_name)
    except ValueError:
        environment = Environment.DEVELOPMENT

    # 获取环境特定配置
    env_config = get_environment_config(environment)

    # 处理级别覆盖
    if log_level in LogLevel.__members__:
        env_config["level"] = LogLevel(log_level)

    # 创建配置对象
    config = LogConfig(
        environment=environment,
        log_dir=Path(log_dir),
        **env_config
    )

    # 确保日志目录存在
    config.log_dir.mkdir(parents=True, exist_ok=True)

    return config


# 全局配置实例
_log_config: Optional[LogConfig] = None


def get_current_config() -> LogConfig:
    """获取当前的日志配置实例"""
    global _log_config
    if _log_config is None:
        _log_config = get_log_config()
    return _log_config


def update_config(new_config: LogConfig) -> None:
    """更新全局日志配置"""
    global _log_config
    _log_config = new_config
