"""
柴管家项目FastAPI日志中间件
确保所有API请求都有完整的日志记录
"""

import time
import uuid
import json
from typing import Callable, Optional, Dict, Any
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
from fastapi import FastAPI

from .logger import get_logger, set_request_context, clear_request_context
from .business_logger import get_business_logger, EventType, EventStatus


class LoggingMiddleware(BaseHTTPMiddleware):
    """API请求日志中间件"""
    
    def __init__(self, app: FastAPI, logger_name: str = "api"):
        super().__init__(app)
        self.logger = get_logger(logger_name)
        self.business_logger = get_business_logger()
        
        # 配置需要记录的路径
        self.exclude_paths = {
            "/health",
            "/metrics", 
            "/docs",
            "/redoc",
            "/openapi.json",
            "/favicon.ico"
        }
        
        # 配置敏感字段，在日志中需要脱敏
        self.sensitive_fields = {
            "password", "token", "secret", "key", "authorization",
            "cookie", "session", "csrf", "api_key"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并记录日志"""
        
        # 生成请求ID和追踪ID
        request_id = str(uuid.uuid4())
        trace_id = request.headers.get("X-Trace-ID", str(uuid.uuid4()))
        
        # 设置请求上下文
        set_request_context(request_id=request_id, trace_id=trace_id)
        
        # 在请求对象中添加ID，供后续使用
        request.state.request_id = request_id
        request.state.trace_id = trace_id
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 检查是否需要记录此路径
        should_log = request.url.path not in self.exclude_paths
        
        # 记录请求开始
        if should_log:
            await self._log_request_start(request, request_id, trace_id)
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录请求完成
            if should_log:
                await self._log_request_end(
                    request, response, request_id, trace_id, process_time
                )
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Trace-ID"] = trace_id
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录请求错误
            if should_log:
                await self._log_request_error(
                    request, e, request_id, trace_id, process_time
                )
            
            raise
        
        finally:
            # 清除请求上下文
            clear_request_context()
    
    async def _log_request_start(self, request: Request, request_id: str, trace_id: str) -> None:
        """记录请求开始"""
        
        # 获取请求信息
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "")
        
        # 构建请求数据（脱敏处理）
        request_data = {
            'method': request.method,
            'url': str(request.url),
            'path': request.url.path,
            'query_params': dict(request.query_params),
            'headers': self._sanitize_headers(dict(request.headers)),
            'client_ip': client_ip,
            'user_agent': user_agent,
        }
        
        # 记录请求体（对于POST/PUT请求）
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await self._get_request_body(request)
                if body:
                    request_data['body'] = self._sanitize_data(body)
            except Exception as e:
                request_data['body_error'] = str(e)
        
        # 记录应用日志
        self.logger.info(
            f"API请求开始: {request.method} {request.url.path}",
            extra={
                'extra_fields': request_data,
                'request_id': request_id,
                'trace_id': trace_id,
            }
        )
    
    async def _log_request_end(self, request: Request, response: Response, 
                              request_id: str, trace_id: str, process_time: float) -> None:
        """记录请求完成"""
        
        # 获取用户ID（如果存在）
        user_id = getattr(request.state, 'user_id', None)
        
        # 构建响应数据
        response_data = {
            'status_code': response.status_code,
            'process_time': process_time,
            'response_size': len(response.body) if hasattr(response, 'body') else 0,
        }
        
        # 记录应用日志
        log_level = 20 if response.status_code < 400 else 40  # INFO or ERROR
        self.logger.log(
            log_level,
            f"API请求完成: {request.method} {request.url.path} - {response.status_code} ({process_time:.3f}s)",
            extra={
                'extra_fields': response_data,
                'request_id': request_id,
                'user_id': user_id,
                'trace_id': trace_id,
            }
        )
        
        # 记录业务事件
        status = EventStatus.SUCCESS if response.status_code < 400 else EventStatus.FAILURE
        self.business_logger.log_simple_event(
            event_type=EventType.SYSTEM_HEALTH_CHECK if request.url.path.startswith('/health') else EventType.USER_LOGIN,
            event_name=f"API请求: {request.method} {request.url.path}",
            request_id=request_id,
            user_id=user_id,
            trace_id=trace_id,
            business_data={
                'method': request.method,
                'path': request.url.path,
                'status_code': response.status_code,
                'process_time': process_time,
            },
            status=status
        )
    
    async def _log_request_error(self, request: Request, error: Exception,
                                request_id: str, trace_id: str, process_time: float) -> None:
        """记录请求错误"""
        
        # 获取用户ID（如果存在）
        user_id = getattr(request.state, 'user_id', None)
        
        # 构建错误数据
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'process_time': process_time,
        }
        
        # 记录应用日志
        self.logger.error(
            f"API请求错误: {request.method} {request.url.path} - {type(error).__name__}: {error}",
            extra={
                'extra_fields': error_data,
                'request_id': request_id,
                'user_id': user_id,
                'trace_id': trace_id,
            },
            exc_info=True
        )
        
        # 记录业务事件
        self.business_logger.log_simple_event(
            event_type=EventType.SYSTEM_ERROR,
            event_name=f"API请求错误: {request.method} {request.url.path}",
            request_id=request_id,
            user_id=user_id,
            trace_id=trace_id,
            business_data={
                'method': request.method,
                'path': request.url.path,
                'error_type': type(error).__name__,
                'error_message': str(error),
                'process_time': process_time,
            },
            status=EventStatus.FAILURE
        )
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 优先从代理头获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 从连接信息获取
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    async def _get_request_body(self, request: Request) -> Optional[Dict[str, Any]]:
        """获取请求体内容"""
        try:
            content_type = request.headers.get("Content-Type", "")
            
            if "application/json" in content_type:
                body = await request.body()
                if body:
                    return json.loads(body.decode())
            elif "application/x-www-form-urlencoded" in content_type:
                form = await request.form()
                return dict(form)
            
        except Exception:
            pass
        
        return None
    
    def _sanitize_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """脱敏处理请求头"""
        sanitized = {}
        for key, value in headers.items():
            key_lower = key.lower()
            if any(sensitive in key_lower for sensitive in self.sensitive_fields):
                sanitized[key] = "***REDACTED***"
            else:
                sanitized[key] = value
        return sanitized
    
    def _sanitize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """脱敏处理数据"""
        if not isinstance(data, dict):
            return data
        
        sanitized = {}
        for key, value in data.items():
            key_lower = key.lower()
            if any(sensitive in key_lower for sensitive in self.sensitive_fields):
                sanitized[key] = "***REDACTED***"
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_data(value)
            else:
                sanitized[key] = value
        
        return sanitized


def setup_logging_middleware(app: FastAPI) -> None:
    """设置日志中间件"""
    app.add_middleware(LoggingMiddleware)
