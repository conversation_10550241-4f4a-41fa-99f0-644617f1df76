"""
柴管家项目日志系统集成示例
演示如何在FastAPI应用中集成和使用日志系统
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from typing import Dict, Any

from .logger import setup_logging, get_logger
from .middleware import setup_logging_middleware
from .business_logger import get_business_logger, EventType, EventStatus
from .module_loggers import get_module_logger


# 应用日志器
app_logger = get_logger("app.main")
business_logger = get_business_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    
    # 启动时初始化日志系统
    app_logger.info("正在初始化柴管家应用...")
    
    # 记录系统启动事件
    business_logger.log_simple_event(
        event_type=EventType.SYSTEM_STARTUP,
        event_name="应用启动",
        business_data={
            "app_name": "chaiguanjia",
            "version": "0.1.0"
        },
        status=EventStatus.SUCCESS
    )
    
    app_logger.info("柴管家应用启动完成")
    
    yield
    
    # 关闭时记录日志
    app_logger.info("正在关闭柴管家应用...")
    
    business_logger.log_simple_event(
        event_type=EventType.SYSTEM_SHUTDOWN,
        event_name="应用关闭",
        business_data={
            "app_name": "chaiguanjia"
        },
        status=EventStatus.SUCCESS
    )
    
    app_logger.info("柴管家应用已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    # 初始化日志系统
    setup_logging()
    
    # 创建应用
    app = FastAPI(
        title="柴管家API",
        description="智能私域运营中枢系统",
        version="0.1.0",
        lifespan=lifespan
    )
    
    # 设置日志中间件
    setup_logging_middleware(app)
    
    # 注册路由
    register_routes(app)
    
    # 注册异常处理器
    register_exception_handlers(app)
    
    return app


def register_routes(app: FastAPI):
    """注册API路由"""
    
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        
        # 记录健康检查事件
        business_logger.log_simple_event(
            event_type=EventType.SYSTEM_HEALTH_CHECK,
            event_name="健康检查",
            business_data={"status": "healthy"},
            status=EventStatus.SUCCESS
        )
        
        return {"status": "healthy", "timestamp": "2024-01-15T10:30:45Z"}
    
    @app.post("/api/v1/users/register")
    async def register_user(request: Request, user_data: Dict[str, Any]):
        """用户注册示例"""
        
        # 获取用户管理模块日志器
        user_logger = get_module_logger("user_management")
        
        try:
            # 模拟用户注册逻辑
            user_id = f"user-{hash(user_data.get('email', ''))}"
            
            # 记录用户注册成功
            user_logger.log_user_register(
                user_id=user_id,
                email=user_data.get("email", ""),
                request_id=getattr(request.state, 'request_id', None),
                success=True
            )
            
            app_logger.info(
                f"用户注册成功: {user_id}",
                extra={
                    'extra_fields': {
                        'user_id': user_id,
                        'email': user_data.get('email'),
                        'operation': 'register'
                    },
                    'request_id': getattr(request.state, 'request_id', None)
                }
            )
            
            return {
                "success": True,
                "user_id": user_id,
                "message": "用户注册成功"
            }
            
        except Exception as e:
            # 记录用户注册失败
            user_logger.log_user_register(
                user_id="",
                email=user_data.get("email", ""),
                request_id=getattr(request.state, 'request_id', None),
                success=False,
                error=e
            )
            
            app_logger.error(
                f"用户注册失败: {e}",
                extra={
                    'extra_fields': {
                        'email': user_data.get('email'),
                        'operation': 'register',
                        'error_type': type(e).__name__
                    },
                    'request_id': getattr(request.state, 'request_id', None)
                },
                exc_info=True
            )
            
            raise HTTPException(status_code=500, detail="用户注册失败")
    
    @app.post("/api/v1/users/login")
    async def login_user(request: Request, login_data: Dict[str, Any]):
        """用户登录示例"""
        
        user_logger = get_module_logger("user_management")
        
        try:
            # 模拟用户登录逻辑
            email = login_data.get("email", "")
            user_id = f"user-{hash(email)}"
            
            # 使用业务日志记录登录过程
            with business_logger.event_context(
                event_type=EventType.USER_LOGIN,
                event_name="用户登录",
                request_id=getattr(request.state, 'request_id', None),
                user_id=user_id,
                business_data={
                    "email": email,
                    "login_method": "email"
                }
            ) as event_id:
                
                # 模拟登录验证过程
                import time
                time.sleep(0.1)  # 模拟验证时间
                
                # 记录模块级别的登录事件
                user_logger.log_user_login(
                    user_id=user_id,
                    login_method="email",
                    request_id=getattr(request.state, 'request_id', None),
                    success=True
                )
                
                app_logger.info(
                    f"用户登录成功: {user_id}",
                    extra={
                        'extra_fields': {
                            'user_id': user_id,
                            'email': email,
                            'operation': 'login',
                            'event_id': event_id
                        },
                        'request_id': getattr(request.state, 'request_id', None),
                        'user_id': user_id
                    }
                )
            
            return {
                "success": True,
                "user_id": user_id,
                "token": "mock-jwt-token",
                "message": "登录成功"
            }
            
        except Exception as e:
            user_logger.log_user_login(
                user_id=user_id if 'user_id' in locals() else "",
                login_method="email",
                request_id=getattr(request.state, 'request_id', None),
                success=False,
                error=e
            )
            
            app_logger.error(
                f"用户登录失败: {e}",
                extra={
                    'extra_fields': {
                        'email': login_data.get('email'),
                        'operation': 'login',
                        'error_type': type(e).__name__
                    },
                    'request_id': getattr(request.state, 'request_id', None)
                },
                exc_info=True
            )
            
            raise HTTPException(status_code=401, detail="登录失败")
    
    @app.post("/api/v1/messages/process")
    async def process_message(request: Request, message_data: Dict[str, Any]):
        """消息处理示例"""
        
        msg_logger = get_module_logger("message_processing")
        
        try:
            message_id = f"msg-{hash(str(message_data))}"
            channel_id = message_data.get("channel_id", "unknown")
            user_id = message_data.get("user_id")
            
            # 记录消息接收
            msg_logger.log_message_flow(
                operation="receive",
                message_id=message_id,
                channel_id=channel_id,
                user_id=user_id,
                request_id=getattr(request.state, 'request_id', None),
                message_type=message_data.get("type", "text"),
                success=True
            )
            
            # 使用业务日志记录消息处理过程
            with business_logger.event_context(
                event_type=EventType.MESSAGE_PROCESS,
                event_name="消息处理",
                request_id=getattr(request.state, 'request_id', None),
                user_id=user_id,
                business_data={
                    "message_id": message_id,
                    "channel_id": channel_id,
                    "message_type": message_data.get("type", "text")
                }
            ) as event_id:
                
                # 模拟消息处理
                import time
                time.sleep(0.2)
                
                # 记录消息发送
                msg_logger.log_message_flow(
                    operation="send",
                    message_id=message_id,
                    channel_id=channel_id,
                    user_id=user_id,
                    request_id=getattr(request.state, 'request_id', None),
                    processing_time=0.2,
                    success=True
                )
            
            return {
                "success": True,
                "message_id": message_id,
                "response": "消息处理完成"
            }
            
        except Exception as e:
            app_logger.error(
                f"消息处理失败: {e}",
                extra={
                    'extra_fields': {
                        'message_data': message_data,
                        'operation': 'process_message',
                        'error_type': type(e).__name__
                    },
                    'request_id': getattr(request.state, 'request_id', None)
                },
                exc_info=True
            )
            
            raise HTTPException(status_code=500, detail="消息处理失败")


def register_exception_handlers(app: FastAPI):
    """注册异常处理器"""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理器"""
        
        app_logger.warning(
            f"HTTP异常: {exc.status_code} - {exc.detail}",
            extra={
                'extra_fields': {
                    'status_code': exc.status_code,
                    'detail': exc.detail,
                    'path': request.url.path,
                    'method': request.method
                },
                'request_id': getattr(request.state, 'request_id', None)
            }
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": True,
                "message": exc.detail,
                "status_code": exc.status_code
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理器"""
        
        app_logger.error(
            f"未处理的异常: {exc}",
            extra={
                'extra_fields': {
                    'exception_type': type(exc).__name__,
                    'path': request.url.path,
                    'method': request.method
                },
                'request_id': getattr(request.state, 'request_id', None)
            },
            exc_info=True
        )
        
        # 记录系统错误事件
        business_logger.log_simple_event(
            event_type=EventType.SYSTEM_ERROR,
            event_name="系统异常",
            request_id=getattr(request.state, 'request_id', None),
            business_data={
                'exception_type': type(exc).__name__,
                'path': request.url.path,
                'method': request.method
            },
            status=EventStatus.FAILURE
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "error": True,
                "message": "内部服务器错误",
                "status_code": 500
            }
        )


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    # 启动应用
    uvicorn.run(
        "integration_example:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_config=None  # 使用我们自己的日志配置
    )
