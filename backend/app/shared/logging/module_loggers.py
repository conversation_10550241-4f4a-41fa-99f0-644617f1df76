"""
柴管家项目业务模块日志接口
为各个业务模块提供统一的日志记录接口
"""

from typing import Dict, Any, Optional, Type
from abc import ABC, abstractmethod

from .logger import get_logger, LoggerAdapter
from .business_logger import BusinessLogger, EventType, EventStatus, get_business_logger


class ModuleLogger(ABC):
    """业务模块日志记录器基类"""
    
    def __init__(self, module_name: str):
        self.module_name = module_name
        self.logger = get_logger(f"module.{module_name}")
        self.business_logger = get_business_logger()
    
    @abstractmethod
    def get_event_types(self) -> Dict[str, EventType]:
        """获取模块相关的事件类型映射"""
        pass
    
    def log_operation(self, operation: str, status: str = "success", 
                     request_id: Optional[str] = None,
                     user_id: Optional[str] = None,
                     data: Optional[Dict[str, Any]] = None,
                     error: Optional[Exception] = None) -> None:
        """记录模块操作"""
        
        # 构建日志消息
        message = f"{self.module_name}.{operation}: {status}"
        
        # 构建额外字段
        extra_fields = {
            'module': self.module_name,
            'operation': operation,
            'status': status,
        }
        if data:
            extra_fields['data'] = data
        if error:
            extra_fields['error'] = {
                'type': type(error).__name__,
                'message': str(error)
            }
        
        # 选择日志级别
        if status == "success":
            level = 20  # INFO
        elif status == "failure":
            level = 40  # ERROR
        else:
            level = 30  # WARNING
        
        # 记录日志
        extra = {
            'extra_fields': extra_fields,
            'request_id': request_id,
            'user_id': user_id,
        }
        self.logger.log(level, message, extra=extra)


class UserManagementLogger(ModuleLogger):
    """用户管理模块日志记录器"""
    
    def __init__(self):
        super().__init__("user_management")
    
    def get_event_types(self) -> Dict[str, EventType]:
        return {
            'register': EventType.USER_REGISTER,
            'login': EventType.USER_LOGIN,
            'logout': EventType.USER_LOGOUT,
            'update': EventType.USER_UPDATE,
            'delete': EventType.USER_DELETE,
        }
    
    def log_user_register(self, user_id: str, email: str, 
                         request_id: Optional[str] = None,
                         success: bool = True,
                         error: Optional[Exception] = None) -> None:
        """记录用户注册事件"""
        
        business_data = {
            'user_id': user_id,
            'email': email,
        }
        
        if success:
            self.business_logger.log_simple_event(
                event_type=EventType.USER_REGISTER,
                event_name="用户注册成功",
                request_id=request_id,
                user_id=user_id,
                business_data=business_data,
                status=EventStatus.SUCCESS
            )
        else:
            error_info = None
            if error:
                error_info = {
                    'error_type': type(error).__name__,
                    'error_message': str(error)
                }
            
            self.business_logger.log_simple_event(
                event_type=EventType.USER_REGISTER,
                event_name="用户注册失败",
                request_id=request_id,
                business_data=business_data,
                status=EventStatus.FAILURE
            )
    
    def log_user_login(self, user_id: str, login_method: str,
                      request_id: Optional[str] = None,
                      success: bool = True,
                      error: Optional[Exception] = None) -> None:
        """记录用户登录事件"""
        
        business_data = {
            'user_id': user_id,
            'login_method': login_method,
        }
        
        status = EventStatus.SUCCESS if success else EventStatus.FAILURE
        event_name = "用户登录成功" if success else "用户登录失败"
        
        self.business_logger.log_simple_event(
            event_type=EventType.USER_LOGIN,
            event_name=event_name,
            request_id=request_id,
            user_id=user_id,
            business_data=business_data,
            status=status
        )


class ChannelManagementLogger(ModuleLogger):
    """渠道管理模块日志记录器"""
    
    def __init__(self):
        super().__init__("channel_management")
    
    def get_event_types(self) -> Dict[str, EventType]:
        return {
            'create': EventType.CHANNEL_CREATE,
            'update': EventType.CHANNEL_UPDATE,
            'delete': EventType.CHANNEL_DELETE,
            'connect': EventType.CHANNEL_CONNECT,
            'disconnect': EventType.CHANNEL_DISCONNECT,
        }
    
    def log_channel_operation(self, operation: str, channel_id: str, 
                             channel_type: str, user_id: str,
                             request_id: Optional[str] = None,
                             success: bool = True,
                             additional_data: Optional[Dict[str, Any]] = None) -> None:
        """记录渠道操作事件"""
        
        event_types = self.get_event_types()
        event_type = event_types.get(operation, EventType.SYSTEM_ERROR)
        
        business_data = {
            'channel_id': channel_id,
            'channel_type': channel_type,
            'operation': operation,
        }
        if additional_data:
            business_data.update(additional_data)
        
        status = EventStatus.SUCCESS if success else EventStatus.FAILURE
        event_name = f"渠道{operation}{'成功' if success else '失败'}"
        
        self.business_logger.log_simple_event(
            event_type=event_type,
            event_name=event_name,
            request_id=request_id,
            user_id=user_id,
            business_data=business_data,
            status=status
        )


class MessageProcessingLogger(ModuleLogger):
    """消息处理模块日志记录器"""
    
    def __init__(self):
        super().__init__("message_processing")
    
    def get_event_types(self) -> Dict[str, EventType]:
        return {
            'receive': EventType.MESSAGE_RECEIVE,
            'process': EventType.MESSAGE_PROCESS,
            'send': EventType.MESSAGE_SEND,
            'route': EventType.MESSAGE_ROUTE,
        }
    
    def log_message_flow(self, operation: str, message_id: str,
                        channel_id: str, user_id: Optional[str] = None,
                        request_id: Optional[str] = None,
                        message_type: Optional[str] = None,
                        processing_time: Optional[float] = None,
                        success: bool = True) -> None:
        """记录消息流转事件"""
        
        event_types = self.get_event_types()
        event_type = event_types.get(operation, EventType.SYSTEM_ERROR)
        
        business_data = {
            'message_id': message_id,
            'channel_id': channel_id,
            'operation': operation,
        }
        if message_type:
            business_data['message_type'] = message_type
        if processing_time:
            business_data['processing_time'] = processing_time
        
        status = EventStatus.SUCCESS if success else EventStatus.FAILURE
        event_name = f"消息{operation}{'成功' if success else '失败'}"
        
        self.business_logger.log_simple_event(
            event_type=event_type,
            event_name=event_name,
            request_id=request_id,
            user_id=user_id,
            business_data=business_data,
            status=status
        )


class AIServiceLogger(ModuleLogger):
    """AI服务模块日志记录器"""
    
    def __init__(self):
        super().__init__("ai_service")
    
    def get_event_types(self) -> Dict[str, EventType]:
        return {
            'request': EventType.AI_REQUEST,
            'response': EventType.AI_RESPONSE,
            'confidence_check': EventType.AI_CONFIDENCE_CHECK,
            'fallback': EventType.AI_FALLBACK,
        }
    
    def log_ai_interaction(self, operation: str, request_id: str,
                          user_id: Optional[str] = None,
                          model_name: Optional[str] = None,
                          confidence_score: Optional[float] = None,
                          response_time: Optional[float] = None,
                          success: bool = True,
                          error_details: Optional[Dict[str, Any]] = None) -> None:
        """记录AI交互事件"""
        
        event_types = self.get_event_types()
        event_type = event_types.get(operation, EventType.AI_REQUEST)
        
        business_data = {
            'operation': operation,
            'request_id': request_id,
        }
        if model_name:
            business_data['model_name'] = model_name
        if confidence_score is not None:
            business_data['confidence_score'] = confidence_score
        if response_time:
            business_data['response_time'] = response_time
        if error_details:
            business_data['error_details'] = error_details
        
        status = EventStatus.SUCCESS if success else EventStatus.FAILURE
        event_name = f"AI{operation}{'成功' if success else '失败'}"
        
        self.business_logger.log_simple_event(
            event_type=event_type,
            event_name=event_name,
            request_id=request_id,
            user_id=user_id,
            business_data=business_data,
            status=status
        )


# 模块日志记录器工厂
class ModuleLoggerFactory:
    """模块日志记录器工厂"""
    
    _loggers: Dict[str, ModuleLogger] = {}
    
    @classmethod
    def get_logger(cls, module_name: str) -> ModuleLogger:
        """获取指定模块的日志记录器"""
        
        if module_name in cls._loggers:
            return cls._loggers[module_name]
        
        # 根据模块名创建对应的日志记录器
        logger_classes = {
            'user_management': UserManagementLogger,
            'channel_management': ChannelManagementLogger,
            'message_processing': MessageProcessingLogger,
            'ai_service': AIServiceLogger,
        }
        
        logger_class = logger_classes.get(module_name)
        if logger_class:
            logger = logger_class()
        else:
            # 创建通用模块日志记录器
            class GenericModuleLogger(ModuleLogger):
                def get_event_types(self) -> Dict[str, EventType]:
                    return {}
            
            logger = GenericModuleLogger(module_name)
        
        cls._loggers[module_name] = logger
        return logger


# 便捷函数
def get_module_logger(module_name: str) -> ModuleLogger:
    """获取模块日志记录器"""
    return ModuleLoggerFactory.get_logger(module_name)
