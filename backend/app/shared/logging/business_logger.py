"""
柴管家项目业务事件日志模块
定义关键业务事件的日志记录标准和格式规范
"""

import time
import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from contextlib import contextmanager

from .logger import get_logger


class EventType(str, Enum):
    """业务事件类型枚举"""
    
    # 用户相关事件
    USER_REGISTER = "user.register"
    USER_LOGIN = "user.login"
    USER_LOGOUT = "user.logout"
    USER_UPDATE = "user.update"
    USER_DELETE = "user.delete"
    
    # 认证相关事件
    AUTH_TOKEN_GENERATE = "auth.token.generate"
    AUTH_TOKEN_REFRESH = "auth.token.refresh"
    AUTH_TOKEN_REVOKE = "auth.token.revoke"
    AUTH_PERMISSION_CHECK = "auth.permission.check"
    
    # 渠道管理事件
    CHANNEL_CREATE = "channel.create"
    CHANNEL_UPDATE = "channel.update"
    CHANNEL_DELETE = "channel.delete"
    CHANNEL_CONNECT = "channel.connect"
    CHANNEL_DISCONNECT = "channel.disconnect"
    
    # 消息处理事件
    MESSAGE_RECEIVE = "message.receive"
    MESSAGE_PROCESS = "message.process"
    MESSAGE_SEND = "message.send"
    MESSAGE_ROUTE = "message.route"
    
    # AI服务事件
    AI_REQUEST = "ai.request"
    AI_RESPONSE = "ai.response"
    AI_CONFIDENCE_CHECK = "ai.confidence.check"
    AI_FALLBACK = "ai.fallback"
    
    # 知识库事件
    KNOWLEDGE_SEARCH = "knowledge.search"
    KNOWLEDGE_CREATE = "knowledge.create"
    KNOWLEDGE_UPDATE = "knowledge.update"
    KNOWLEDGE_DELETE = "knowledge.delete"
    
    # 工作流事件
    WORKFLOW_START = "workflow.start"
    WORKFLOW_STEP = "workflow.step"
    WORKFLOW_COMPLETE = "workflow.complete"
    WORKFLOW_ERROR = "workflow.error"
    
    # 系统事件
    SYSTEM_STARTUP = "system.startup"
    SYSTEM_SHUTDOWN = "system.shutdown"
    SYSTEM_ERROR = "system.error"
    SYSTEM_HEALTH_CHECK = "system.health_check"


class EventStatus(str, Enum):
    """事件状态枚举"""
    SUCCESS = "success"
    FAILURE = "failure"
    PENDING = "pending"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


@dataclass
class BusinessEvent:
    """业务事件数据模型"""
    
    # 基础字段
    event_type: EventType
    event_name: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    
    # 上下文字段
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    trace_id: Optional[str] = None
    session_id: Optional[str] = None
    
    # 执行信息
    status: EventStatus = EventStatus.SUCCESS
    duration: Optional[float] = None
    start_time: Optional[float] = None
    
    # 业务数据
    business_data: Dict[str, Any] = field(default_factory=dict)
    
    # 错误信息
    error_info: Optional[Dict[str, Any]] = None
    
    # 额外元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'event_name': self.event_name,
            'timestamp': self.timestamp.isoformat() + 'Z',
            'request_id': self.request_id,
            'user_id': self.user_id,
            'trace_id': self.trace_id,
            'session_id': self.session_id,
            'status': self.status.value,
            'duration': self.duration,
            'business_data': self.business_data,
            'error_info': self.error_info,
            'metadata': self.metadata,
        }


class BusinessLogger:
    """业务事件日志记录器"""
    
    def __init__(self, logger_name: str = "business"):
        self.logger = get_logger(logger_name)
        self._active_events: Dict[str, BusinessEvent] = {}
    
    def log_event(self, event: BusinessEvent) -> None:
        """记录业务事件"""
        
        # 构建日志记录的额外字段
        extra = {
            'event_type': event.event_type.value,
            'event_name': event.event_name,
            'event_id': event.event_id,
            'user_id': event.user_id,
            'request_id': event.request_id,
            'trace_id': event.trace_id,
            'duration': event.duration,
            'status': event.status.value,
            'business_data': event.business_data,
            'error_info': event.error_info,
        }
        
        # 根据事件状态选择日志级别
        if event.status == EventStatus.SUCCESS:
            level = 20  # INFO
        elif event.status == EventStatus.FAILURE:
            level = 40  # ERROR
        elif event.status in [EventStatus.TIMEOUT, EventStatus.CANCELLED]:
            level = 30  # WARNING
        else:
            level = 20  # INFO
        
        # 构建日志消息
        message = f"{event.event_type.value}: {event.event_name}"
        if event.duration:
            message += f" (duration: {event.duration:.3f}s)"
        
        # 记录日志
        self.logger.log(level, message, extra=extra)
    
    def start_event(self, event_type: EventType, event_name: str, 
                   request_id: Optional[str] = None,
                   user_id: Optional[str] = None,
                   trace_id: Optional[str] = None,
                   business_data: Optional[Dict[str, Any]] = None) -> str:
        """开始记录一个事件（用于需要计算执行时间的事件）"""
        
        event = BusinessEvent(
            event_type=event_type,
            event_name=event_name,
            request_id=request_id,
            user_id=user_id,
            trace_id=trace_id,
            business_data=business_data or {},
            status=EventStatus.PENDING,
            start_time=time.time()
        )
        
        self._active_events[event.event_id] = event
        return event.event_id
    
    def end_event(self, event_id: str, status: EventStatus = EventStatus.SUCCESS,
                 error_info: Optional[Dict[str, Any]] = None,
                 additional_data: Optional[Dict[str, Any]] = None) -> None:
        """结束一个事件记录"""
        
        if event_id not in self._active_events:
            self.logger.warning(f"Event {event_id} not found in active events")
            return
        
        event = self._active_events.pop(event_id)
        
        # 计算执行时间
        if event.start_time:
            event.duration = time.time() - event.start_time
        
        # 更新事件状态
        event.status = status
        if error_info:
            event.error_info = error_info
        if additional_data:
            event.business_data.update(additional_data)
        
        # 记录事件
        self.log_event(event)
    
    @contextmanager
    def event_context(self, event_type: EventType, event_name: str,
                     request_id: Optional[str] = None,
                     user_id: Optional[str] = None,
                     trace_id: Optional[str] = None,
                     business_data: Optional[Dict[str, Any]] = None):
        """事件上下文管理器，自动处理事件的开始和结束"""
        
        event_id = self.start_event(
            event_type=event_type,
            event_name=event_name,
            request_id=request_id,
            user_id=user_id,
            trace_id=trace_id,
            business_data=business_data
        )
        
        try:
            yield event_id
            self.end_event(event_id, EventStatus.SUCCESS)
        except Exception as e:
            error_info = {
                'error_type': type(e).__name__,
                'error_message': str(e),
            }
            self.end_event(event_id, EventStatus.FAILURE, error_info)
            raise
    
    def log_simple_event(self, event_type: EventType, event_name: str,
                        request_id: Optional[str] = None,
                        user_id: Optional[str] = None,
                        trace_id: Optional[str] = None,
                        business_data: Optional[Dict[str, Any]] = None,
                        status: EventStatus = EventStatus.SUCCESS) -> None:
        """记录简单事件（不需要计算执行时间）"""
        
        event = BusinessEvent(
            event_type=event_type,
            event_name=event_name,
            request_id=request_id,
            user_id=user_id,
            trace_id=trace_id,
            business_data=business_data or {},
            status=status
        )
        
        self.log_event(event)


# 全局业务日志记录器实例
_business_logger: Optional[BusinessLogger] = None


def get_business_logger() -> BusinessLogger:
    """获取全局业务日志记录器"""
    global _business_logger
    if _business_logger is None:
        _business_logger = BusinessLogger()
    return _business_logger
