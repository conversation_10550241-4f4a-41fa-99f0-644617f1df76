"""
柴管家项目日志使用示例
演示各种日志功能的正确使用方法
"""

import time
import asyncio
from typing import Dict, Any, Optional

from .logger import get_logger, set_request_context, clear_request_context
from .business_logger import get_business_logger, EventType, EventStatus
from .module_loggers import get_module_logger


def basic_logging_example():
    """基础日志使用示例"""
    print("=== 基础日志使用示例 ===")
    
    # 获取日志器
    logger = get_logger("example.basic")
    
    # 记录不同级别的日志
    logger.debug("这是调试信息")
    logger.info("这是一般信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    logger.critical("这是严重错误")
    
    # 带额外字段的日志
    logger.info("用户操作", extra={
        'extra_fields': {
            'operation': 'login',
            'user_id': 'user-123',
            'ip_address': '*************'
        }
    })


def context_logging_example():
    """上下文日志使用示例"""
    print("=== 上下文日志使用示例 ===")
    
    logger = get_logger("example.context")
    
    # 设置请求上下文
    set_request_context(
        request_id="req-12345",
        user_id="user-67890",
        trace_id="trace-abcdef"
    )
    
    try:
        # 这些日志会自动包含上下文信息
        logger.info("开始处理用户请求")
        
        # 模拟一些操作
        time.sleep(0.1)
        
        logger.info("用户请求处理完成")
        
    finally:
        # 清除上下文
        clear_request_context()


def business_event_example():
    """业务事件日志示例"""
    print("=== 业务事件日志示例 ===")
    
    business_logger = get_business_logger()
    
    # 简单业务事件
    business_logger.log_simple_event(
        event_type=EventType.USER_LOGIN,
        event_name="用户登录成功",
        request_id="req-12345",
        user_id="user-67890",
        business_data={
            "login_method": "email",
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0..."
        },
        status=EventStatus.SUCCESS
    )
    
    # 使用上下文管理器的复杂事件
    with business_logger.event_context(
        event_type=EventType.MESSAGE_PROCESS,
        event_name="处理用户消息",
        request_id="req-12345",
        user_id="user-67890",
        business_data={"message_type": "text", "channel": "wechat"}
    ) as event_id:
        # 模拟消息处理
        print(f"处理事件ID: {event_id}")
        time.sleep(0.2)  # 模拟处理时间
        
        # 如果发生异常，会自动记录为失败事件
        # raise Exception("处理失败")


def module_logger_example():
    """模块日志器使用示例"""
    print("=== 模块日志器使用示例 ===")
    
    # 用户管理模块日志
    user_logger = get_module_logger("user_management")
    
    user_logger.log_user_register(
        user_id="user-12345",
        email="<EMAIL>",
        request_id="req-67890",
        success=True
    )
    
    user_logger.log_user_login(
        user_id="user-12345",
        login_method="email",
        request_id="req-67890",
        success=True
    )
    
    # 消息处理模块日志
    msg_logger = get_module_logger("message_processing")
    
    msg_logger.log_message_flow(
        operation="receive",
        message_id="msg-12345",
        channel_id="channel-67890",
        user_id="user-12345",
        request_id="req-67890",
        message_type="text",
        processing_time=0.15,
        success=True
    )
    
    # AI服务模块日志
    ai_logger = get_module_logger("ai_service")
    
    ai_logger.log_ai_interaction(
        operation="request",
        request_id="req-67890",
        user_id="user-12345",
        model_name="tongyi-qwen",
        confidence_score=0.85,
        response_time=1.2,
        success=True
    )


def error_handling_example():
    """错误处理日志示例"""
    print("=== 错误处理日志示例 ===")
    
    logger = get_logger("example.error")
    business_logger = get_business_logger()
    
    try:
        # 模拟一个可能失败的操作
        result = risky_operation()
        logger.info("操作成功完成", extra={
            'extra_fields': {'result': result}
        })
        
    except ValueError as e:
        # 记录业务逻辑错误
        logger.warning(f"业务逻辑错误: {e}", extra={
            'extra_fields': {
                'error_type': 'business_logic',
                'operation': 'risky_operation'
            }
        })
        
        # 记录业务事件
        business_logger.log_simple_event(
            event_type=EventType.SYSTEM_ERROR,
            event_name="业务逻辑错误",
            business_data={
                'error_type': 'ValueError',
                'error_message': str(e)
            },
            status=EventStatus.FAILURE
        )
        
    except Exception as e:
        # 记录系统错误
        logger.error(f"系统错误: {e}", exc_info=True, extra={
            'extra_fields': {
                'error_type': 'system_error',
                'operation': 'risky_operation'
            }
        })
        
        # 记录业务事件
        business_logger.log_simple_event(
            event_type=EventType.SYSTEM_ERROR,
            event_name="系统错误",
            business_data={
                'error_type': type(e).__name__,
                'error_message': str(e)
            },
            status=EventStatus.FAILURE
        )


def performance_monitoring_example():
    """性能监控日志示例"""
    print("=== 性能监控日志示例 ===")
    
    logger = get_logger("example.performance")
    business_logger = get_business_logger()
    
    # 手动计时
    start_time = time.time()
    
    # 模拟一个耗时操作
    time.sleep(0.3)
    
    duration = time.time() - start_time
    
    logger.info(f"操作完成，耗时: {duration:.3f}秒", extra={
        'extra_fields': {
            'operation': 'slow_operation',
            'duration': duration,
            'performance_category': 'slow' if duration > 0.2 else 'normal'
        }
    })
    
    # 使用业务日志记录性能事件
    business_logger.log_simple_event(
        event_type=EventType.SYSTEM_HEALTH_CHECK,
        event_name="性能监控",
        business_data={
            'operation': 'slow_operation',
            'duration': duration,
            'threshold_exceeded': duration > 0.2
        },
        status=EventStatus.SUCCESS
    )


async def async_logging_example():
    """异步日志使用示例"""
    print("=== 异步日志使用示例 ===")
    
    logger = get_logger("example.async")
    business_logger = get_business_logger()
    
    # 设置异步上下文
    set_request_context(
        request_id="async-req-123",
        trace_id="async-trace-456"
    )
    
    try:
        logger.info("开始异步操作")
        
        # 模拟异步操作
        await asyncio.sleep(0.1)
        
        # 使用异步上下文管理器
        with business_logger.event_context(
            event_type=EventType.AI_REQUEST,
            event_name="异步AI调用",
            request_id="async-req-123"
        ) as event_id:
            await asyncio.sleep(0.2)  # 模拟AI调用
            logger.info(f"AI调用完成，事件ID: {event_id}")
        
        logger.info("异步操作完成")
        
    finally:
        clear_request_context()


def risky_operation() -> str:
    """模拟一个可能失败的操作"""
    import random
    
    if random.random() < 0.3:
        raise ValueError("业务逻辑验证失败")
    elif random.random() < 0.1:
        raise RuntimeError("系统运行时错误")
    
    return "操作成功"


def run_all_examples():
    """运行所有示例"""
    print("柴管家日志系统使用示例")
    print("=" * 50)
    
    # 初始化日志系统
    from .logger import setup_logging
    setup_logging()
    
    # 运行各种示例
    basic_logging_example()
    print()
    
    context_logging_example()
    print()
    
    business_event_example()
    print()
    
    module_logger_example()
    print()
    
    error_handling_example()
    print()
    
    performance_monitoring_example()
    print()
    
    # 运行异步示例
    print("=== 异步日志使用示例 ===")
    asyncio.run(async_logging_example())
    print()
    
    print("所有示例运行完成！")
    print("请查看 logs/ 目录下的日志文件：")
    print("- app.log: 应用日志")
    print("- business.log: 业务事件日志")


if __name__ == "__main__":
    run_all_examples()
