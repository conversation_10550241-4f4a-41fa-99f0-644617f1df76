"""
柴管家项目日志器管理
提供统一的日志器创建和配置功能
"""

import logging
import sys
from typing import Optional, Dict, Any

from .config import LogConfig, get_current_config
from .handlers import setup_handlers, get_context_filter


# 全局日志器缓存
_loggers: Dict[str, logging.Logger] = {}


def get_logger(name: str, config: Optional[LogConfig] = None) -> logging.Logger:
    """获取指定名称的日志器"""

    # 如果日志器已存在，直接返回
    if name in _loggers:
        return _loggers[name]

    if config is None:
        config = get_current_config()

    # 创建日志器
    logger = logging.getLogger(name)
    level_value = config.level.value if hasattr(config.level, 'value') else config.level
    logger.setLevel(level_value)

    # 防止日志重复（禁用传播到父日志器）
    logger.propagate = False

    # 设置处理器
    setup_handlers(logger, config)

    # 缓存日志器
    _loggers[name] = logger

    return logger


def setup_logging(config: Optional[LogConfig] = None) -> None:
    """设置全局日志配置"""

    if config is None:
        config = get_current_config()

    # 设置根日志器级别
    root_logger = logging.getLogger()
    level_value = config.level.value if hasattr(config.level, 'value') else config.level
    root_logger.setLevel(level_value)

    # 清除根日志器的默认处理器
    root_logger.handlers.clear()

    # 禁用一些第三方库的详细日志
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.error").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)
    logging.getLogger("alembic").setLevel(logging.INFO)

    # 在测试环境中禁用一些日志
    env_value = config.environment.value if hasattr(config.environment, 'value') else config.environment
    if env_value == "testing":
        logging.getLogger("asyncio").setLevel(logging.WARNING)
        logging.getLogger("concurrent.futures").setLevel(logging.WARNING)


def set_request_context(request_id: Optional[str] = None,
                       user_id: Optional[str] = None,
                       trace_id: Optional[str] = None) -> None:
    """设置请求上下文信息"""
    context_filter = get_context_filter()
    context_filter.set_context(request_id, user_id, trace_id)


def clear_request_context() -> None:
    """清除请求上下文信息"""
    context_filter = get_context_filter()
    context_filter.clear_context()


def log_with_context(logger: logging.Logger, level: int, message: str,
                    extra_fields: Optional[Dict[str, Any]] = None,
                    request_id: Optional[str] = None,
                    user_id: Optional[str] = None,
                    trace_id: Optional[str] = None) -> None:
    """带上下文信息的日志记录"""

    # 构建额外字段
    extra = {}
    if extra_fields:
        extra['extra_fields'] = extra_fields
    if request_id:
        extra['request_id'] = request_id
    if user_id:
        extra['user_id'] = user_id
    if trace_id:
        extra['trace_id'] = trace_id

    # 记录日志
    logger.log(level, message, extra=extra)


class LoggerAdapter(logging.LoggerAdapter):
    """日志器适配器，用于自动添加上下文信息"""

    def __init__(self, logger: logging.Logger, extra: Optional[Dict[str, Any]] = None):
        super().__init__(logger, extra or {})

    def process(self, msg: str, kwargs: Dict[str, Any]) -> tuple:
        """处理日志消息，添加上下文信息"""

        # 获取当前上下文
        context_filter = get_context_filter()

        # 合并上下文信息
        extra = kwargs.get('extra', {})
        if context_filter.request_id:
            extra['request_id'] = context_filter.request_id
        if context_filter.user_id:
            extra['user_id'] = context_filter.user_id
        if context_filter.trace_id:
            extra['trace_id'] = context_filter.trace_id

        # 添加适配器的额外信息
        extra.update(self.extra)

        kwargs['extra'] = extra
        return msg, kwargs


def get_logger_adapter(name: str, extra: Optional[Dict[str, Any]] = None) -> LoggerAdapter:
    """获取日志器适配器"""
    logger = get_logger(name)
    return LoggerAdapter(logger, extra)


# 便捷函数
def debug(message: str, **kwargs) -> None:
    """记录DEBUG级别日志"""
    logger = get_logger(__name__)
    log_with_context(logger, logging.DEBUG, message, **kwargs)


def info(message: str, **kwargs) -> None:
    """记录INFO级别日志"""
    logger = get_logger(__name__)
    log_with_context(logger, logging.INFO, message, **kwargs)


def warning(message: str, **kwargs) -> None:
    """记录WARNING级别日志"""
    logger = get_logger(__name__)
    log_with_context(logger, logging.WARNING, message, **kwargs)


def error(message: str, **kwargs) -> None:
    """记录ERROR级别日志"""
    logger = get_logger(__name__)
    log_with_context(logger, logging.ERROR, message, **kwargs)


def critical(message: str, **kwargs) -> None:
    """记录CRITICAL级别日志"""
    logger = get_logger(__name__)
    log_with_context(logger, logging.CRITICAL, message, **kwargs)
