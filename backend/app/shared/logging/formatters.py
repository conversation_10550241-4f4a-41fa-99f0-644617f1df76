"""
柴管家项目日志格式化器
提供JSON和结构化日志格式
"""

import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
from pythonjsonlogger import jsonlogger


class JSONFormatter(jsonlogger.JsonFormatter):
    """JSON格式化器，用于生产环境的结构化日志"""
    
    def __init__(self, *args, **kwargs):
        # 定义JSON日志的标准字段
        format_str = (
            "%(timestamp)s %(level)s %(logger)s %(module)s %(funcName)s "
            "%(lineno)d %(message)s %(request_id)s %(user_id)s %(trace_id)s"
        )
        super().__init__(format_str, *args, **kwargs)
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]) -> None:
        """添加自定义字段到日志记录"""
        super().add_fields(log_record, record, message_dict)
        
        # 添加时间戳
        log_record['timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        # 添加日志级别
        log_record['level'] = record.levelname
        
        # 添加日志器名称
        log_record['logger'] = record.name
        
        # 添加模块信息
        log_record['module'] = record.module
        log_record['funcName'] = record.funcName
        log_record['lineno'] = record.lineno
        
        # 添加进程和线程信息
        log_record['process_id'] = record.process
        log_record['thread_id'] = record.thread
        
        # 添加请求相关信息（如果存在）
        log_record['request_id'] = getattr(record, 'request_id', None)
        log_record['user_id'] = getattr(record, 'user_id', None)
        log_record['trace_id'] = getattr(record, 'trace_id', None)
        
        # 添加异常信息（如果存在）
        if record.exc_info:
            log_record['exception'] = {
                'type': record.exc_info[0].__name__ if record.exc_info[0] else None,
                'message': str(record.exc_info[1]) if record.exc_info[1] else None,
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # 添加额外的上下文信息
        extra_fields = getattr(record, 'extra_fields', {})
        if extra_fields:
            log_record.update(extra_fields)


class StructuredFormatter(logging.Formatter):
    """结构化格式化器，用于开发环境的可读日志"""
    
    def __init__(self):
        # 定义可读的日志格式
        format_str = (
            "%(asctime)s | %(levelname)-8s | %(name)-20s | "
            "%(module)s:%(funcName)s:%(lineno)d | %(message)s"
        )
        super().__init__(format_str, datefmt='%Y-%m-%d %H:%M:%S')
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 基础格式化
        formatted = super().format(record)
        
        # 添加请求信息（如果存在）
        request_info = []
        if hasattr(record, 'request_id') and record.request_id:
            request_info.append(f"req_id={record.request_id}")
        if hasattr(record, 'user_id') and record.user_id:
            request_info.append(f"user_id={record.user_id}")
        if hasattr(record, 'trace_id') and record.trace_id:
            request_info.append(f"trace_id={record.trace_id}")
        
        if request_info:
            formatted += f" | {' '.join(request_info)}"
        
        # 添加额外字段（如果存在）
        if hasattr(record, 'extra_fields') and record.extra_fields:
            extra_str = " | ".join([f"{k}={v}" for k, v in record.extra_fields.items()])
            formatted += f" | {extra_str}"
        
        return formatted


class BusinessEventFormatter(jsonlogger.JsonFormatter):
    """业务事件专用格式化器"""
    
    def __init__(self, *args, **kwargs):
        format_str = (
            "%(timestamp)s %(event_type)s %(event_name)s %(user_id)s "
            "%(request_id)s %(duration)s %(status)s %(message)s"
        )
        super().__init__(format_str, *args, **kwargs)
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]) -> None:
        """添加业务事件特定字段"""
        super().add_fields(log_record, record, message_dict)
        
        # 添加时间戳
        log_record['timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        # 添加业务事件特定字段
        log_record['event_type'] = getattr(record, 'event_type', 'unknown')
        log_record['event_name'] = getattr(record, 'event_name', 'unknown')
        log_record['user_id'] = getattr(record, 'user_id', None)
        log_record['request_id'] = getattr(record, 'request_id', None)
        log_record['duration'] = getattr(record, 'duration', None)
        log_record['status'] = getattr(record, 'status', 'unknown')
        
        # 添加业务数据
        business_data = getattr(record, 'business_data', {})
        if business_data:
            log_record['business_data'] = business_data
        
        # 添加错误信息（如果存在）
        error_info = getattr(record, 'error_info', {})
        if error_info:
            log_record['error_info'] = error_info


def get_formatter(format_type: str) -> logging.Formatter:
    """根据格式类型获取对应的格式化器"""
    
    formatters = {
        'json': JSONFormatter,
        'structured': StructuredFormatter,
        'business': BusinessEventFormatter,
    }
    
    formatter_class = formatters.get(format_type, JSONFormatter)
    return formatter_class()
