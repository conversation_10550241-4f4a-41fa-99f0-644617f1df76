"""
柴管家项目日志处理器
提供文件和控制台日志处理器
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Optional

from .config import LogConfig, get_current_config
from .formatters import get_formatter


class AsyncFileHandler(logging.handlers.RotatingFileHandler):
    """异步文件处理器，避免阻塞主线程"""

    def __init__(self, filename: str, mode: str = 'a', maxBytes: int = 0,
                 backupCount: int = 0, encoding: Optional[str] = None, delay: bool = False):
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay)
        self.setLevel(logging.DEBUG)


def get_file_handler(
    log_file: str,
    config: Optional[LogConfig] = None,
    format_type: str = "json"
) -> logging.Handler:
    """创建文件日志处理器"""

    if config is None:
        config = get_current_config()

    # 构建完整的日志文件路径
    log_path = config.log_dir / log_file

    # 确保日志目录存在
    log_path.parent.mkdir(parents=True, exist_ok=True)

    # 创建轮转文件处理器
    if config.async_logging:
        handler = AsyncFileHandler(
            filename=str(log_path),
            maxBytes=config.max_file_size,
            backupCount=config.backup_count,
            encoding='utf-8'
        )
    else:
        handler = logging.handlers.RotatingFileHandler(
            filename=str(log_path),
            maxBytes=config.max_file_size,
            backupCount=config.backup_count,
            encoding='utf-8'
        )

    # 设置格式化器
    formatter = get_formatter(format_type)
    handler.setFormatter(formatter)

    # 设置日志级别
    level_value = config.level.value if hasattr(config.level, 'value') else config.level
    handler.setLevel(level_value)

    return handler


def get_console_handler(
    config: Optional[LogConfig] = None,
    format_type: str = "structured"
) -> logging.Handler:
    """创建控制台日志处理器"""

    if config is None:
        config = get_current_config()

    # 创建控制台处理器
    handler = logging.StreamHandler()

    # 设置格式化器
    formatter = get_formatter(format_type)
    handler.setFormatter(formatter)

    # 设置日志级别
    console_level_value = config.console_level.value if hasattr(config.console_level, 'value') else config.console_level
    handler.setLevel(console_level_value)

    return handler


def get_business_handler(
    config: Optional[LogConfig] = None
) -> logging.Handler:
    """创建业务日志处理器"""

    if config is None:
        config = get_current_config()

    return get_file_handler(
        log_file=config.business_log_file,
        config=config,
        format_type="business"
    )


class ContextFilter(logging.Filter):
    """上下文过滤器，用于添加请求上下文信息"""

    def __init__(self, name: str = ""):
        super().__init__(name)
        self.request_id = None
        self.user_id = None
        self.trace_id = None

    def filter(self, record: logging.LogRecord) -> bool:
        """为日志记录添加上下文信息"""

        # 添加请求ID（如果存在）
        if not hasattr(record, 'request_id'):
            record.request_id = self.request_id

        # 添加用户ID（如果存在）
        if not hasattr(record, 'user_id'):
            record.user_id = self.user_id

        # 添加追踪ID（如果存在）
        if not hasattr(record, 'trace_id'):
            record.trace_id = self.trace_id

        return True

    def set_context(self, request_id: Optional[str] = None,
                   user_id: Optional[str] = None,
                   trace_id: Optional[str] = None) -> None:
        """设置上下文信息"""
        if request_id is not None:
            self.request_id = request_id
        if user_id is not None:
            self.user_id = user_id
        if trace_id is not None:
            self.trace_id = trace_id

    def clear_context(self) -> None:
        """清除上下文信息"""
        self.request_id = None
        self.user_id = None
        self.trace_id = None


# 全局上下文过滤器实例
_context_filter = ContextFilter()


def get_context_filter() -> ContextFilter:
    """获取全局上下文过滤器"""
    return _context_filter


def setup_handlers(logger: logging.Logger, config: Optional[LogConfig] = None) -> None:
    """为日志器设置处理器"""

    if config is None:
        config = get_current_config()

    # 清除现有处理器
    logger.handlers.clear()

    # 添加文件处理器
    file_handler = get_file_handler(config.log_file, config, config.format_type)
    file_handler.addFilter(_context_filter)
    logger.addHandler(file_handler)

    # 添加控制台处理器（如果启用）
    if config.console_enabled:
        console_handler = get_console_handler(config, "structured")
        console_handler.addFilter(_context_filter)
        logger.addHandler(console_handler)

    # 添加业务日志处理器（如果启用）
    if config.business_log_enabled:
        business_handler = get_business_handler(config)
        business_handler.addFilter(_context_filter)
        # 业务日志处理器只处理业务相关的日志
        business_handler.addFilter(lambda record: hasattr(record, 'event_type'))
        logger.addHandler(business_handler)
