"""数据库基础配置和模型基类."""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict
from sqlalchemy import Column, DateTime, String, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.orm import as_declarative


@as_declarative()
class Base:
    """数据库模型基类."""
    
    # 自动生成表名
    @declared_attr
    def __tablename__(cls) -> str:
        """根据类名自动生成表名."""
        # 将驼峰命名转换为下划线命名
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()
    
    # 通用字段
    id = Column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4,
        comment="主键ID"
    )
    
    created_at = Column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
        comment="更新时间"
    )
    
    is_deleted = Column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="软删除标记"
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """将模型转换为字典."""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            elif isinstance(value, uuid.UUID):
                value = str(value)
            result[column.name] = value
        return result
    
    def __repr__(self) -> str:
        """模型的字符串表示."""
        return f"<{self.__class__.__name__}(id={self.id})>"


# 创建基类实例
Base = declarative_base(cls=Base)
