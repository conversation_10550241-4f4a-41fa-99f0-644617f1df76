"""
柴管家项目错误处理器
提供全局错误处理和异常捕获功能
"""

import sys
import asyncio
import functools
from typing import Any, Callable, Optional, Dict, Type
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

from ..logging import get_logger, set_request_context
from .error_monitor import get_error_monitor, ErrorInfo, ErrorCategory, ErrorSeverity


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.logger = get_logger("error_handler")
        self.error_monitor = get_error_monitor()
        
        # 错误响应模板
        self.error_templates = {
            ErrorCategory.VALIDATION_ERROR: {
                "status_code": 400,
                "message": "请求参数验证失败",
                "user_message": "请检查输入的参数是否正确"
            },
            ErrorCategory.AUTHENTICATION_ERROR: {
                "status_code": 401,
                "message": "身份认证失败",
                "user_message": "请重新登录"
            },
            ErrorCategory.AUTHORIZATION_ERROR: {
                "status_code": 403,
                "message": "权限不足",
                "user_message": "您没有权限执行此操作"
            },
            ErrorCategory.NOT_FOUND_ERROR: {
                "status_code": 404,
                "message": "资源不存在",
                "user_message": "请求的资源不存在"
            },
            ErrorCategory.CONFLICT_ERROR: {
                "status_code": 409,
                "message": "资源冲突",
                "user_message": "操作冲突，请稍后重试"
            },
            ErrorCategory.BUSINESS_LOGIC_ERROR: {
                "status_code": 422,
                "message": "业务逻辑错误",
                "user_message": "操作无法完成，请检查业务规则"
            },
            ErrorCategory.TIMEOUT_ERROR: {
                "status_code": 504,
                "message": "请求超时",
                "user_message": "请求处理超时，请稍后重试"
            },
            ErrorCategory.EXTERNAL_API_ERROR: {
                "status_code": 502,
                "message": "外部服务错误",
                "user_message": "外部服务暂时不可用，请稍后重试"
            },
            ErrorCategory.SYSTEM_ERROR: {
                "status_code": 500,
                "message": "系统内部错误",
                "user_message": "系统出现问题，我们正在处理"
            },
        }
    
    def handle_exception(self, 
                        exception: Exception,
                        request: Optional[Request] = None,
                        user_id: Optional[str] = None,
                        business_context: Optional[Dict[str, Any]] = None) -> ErrorInfo:
        """处理异常"""
        
        # 获取请求上下文
        request_id = None
        trace_id = None
        if request:
            request_id = getattr(request.state, 'request_id', None)
            trace_id = getattr(request.state, 'trace_id', None)
            if not user_id:
                user_id = getattr(request.state, 'user_id', None)
        
        # 捕获异常
        error_info = self.error_monitor.capture_exception(
            exception=exception,
            request_id=request_id,
            user_id=user_id,
            trace_id=trace_id,
            business_context=business_context
        )
        
        return error_info
    
    def create_error_response(self, error_info: ErrorInfo, include_details: bool = False) -> JSONResponse:
        """创建错误响应"""
        
        # 获取错误模板
        template = self.error_templates.get(
            error_info.category,
            self.error_templates[ErrorCategory.SYSTEM_ERROR]
        )
        
        # 构建响应数据
        response_data = {
            "success": False,
            "error": {
                "code": error_info.category.value,
                "message": template["message"],
                "user_message": template["user_message"],
                "error_id": error_info.error_id,
                "timestamp": error_info.timestamp.isoformat(),
            }
        }
        
        # 在开发环境中包含详细信息
        if include_details:
            response_data["error"]["details"] = {
                "error_type": error_info.error_type,
                "error_message": error_info.error_message,
                "module": error_info.module,
                "function": error_info.function,
                "line_number": error_info.line_number,
            }
        
        return JSONResponse(
            status_code=template["status_code"],
            content=response_data,
            headers={
                "X-Error-ID": error_info.error_id,
                "X-Request-ID": error_info.request_id or "",
            }
        )


def exception_handler(
    include_details: bool = False,
    business_context: Optional[Dict[str, Any]] = None
):
    """异常处理装饰器"""
    
    def decorator(func: Callable) -> Callable:
        error_handler = ErrorHandler()
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    # 尝试从参数中获取request对象
                    request = None
                    for arg in args:
                        if isinstance(arg, Request):
                            request = arg
                            break
                    
                    error_info = error_handler.handle_exception(
                        exception=e,
                        request=request,
                        business_context=business_context
                    )
                    
                    # 如果是HTTP异常，直接抛出
                    if isinstance(e, (HTTPException, StarletteHTTPException)):
                        raise
                    
                    # 创建错误响应
                    return error_handler.create_error_response(error_info, include_details)
            
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # 尝试从参数中获取request对象
                    request = None
                    for arg in args:
                        if isinstance(arg, Request):
                            request = arg
                            break
                    
                    error_info = error_handler.handle_exception(
                        exception=e,
                        request=request,
                        business_context=business_context
                    )
                    
                    # 如果是HTTP异常，直接抛出
                    if isinstance(e, (HTTPException, StarletteHTTPException)):
                        raise
                    
                    # 创建错误响应
                    return error_handler.create_error_response(error_info, include_details)
            
            return sync_wrapper
    
    return decorator


def setup_error_handlers(app: FastAPI, include_details: bool = False) -> None:
    """设置全局错误处理器"""
    
    error_handler = ErrorHandler()
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理器"""
        
        # 记录HTTP异常
        error_info = error_handler.handle_exception(
            exception=exc,
            request=request,
            business_context={
                "status_code": exc.status_code,
                "detail": exc.detail,
                "path": request.url.path,
                "method": request.method,
            }
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "code": f"http_{exc.status_code}",
                    "message": exc.detail,
                    "user_message": exc.detail,
                    "error_id": error_info.error_id,
                    "timestamp": error_info.timestamp.isoformat(),
                }
            },
            headers={
                "X-Error-ID": error_info.error_id,
                "X-Request-ID": getattr(request.state, 'request_id', ''),
            }
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def starlette_exception_handler(request: Request, exc: StarletteHTTPException):
        """Starlette HTTP异常处理器"""
        
        # 记录HTTP异常
        error_info = error_handler.handle_exception(
            exception=exc,
            request=request,
            business_context={
                "status_code": exc.status_code,
                "detail": exc.detail,
                "path": request.url.path,
                "method": request.method,
            }
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "code": f"http_{exc.status_code}",
                    "message": exc.detail,
                    "user_message": exc.detail,
                    "error_id": error_info.error_id,
                    "timestamp": error_info.timestamp.isoformat(),
                }
            },
            headers={
                "X-Error-ID": error_info.error_id,
                "X-Request-ID": getattr(request.state, 'request_id', ''),
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理器"""
        
        # 记录异常
        error_info = error_handler.handle_exception(
            exception=exc,
            request=request,
            business_context={
                "path": request.url.path,
                "method": request.method,
                "query_params": dict(request.query_params),
            }
        )
        
        # 创建错误响应
        return error_handler.create_error_response(error_info, include_details)


def setup_global_exception_handler() -> None:
    """设置全局异常处理器（用于非FastAPI环境）"""
    
    error_handler = ErrorHandler()
    
    def handle_exception(exc_type: Type[BaseException], 
                        exc_value: BaseException, 
                        exc_traceback) -> None:
        """全局异常处理函数"""
        
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许键盘中断
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录异常
        error_info = error_handler.handle_exception(
            exception=exc_value,
            business_context={
                "context": "global_exception_handler",
                "exc_type": exc_type.__name__,
            }
        )
        
        # 输出错误信息
        print(f"未处理的异常 [错误ID: {error_info.error_id}]: {exc_value}")
    
    # 设置全局异常处理器
    sys.excepthook = handle_exception
