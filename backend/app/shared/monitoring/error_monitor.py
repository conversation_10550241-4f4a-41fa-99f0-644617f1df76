"""
柴管家项目错误监控模块
实现应用内置的错误捕获和分类机制
"""

import sys
import traceback
import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, Any, Optional, List, Type, Callable
from dataclasses import dataclass, field

from ..logging import get_logger, get_business_logger, EventType, EventStatus


class ErrorCategory(str, Enum):
    """错误分类枚举"""
    
    # 系统错误
    SYSTEM_ERROR = "system_error"
    DATABASE_ERROR = "database_error"
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    
    # 业务错误
    BUSINESS_LOGIC_ERROR = "business_logic_error"
    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "authentication_error"
    AUTHORIZATION_ERROR = "authorization_error"
    
    # 外部服务错误
    EXTERNAL_API_ERROR = "external_api_error"
    AI_SERVICE_ERROR = "ai_service_error"
    THIRD_PARTY_ERROR = "third_party_error"
    
    # 用户错误
    USER_INPUT_ERROR = "user_input_error"
    NOT_FOUND_ERROR = "not_found_error"
    CONFLICT_ERROR = "conflict_error"
    
    # 未知错误
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(str, Enum):
    """错误严重程度枚举"""
    
    LOW = "low"           # 低：不影响核心功能，可延后处理
    MEDIUM = "medium"     # 中：影响部分功能，需要关注
    HIGH = "high"         # 高：影响核心功能，需要尽快处理
    CRITICAL = "critical" # 严重：系统无法正常运行，需要立即处理


@dataclass
class ErrorInfo:
    """错误信息数据模型"""
    
    # 基础信息
    error_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # 错误分类
    category: ErrorCategory = ErrorCategory.UNKNOWN_ERROR
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
    
    # 错误详情
    error_type: str = ""
    error_message: str = ""
    error_code: Optional[str] = None
    
    # 上下文信息
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    trace_id: Optional[str] = None
    
    # 技术信息
    module: Optional[str] = None
    function: Optional[str] = None
    line_number: Optional[int] = None
    stack_trace: Optional[str] = None
    
    # 环境信息
    environment: Optional[str] = None
    version: Optional[str] = None
    
    # 业务信息
    business_context: Dict[str, Any] = field(default_factory=dict)
    
    # 处理信息
    is_handled: bool = False
    resolution_status: Optional[str] = None
    resolution_notes: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_id': self.error_id,
            'timestamp': self.timestamp.isoformat(),
            'category': self.category.value,
            'severity': self.severity.value,
            'error_type': self.error_type,
            'error_message': self.error_message,
            'error_code': self.error_code,
            'request_id': self.request_id,
            'user_id': self.user_id,
            'trace_id': self.trace_id,
            'module': self.module,
            'function': self.function,
            'line_number': self.line_number,
            'stack_trace': self.stack_trace,
            'environment': self.environment,
            'version': self.version,
            'business_context': self.business_context,
            'is_handled': self.is_handled,
            'resolution_status': self.resolution_status,
            'resolution_notes': self.resolution_notes,
        }


class ErrorClassifier:
    """错误分类器"""
    
    def __init__(self):
        # 错误类型到分类的映射
        self.error_mappings = {
            # 系统错误
            'SystemError': ErrorCategory.SYSTEM_ERROR,
            'MemoryError': ErrorCategory.SYSTEM_ERROR,
            'OSError': ErrorCategory.SYSTEM_ERROR,
            'IOError': ErrorCategory.SYSTEM_ERROR,
            
            # 数据库错误
            'DatabaseError': ErrorCategory.DATABASE_ERROR,
            'IntegrityError': ErrorCategory.DATABASE_ERROR,
            'OperationalError': ErrorCategory.DATABASE_ERROR,
            'ProgrammingError': ErrorCategory.DATABASE_ERROR,
            
            # 网络错误
            'ConnectionError': ErrorCategory.NETWORK_ERROR,
            'HTTPError': ErrorCategory.NETWORK_ERROR,
            'RequestException': ErrorCategory.NETWORK_ERROR,
            'URLError': ErrorCategory.NETWORK_ERROR,
            
            # 超时错误
            'TimeoutError': ErrorCategory.TIMEOUT_ERROR,
            'ConnectTimeout': ErrorCategory.TIMEOUT_ERROR,
            'ReadTimeout': ErrorCategory.TIMEOUT_ERROR,
            
            # 验证错误
            'ValidationError': ErrorCategory.VALIDATION_ERROR,
            'ValueError': ErrorCategory.VALIDATION_ERROR,
            'TypeError': ErrorCategory.VALIDATION_ERROR,
            
            # 认证授权错误
            'AuthenticationError': ErrorCategory.AUTHENTICATION_ERROR,
            'PermissionError': ErrorCategory.AUTHORIZATION_ERROR,
            'Forbidden': ErrorCategory.AUTHORIZATION_ERROR,
            'Unauthorized': ErrorCategory.AUTHENTICATION_ERROR,
            
            # 用户错误
            'NotFoundError': ErrorCategory.NOT_FOUND_ERROR,
            'FileNotFoundError': ErrorCategory.NOT_FOUND_ERROR,
            'KeyError': ErrorCategory.NOT_FOUND_ERROR,
            'AttributeError': ErrorCategory.NOT_FOUND_ERROR,
            
            # 冲突错误
            'ConflictError': ErrorCategory.CONFLICT_ERROR,
            'IntegrityError': ErrorCategory.CONFLICT_ERROR,
        }
        
        # 严重程度映射
        self.severity_mappings = {
            ErrorCategory.SYSTEM_ERROR: ErrorSeverity.CRITICAL,
            ErrorCategory.DATABASE_ERROR: ErrorSeverity.HIGH,
            ErrorCategory.NETWORK_ERROR: ErrorSeverity.MEDIUM,
            ErrorCategory.TIMEOUT_ERROR: ErrorSeverity.MEDIUM,
            ErrorCategory.BUSINESS_LOGIC_ERROR: ErrorSeverity.MEDIUM,
            ErrorCategory.VALIDATION_ERROR: ErrorSeverity.LOW,
            ErrorCategory.AUTHENTICATION_ERROR: ErrorSeverity.HIGH,
            ErrorCategory.AUTHORIZATION_ERROR: ErrorSeverity.HIGH,
            ErrorCategory.EXTERNAL_API_ERROR: ErrorSeverity.MEDIUM,
            ErrorCategory.AI_SERVICE_ERROR: ErrorSeverity.MEDIUM,
            ErrorCategory.USER_INPUT_ERROR: ErrorSeverity.LOW,
            ErrorCategory.NOT_FOUND_ERROR: ErrorSeverity.LOW,
            ErrorCategory.CONFLICT_ERROR: ErrorSeverity.MEDIUM,
            ErrorCategory.UNKNOWN_ERROR: ErrorSeverity.MEDIUM,
        }
    
    def classify_error(self, exception: Exception) -> tuple[ErrorCategory, ErrorSeverity]:
        """分类错误"""
        error_type = type(exception).__name__
        
        # 查找错误分类
        category = self.error_mappings.get(error_type, ErrorCategory.UNKNOWN_ERROR)
        
        # 特殊规则处理
        if 'timeout' in str(exception).lower():
            category = ErrorCategory.TIMEOUT_ERROR
        elif 'connection' in str(exception).lower():
            category = ErrorCategory.NETWORK_ERROR
        elif 'permission' in str(exception).lower():
            category = ErrorCategory.AUTHORIZATION_ERROR
        elif 'auth' in str(exception).lower():
            category = ErrorCategory.AUTHENTICATION_ERROR
        
        # 确定严重程度
        severity = self.severity_mappings.get(category, ErrorSeverity.MEDIUM)
        
        return category, severity


class ErrorMonitor:
    """错误监控器"""
    
    def __init__(self):
        self.logger = get_logger("error_monitor")
        self.business_logger = get_business_logger()
        self.classifier = ErrorClassifier()
        self.error_handlers: List[Callable[[ErrorInfo], None]] = []
        
        # 错误统计
        self.error_counts: Dict[str, int] = {}
        self.recent_errors: List[ErrorInfo] = []
        self.max_recent_errors = 100
    
    def add_error_handler(self, handler: Callable[[ErrorInfo], None]) -> None:
        """添加错误处理器"""
        self.error_handlers.append(handler)
    
    def capture_exception(self, 
                         exception: Exception,
                         request_id: Optional[str] = None,
                         user_id: Optional[str] = None,
                         trace_id: Optional[str] = None,
                         business_context: Optional[Dict[str, Any]] = None) -> ErrorInfo:
        """捕获异常并生成错误信息"""
        
        # 分类错误
        category, severity = self.classifier.classify_error(exception)
        
        # 获取堆栈信息
        tb = traceback.extract_tb(exception.__traceback__)
        stack_trace = ''.join(traceback.format_exception(type(exception), exception, exception.__traceback__))
        
        # 获取最后一个堆栈帧的信息
        if tb:
            last_frame = tb[-1]
            module = last_frame.filename
            function = last_frame.name
            line_number = last_frame.lineno
        else:
            module = None
            function = None
            line_number = None
        
        # 创建错误信息
        error_info = ErrorInfo(
            category=category,
            severity=severity,
            error_type=type(exception).__name__,
            error_message=str(exception),
            request_id=request_id,
            user_id=user_id,
            trace_id=trace_id,
            module=module,
            function=function,
            line_number=line_number,
            stack_trace=stack_trace,
            business_context=business_context or {},
        )
        
        # 记录错误
        self._record_error(error_info)
        
        # 调用错误处理器
        for handler in self.error_handlers:
            try:
                handler(error_info)
            except Exception as e:
                self.logger.error(f"错误处理器执行失败: {e}")
        
        return error_info
    
    def _record_error(self, error_info: ErrorInfo) -> None:
        """记录错误信息"""
        
        # 更新错误统计
        error_key = f"{error_info.category.value}:{error_info.error_type}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # 添加到最近错误列表
        self.recent_errors.append(error_info)
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors.pop(0)
        
        # 记录应用日志
        log_level = self._get_log_level(error_info.severity)
        self.logger.log(
            log_level,
            f"错误监控: {error_info.error_type} - {error_info.error_message}",
            extra={
                'extra_fields': error_info.to_dict(),
                'request_id': error_info.request_id,
                'user_id': error_info.user_id,
                'trace_id': error_info.trace_id,
            }
        )
        
        # 记录业务事件
        self.business_logger.log_simple_event(
            event_type=EventType.SYSTEM_ERROR,
            event_name=f"错误监控: {error_info.category.value}",
            request_id=error_info.request_id,
            user_id=error_info.user_id,
            trace_id=error_info.trace_id,
            business_data={
                'error_id': error_info.error_id,
                'category': error_info.category.value,
                'severity': error_info.severity.value,
                'error_type': error_info.error_type,
                'error_message': error_info.error_message,
                'module': error_info.module,
                'function': error_info.function,
            },
            status=EventStatus.FAILURE
        )
    
    def _get_log_level(self, severity: ErrorSeverity) -> int:
        """根据严重程度获取日志级别"""
        mapping = {
            ErrorSeverity.LOW: 30,      # WARNING
            ErrorSeverity.MEDIUM: 40,   # ERROR
            ErrorSeverity.HIGH: 40,     # ERROR
            ErrorSeverity.CRITICAL: 50, # CRITICAL
        }
        return mapping.get(severity, 40)
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            'total_errors': sum(self.error_counts.values()),
            'error_counts_by_type': dict(self.error_counts),
            'recent_errors_count': len(self.recent_errors),
            'recent_errors': [error.to_dict() for error in self.recent_errors[-10:]],  # 最近10个错误
        }
    
    def clear_statistics(self) -> None:
        """清除统计信息"""
        self.error_counts.clear()
        self.recent_errors.clear()


# 全局错误监控器实例
_error_monitor: Optional[ErrorMonitor] = None


def get_error_monitor() -> ErrorMonitor:
    """获取全局错误监控器"""
    global _error_monitor
    if _error_monitor is None:
        _error_monitor = ErrorMonitor()
    return _error_monitor
