"""健康检查模块 - 检查各种服务的连接状态."""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import asyncpg
import redis.asyncio as redis
import aio_pika
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

logger = logging.getLogger(__name__)


class HealthChecker:
    """健康检查器类，负责检查各种服务的健康状态."""

    def __init__(self):
        """初始化健康检查器."""
        self.checks = {
            "database": self._check_database,
            "redis": self._check_redis,
            "rabbitmq": self._check_rabbitmq,
        }

    async def check_all(self, timeout: float = 5.0) -> Dict[str, Any]:
        """检查所有服务的健康状态.

        Args:
            timeout: 检查超时时间（秒）

        Returns:
            包含所有服务健康状态的字典
        """
        results = {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "version": "0.1.0",
            "checks": {}
        }

        # 并发执行所有健康检查
        tasks = []
        for service_name, check_func in self.checks.items():
            task = asyncio.create_task(
                self._run_check_with_timeout(service_name, check_func, timeout)
            )
            tasks.append(task)

        # 等待所有检查完成
        check_results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理检查结果
        overall_healthy = True
        for i, (service_name, _) in enumerate(self.checks.items()):
            result = check_results[i]
            if isinstance(result, Exception):
                results["checks"][service_name] = {
                    "status": "unhealthy",
                    "error": str(result),
                    "response_time": None
                }
                overall_healthy = False
            else:
                results["checks"][service_name] = result
                if result["status"] != "healthy":
                    overall_healthy = False

        # 设置整体状态
        results["status"] = "healthy" if overall_healthy else "unhealthy"

        return results

    async def _run_check_with_timeout(
        self,
        service_name: str,
        check_func,
        timeout: float
    ) -> Dict[str, Any]:
        """在超时限制内运行健康检查.

        Args:
            service_name: 服务名称
            check_func: 检查函数
            timeout: 超时时间

        Returns:
            检查结果字典
        """
        start_time = datetime.now()
        try:
            result = await asyncio.wait_for(check_func(), timeout=timeout)
            response_time = (datetime.now() - start_time).total_seconds() * 1000
            result["response_time"] = f"{response_time:.2f}ms"
            return result
        except asyncio.TimeoutError:
            return {
                "status": "unhealthy",
                "error": f"Health check timeout after {timeout}s",
                "response_time": None
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "response_time": None
            }

    async def _check_database(self) -> Dict[str, Any]:
        """检查PostgreSQL数据库连接."""
        try:
            # 从环境变量获取数据库URL
            import os
            database_url = os.getenv(
                "DATABASE_URL",
                "postgresql+asyncpg://chaiguanjia:chaiguanjia123@postgres:5432/chaiguanjia"
            )

            # 创建异步引擎
            engine = create_async_engine(database_url, echo=False)

            # 测试连接
            async with engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                result.fetchone()

            await engine.dispose()

            return {
                "status": "healthy",
                "message": "Database connection successful"
            }

        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": f"Database connection failed: {str(e)}"
            }

    async def _check_redis(self) -> Dict[str, Any]:
        """检查Redis缓存连接."""
        try:
            # 从环境变量获取Redis URL
            import os
            redis_url = os.getenv(
                "REDIS_URL",
                "redis://:redis123@redis:6379/0"
            )

            # 创建Redis连接
            redis_client = redis.from_url(redis_url)

            # 测试连接
            await redis_client.ping()
            await redis_client.close()

            return {
                "status": "healthy",
                "message": "Redis connection successful"
            }

        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": f"Redis connection failed: {str(e)}"
            }

    async def _check_rabbitmq(self) -> Dict[str, Any]:
        """检查RabbitMQ消息队列连接."""
        try:
            # 从环境变量获取RabbitMQ URL
            import os
            broker_url = os.getenv(
                "CELERY_BROKER_URL",
                "amqp://chaiguanjia:rabbitmq123@rabbitmq:5672/chaiguanjia"
            )

            # 创建连接
            connection = await aio_pika.connect_robust(broker_url)

            # 测试连接
            channel = await connection.channel()
            await channel.close()
            await connection.close()

            return {
                "status": "healthy",
                "message": "RabbitMQ connection successful"
            }

        except Exception as e:
            logger.error(f"RabbitMQ health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": f"RabbitMQ connection failed: {str(e)}"
            }


# 全局健康检查器实例
health_checker = HealthChecker()


async def get_health_status(timeout: float = 5.0) -> Dict[str, Any]:
    """获取系统健康状态.

    Args:
        timeout: 检查超时时间

    Returns:
        系统健康状态字典
    """
    return await health_checker.check_all(timeout)


async def is_healthy(timeout: float = 5.0) -> bool:
    """检查系统是否健康.

    Args:
        timeout: 检查超时时间

    Returns:
        系统是否健康
    """
    status = await get_health_status(timeout)
    return status["status"] == "healthy"
