"""
柴管家项目错误处理工作流
建立错误处理和修复跟踪流程
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

from ..logging import get_logger, get_business_logger, EventType, EventStatus
from .error_monitor import ErrorInfo, ErrorCategory, ErrorSeverity
from .error_tracker import get_error_tracker


class ErrorPriority(str, Enum):
    """错误处理优先级"""
    
    IMMEDIATE = "immediate"    # 立即处理（系统无法运行）
    URGENT = "urgent"         # 紧急处理（核心功能受影响）
    HIGH = "high"             # 高优先级（重要功能受影响）
    MEDIUM = "medium"         # 中优先级（部分功能受影响）
    LOW = "low"               # 低优先级（可延后处理）


class ErrorStatus(str, Enum):
    """错误处理状态"""
    
    NEW = "new"                    # 新错误
    ACKNOWLEDGED = "acknowledged"   # 已确认
    INVESTIGATING = "investigating" # 调查中
    IN_PROGRESS = "in_progress"    # 处理中
    RESOLVED = "resolved"          # 已解决
    CLOSED = "closed"              # 已关闭
    REOPENED = "reopened"          # 重新打开


class ResolutionType(str, Enum):
    """解决方案类型"""
    
    FIXED = "fixed"                # 已修复
    WORKAROUND = "workaround"      # 临时解决方案
    DUPLICATE = "duplicate"        # 重复错误
    NOT_REPRODUCIBLE = "not_reproducible"  # 无法重现
    BY_DESIGN = "by_design"        # 按设计工作
    WONT_FIX = "wont_fix"         # 不修复


@dataclass
class ErrorWorkflowRule:
    """错误工作流规则"""
    
    category: ErrorCategory
    severity: ErrorSeverity
    priority: ErrorPriority
    auto_assign: bool = False
    assignee: Optional[str] = None
    escalation_hours: Optional[int] = None
    notification_channels: List[str] = None
    
    def __post_init__(self):
        if self.notification_channels is None:
            self.notification_channels = []


class ErrorWorkflow:
    """错误处理工作流管理器"""
    
    def __init__(self):
        self.logger = get_logger("error_workflow")
        self.business_logger = get_business_logger()
        self.error_tracker = get_error_tracker()
        
        # 工作流规则
        self.workflow_rules = self._init_workflow_rules()
        
        # 状态转换规则
        self.status_transitions = self._init_status_transitions()
        
        # 通知处理器
        self.notification_handlers: Dict[str, Callable] = {}
    
    def _init_workflow_rules(self) -> Dict[tuple, ErrorWorkflowRule]:
        """初始化工作流规则"""
        rules = {}
        
        # 严重错误规则
        rules[(ErrorCategory.SYSTEM_ERROR, ErrorSeverity.CRITICAL)] = ErrorWorkflowRule(
            category=ErrorCategory.SYSTEM_ERROR,
            severity=ErrorSeverity.CRITICAL,
            priority=ErrorPriority.IMMEDIATE,
            auto_assign=True,
            assignee="system_admin",
            escalation_hours=1,
            notification_channels=["email", "sms", "slack"]
        )
        
        rules[(ErrorCategory.DATABASE_ERROR, ErrorSeverity.CRITICAL)] = ErrorWorkflowRule(
            category=ErrorCategory.DATABASE_ERROR,
            severity=ErrorSeverity.CRITICAL,
            priority=ErrorPriority.IMMEDIATE,
            auto_assign=True,
            assignee="database_admin",
            escalation_hours=2,
            notification_channels=["email", "slack"]
        )
        
        # 高优先级错误规则
        rules[(ErrorCategory.AUTHENTICATION_ERROR, ErrorSeverity.HIGH)] = ErrorWorkflowRule(
            category=ErrorCategory.AUTHENTICATION_ERROR,
            severity=ErrorSeverity.HIGH,
            priority=ErrorPriority.URGENT,
            auto_assign=True,
            assignee="security_team",
            escalation_hours=4,
            notification_channels=["email", "slack"]
        )
        
        rules[(ErrorCategory.EXTERNAL_API_ERROR, ErrorSeverity.HIGH)] = ErrorWorkflowRule(
            category=ErrorCategory.EXTERNAL_API_ERROR,
            severity=ErrorSeverity.HIGH,
            priority=ErrorPriority.HIGH,
            auto_assign=False,
            escalation_hours=8,
            notification_channels=["email"]
        )
        
        # 中等优先级错误规则
        rules[(ErrorCategory.BUSINESS_LOGIC_ERROR, ErrorSeverity.MEDIUM)] = ErrorWorkflowRule(
            category=ErrorCategory.BUSINESS_LOGIC_ERROR,
            severity=ErrorSeverity.MEDIUM,
            priority=ErrorPriority.MEDIUM,
            auto_assign=False,
            escalation_hours=24,
            notification_channels=["email"]
        )
        
        # 低优先级错误规则
        rules[(ErrorCategory.VALIDATION_ERROR, ErrorSeverity.LOW)] = ErrorWorkflowRule(
            category=ErrorCategory.VALIDATION_ERROR,
            severity=ErrorSeverity.LOW,
            priority=ErrorPriority.LOW,
            auto_assign=False,
            escalation_hours=72,
            notification_channels=[]
        )
        
        return rules
    
    def _init_status_transitions(self) -> Dict[ErrorStatus, List[ErrorStatus]]:
        """初始化状态转换规则"""
        return {
            ErrorStatus.NEW: [ErrorStatus.ACKNOWLEDGED, ErrorStatus.INVESTIGATING, ErrorStatus.CLOSED],
            ErrorStatus.ACKNOWLEDGED: [ErrorStatus.INVESTIGATING, ErrorStatus.IN_PROGRESS, ErrorStatus.CLOSED],
            ErrorStatus.INVESTIGATING: [ErrorStatus.IN_PROGRESS, ErrorStatus.RESOLVED, ErrorStatus.CLOSED],
            ErrorStatus.IN_PROGRESS: [ErrorStatus.RESOLVED, ErrorStatus.INVESTIGATING, ErrorStatus.CLOSED],
            ErrorStatus.RESOLVED: [ErrorStatus.CLOSED, ErrorStatus.REOPENED],
            ErrorStatus.CLOSED: [ErrorStatus.REOPENED],
            ErrorStatus.REOPENED: [ErrorStatus.INVESTIGATING, ErrorStatus.IN_PROGRESS, ErrorStatus.RESOLVED],
        }
    
    def process_error(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理新错误"""
        
        # 获取工作流规则
        rule = self._get_workflow_rule(error_info)
        
        # 创建工作流记录
        workflow_data = {
            'error_id': error_info.error_id,
            'priority': rule.priority.value,
            'status': ErrorStatus.NEW.value,
            'assignee': rule.assignee if rule.auto_assign else None,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'escalation_deadline': self._calculate_escalation_deadline(rule),
            'workflow_rule': {
                'category': rule.category.value,
                'severity': rule.severity.value,
                'auto_assign': rule.auto_assign,
                'escalation_hours': rule.escalation_hours,
                'notification_channels': rule.notification_channels,
            }
        }
        
        # 记录错误到跟踪器
        self.error_tracker.record_error(error_info)
        
        # 发送通知
        if rule.notification_channels:
            self._send_notifications(error_info, rule, workflow_data)
        
        # 记录工作流事件
        self.business_logger.log_simple_event(
            event_type=EventType.WORKFLOW_START,
            event_name="错误处理工作流启动",
            request_id=error_info.request_id,
            user_id=error_info.user_id,
            trace_id=error_info.trace_id,
            business_data={
                'error_id': error_info.error_id,
                'priority': rule.priority.value,
                'category': error_info.category.value,
                'severity': error_info.severity.value,
                'assignee': rule.assignee,
            },
            status=EventStatus.SUCCESS
        )
        
        self.logger.info(
            f"错误处理工作流启动: {error_info.error_id}",
            extra={
                'extra_fields': workflow_data,
                'request_id': error_info.request_id,
                'user_id': error_info.user_id,
                'trace_id': error_info.trace_id,
            }
        )
        
        return workflow_data
    
    def update_error_status(self, 
                           error_id: str,
                           new_status: ErrorStatus,
                           assignee: Optional[str] = None,
                           resolution_type: Optional[ResolutionType] = None,
                           resolution_notes: Optional[str] = None,
                           updated_by: Optional[str] = None) -> bool:
        """更新错误状态"""
        
        # 获取当前错误记录
        error_record = self.error_tracker.get_error_by_id(error_id)
        if not error_record:
            self.logger.error(f"错误记录不存在: {error_id}")
            return False
        
        # 检查状态转换是否有效
        current_status = ErrorStatus(error_record.resolution_status or ErrorStatus.NEW.value)
        if not self._is_valid_transition(current_status, new_status):
            self.logger.warning(f"无效的状态转换: {current_status} -> {new_status}")
            return False
        
        # 更新错误状态
        is_handled = new_status in [ErrorStatus.RESOLVED, ErrorStatus.CLOSED]
        resolution_status = new_status.value
        
        if resolution_type:
            resolution_notes = f"[{resolution_type.value}] {resolution_notes or ''}"
        
        success = self.error_tracker.update_error_status(
            error_id=error_id,
            is_handled=is_handled,
            resolution_status=resolution_status,
            resolution_notes=resolution_notes
        )
        
        if success:
            # 记录状态变更事件
            self.business_logger.log_simple_event(
                event_type=EventType.WORKFLOW_STEP,
                event_name=f"错误状态更新: {new_status.value}",
                business_data={
                    'error_id': error_id,
                    'old_status': current_status.value,
                    'new_status': new_status.value,
                    'assignee': assignee,
                    'resolution_type': resolution_type.value if resolution_type else None,
                    'updated_by': updated_by,
                },
                status=EventStatus.SUCCESS
            )
            
            self.logger.info(
                f"错误状态已更新: {error_id} -> {new_status.value}",
                extra={
                    'extra_fields': {
                        'error_id': error_id,
                        'old_status': current_status.value,
                        'new_status': new_status.value,
                        'assignee': assignee,
                        'updated_by': updated_by,
                    }
                }
            )
        
        return success
    
    def get_pending_errors(self, 
                          priority: Optional[ErrorPriority] = None,
                          assignee: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取待处理错误"""
        
        # 获取未处理的错误
        errors = self.error_tracker.search_errors(
            is_handled=False,
            limit=1000
        )
        
        pending_errors = []
        for error in errors:
            # 获取工作流规则
            error_info = error.to_error_info()
            rule = self._get_workflow_rule(error_info)
            
            # 过滤条件
            if priority and rule.priority != priority:
                continue
            if assignee and rule.assignee != assignee:
                continue
            
            # 计算是否超期
            escalation_deadline = self._calculate_escalation_deadline(rule, error.created_at)
            is_overdue = datetime.now(timezone.utc) > escalation_deadline if escalation_deadline else False
            
            pending_errors.append({
                'error_id': error.error_id,
                'timestamp': error.timestamp.isoformat(),
                'category': error.category,
                'severity': error.severity,
                'error_type': error.error_type,
                'error_message': error.error_message,
                'priority': rule.priority.value,
                'assignee': rule.assignee,
                'escalation_deadline': escalation_deadline.isoformat() if escalation_deadline else None,
                'is_overdue': is_overdue,
                'age_hours': (datetime.now(timezone.utc) - error.created_at).total_seconds() / 3600,
            })
        
        # 按优先级和时间排序
        priority_order = {
            ErrorPriority.IMMEDIATE: 0,
            ErrorPriority.URGENT: 1,
            ErrorPriority.HIGH: 2,
            ErrorPriority.MEDIUM: 3,
            ErrorPriority.LOW: 4,
        }
        
        pending_errors.sort(key=lambda x: (
            priority_order.get(ErrorPriority(x['priority']), 5),
            x['timestamp']
        ))
        
        return pending_errors
    
    def _get_workflow_rule(self, error_info: ErrorInfo) -> ErrorWorkflowRule:
        """获取工作流规则"""
        rule_key = (error_info.category, error_info.severity)
        
        # 查找精确匹配的规则
        if rule_key in self.workflow_rules:
            return self.workflow_rules[rule_key]
        
        # 查找分类匹配的规则
        for (category, severity), rule in self.workflow_rules.items():
            if category == error_info.category:
                return rule
        
        # 默认规则
        return ErrorWorkflowRule(
            category=error_info.category,
            severity=error_info.severity,
            priority=ErrorPriority.MEDIUM,
            auto_assign=False,
            escalation_hours=24,
            notification_channels=[]
        )
    
    def _calculate_escalation_deadline(self, 
                                     rule: ErrorWorkflowRule,
                                     start_time: Optional[datetime] = None) -> Optional[datetime]:
        """计算升级截止时间"""
        if not rule.escalation_hours:
            return None
        
        if not start_time:
            start_time = datetime.now(timezone.utc)
        
        from datetime import timedelta
        return start_time + timedelta(hours=rule.escalation_hours)
    
    def _is_valid_transition(self, current_status: ErrorStatus, new_status: ErrorStatus) -> bool:
        """检查状态转换是否有效"""
        valid_transitions = self.status_transitions.get(current_status, [])
        return new_status in valid_transitions
    
    def _send_notifications(self, 
                           error_info: ErrorInfo,
                           rule: ErrorWorkflowRule,
                           workflow_data: Dict[str, Any]) -> None:
        """发送通知"""
        
        for channel in rule.notification_channels:
            handler = self.notification_handlers.get(channel)
            if handler:
                try:
                    handler(error_info, rule, workflow_data)
                except Exception as e:
                    self.logger.error(f"通知发送失败 [{channel}]: {e}")
            else:
                self.logger.warning(f"未找到通知处理器: {channel}")
    
    def add_notification_handler(self, channel: str, handler: Callable) -> None:
        """添加通知处理器"""
        self.notification_handlers[channel] = handler
    
    def get_workflow_statistics(self) -> Dict[str, Any]:
        """获取工作流统计信息"""
        
        # 获取所有错误
        all_errors = self.error_tracker.search_errors(limit=10000)
        
        # 统计各种状态
        status_counts = {}
        priority_counts = {}
        overdue_count = 0
        
        for error in all_errors:
            error_info = error.to_error_info()
            rule = self._get_workflow_rule(error_info)
            
            # 状态统计
            status = error.resolution_status or ErrorStatus.NEW.value
            status_counts[status] = status_counts.get(status, 0) + 1
            
            # 优先级统计
            priority = rule.priority.value
            priority_counts[priority] = priority_counts.get(priority, 0) + 1
            
            # 超期统计
            if not error.is_handled:
                escalation_deadline = self._calculate_escalation_deadline(rule, error.created_at)
                if escalation_deadline and datetime.now(timezone.utc) > escalation_deadline:
                    overdue_count += 1
        
        return {
            'total_errors': len(all_errors),
            'status_distribution': status_counts,
            'priority_distribution': priority_counts,
            'overdue_errors': overdue_count,
            'handled_errors': len([e for e in all_errors if e.is_handled]),
            'pending_errors': len([e for e in all_errors if not e.is_handled]),
        }


# 全局工作流管理器实例
_error_workflow: Optional[ErrorWorkflow] = None


def get_error_workflow() -> ErrorWorkflow:
    """获取全局错误工作流管理器"""
    global _error_workflow
    if _error_workflow is None:
        _error_workflow = ErrorWorkflow()
    return _error_workflow
