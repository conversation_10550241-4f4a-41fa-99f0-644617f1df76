"""
柴管家项目错误跟踪器
提供错误记录的持久化存储和查询功能
"""

import json
import sqlite3
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from contextlib import contextmanager

from ..logging import get_logger
from .error_monitor import ErrorInfo, ErrorCategory, ErrorSeverity


@dataclass
class ErrorRecord:
    """错误记录数据模型"""
    
    id: Optional[int] = None
    error_id: str = ""
    timestamp: datetime = None
    category: str = ""
    severity: str = ""
    error_type: str = ""
    error_message: str = ""
    error_code: Optional[str] = None
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    trace_id: Optional[str] = None
    module: Optional[str] = None
    function: Optional[str] = None
    line_number: Optional[int] = None
    stack_trace: Optional[str] = None
    environment: Optional[str] = None
    version: Optional[str] = None
    business_context: str = ""  # JSON字符串
    is_handled: bool = False
    resolution_status: Optional[str] = None
    resolution_notes: Optional[str] = None
    created_at: datetime = None
    updated_at: datetime = None
    
    @classmethod
    def from_error_info(cls, error_info: ErrorInfo) -> 'ErrorRecord':
        """从ErrorInfo创建ErrorRecord"""
        return cls(
            error_id=error_info.error_id,
            timestamp=error_info.timestamp,
            category=error_info.category.value,
            severity=error_info.severity.value,
            error_type=error_info.error_type,
            error_message=error_info.error_message,
            error_code=error_info.error_code,
            request_id=error_info.request_id,
            user_id=error_info.user_id,
            trace_id=error_info.trace_id,
            module=error_info.module,
            function=error_info.function,
            line_number=error_info.line_number,
            stack_trace=error_info.stack_trace,
            environment=error_info.environment,
            version=error_info.version,
            business_context=json.dumps(error_info.business_context),
            is_handled=error_info.is_handled,
            resolution_status=error_info.resolution_status,
            resolution_notes=error_info.resolution_notes,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
    
    def to_error_info(self) -> ErrorInfo:
        """转换为ErrorInfo"""
        return ErrorInfo(
            error_id=self.error_id,
            timestamp=self.timestamp,
            category=ErrorCategory(self.category),
            severity=ErrorSeverity(self.severity),
            error_type=self.error_type,
            error_message=self.error_message,
            error_code=self.error_code,
            request_id=self.request_id,
            user_id=self.user_id,
            trace_id=self.trace_id,
            module=self.module,
            function=self.function,
            line_number=self.line_number,
            stack_trace=self.stack_trace,
            environment=self.environment,
            version=self.version,
            business_context=json.loads(self.business_context) if self.business_context else {},
            is_handled=self.is_handled,
            resolution_status=self.resolution_status,
            resolution_notes=self.resolution_notes,
        )


class ErrorTracker:
    """错误跟踪器"""
    
    def __init__(self, db_path: str = "logs/errors.db"):
        self.logger = get_logger("error_tracker")
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self) -> None:
        """初始化数据库"""
        with self._get_connection() as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS error_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    error_id TEXT UNIQUE NOT NULL,
                    timestamp TEXT NOT NULL,
                    category TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    error_type TEXT NOT NULL,
                    error_message TEXT NOT NULL,
                    error_code TEXT,
                    request_id TEXT,
                    user_id TEXT,
                    trace_id TEXT,
                    module TEXT,
                    function TEXT,
                    line_number INTEGER,
                    stack_trace TEXT,
                    environment TEXT,
                    version TEXT,
                    business_context TEXT,
                    is_handled BOOLEAN DEFAULT FALSE,
                    resolution_status TEXT,
                    resolution_notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_error_id ON error_records(error_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON error_records(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_category ON error_records(category)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_severity ON error_records(severity)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_request_id ON error_records(request_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_user_id ON error_records(user_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_is_handled ON error_records(is_handled)")
            
            conn.commit()
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def record_error(self, error_info: ErrorInfo) -> int:
        """记录错误"""
        record = ErrorRecord.from_error_info(error_info)
        
        with self._get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO error_records (
                    error_id, timestamp, category, severity, error_type, error_message,
                    error_code, request_id, user_id, trace_id, module, function,
                    line_number, stack_trace, environment, version, business_context,
                    is_handled, resolution_status, resolution_notes, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                record.error_id, record.timestamp.isoformat(), record.category, record.severity,
                record.error_type, record.error_message, record.error_code, record.request_id,
                record.user_id, record.trace_id, record.module, record.function,
                record.line_number, record.stack_trace, record.environment, record.version,
                record.business_context, record.is_handled, record.resolution_status,
                record.resolution_notes, record.created_at.isoformat(), record.updated_at.isoformat()
            ))
            conn.commit()
            return cursor.lastrowid
    
    def get_error_by_id(self, error_id: str) -> Optional[ErrorRecord]:
        """根据错误ID获取错误记录"""
        with self._get_connection() as conn:
            row = conn.execute(
                "SELECT * FROM error_records WHERE error_id = ?", (error_id,)
            ).fetchone()
            
            if row:
                return self._row_to_record(row)
            return None
    
    def search_errors(self,
                     category: Optional[str] = None,
                     severity: Optional[str] = None,
                     error_type: Optional[str] = None,
                     user_id: Optional[str] = None,
                     start_time: Optional[datetime] = None,
                     end_time: Optional[datetime] = None,
                     is_handled: Optional[bool] = None,
                     limit: int = 100,
                     offset: int = 0) -> List[ErrorRecord]:
        """搜索错误记录"""
        
        conditions = []
        params = []
        
        if category:
            conditions.append("category = ?")
            params.append(category)
        
        if severity:
            conditions.append("severity = ?")
            params.append(severity)
        
        if error_type:
            conditions.append("error_type = ?")
            params.append(error_type)
        
        if user_id:
            conditions.append("user_id = ?")
            params.append(user_id)
        
        if start_time:
            conditions.append("timestamp >= ?")
            params.append(start_time.isoformat())
        
        if end_time:
            conditions.append("timestamp <= ?")
            params.append(end_time.isoformat())
        
        if is_handled is not None:
            conditions.append("is_handled = ?")
            params.append(is_handled)
        
        where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
        
        query = f"""
            SELECT * FROM error_records
            {where_clause}
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        """
        params.extend([limit, offset])
        
        with self._get_connection() as conn:
            rows = conn.execute(query, params).fetchall()
            return [self._row_to_record(row) for row in rows]
    
    def update_error_status(self, error_id: str, 
                           is_handled: bool,
                           resolution_status: Optional[str] = None,
                           resolution_notes: Optional[str] = None) -> bool:
        """更新错误处理状态"""
        with self._get_connection() as conn:
            cursor = conn.execute("""
                UPDATE error_records 
                SET is_handled = ?, resolution_status = ?, resolution_notes = ?, updated_at = ?
                WHERE error_id = ?
            """, (is_handled, resolution_status, resolution_notes, 
                  datetime.now(timezone.utc).isoformat(), error_id))
            conn.commit()
            return cursor.rowcount > 0
    
    def get_error_statistics(self, 
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """获取错误统计信息"""
        
        if not start_time:
            start_time = datetime.now(timezone.utc) - timedelta(days=7)
        if not end_time:
            end_time = datetime.now(timezone.utc)
        
        with self._get_connection() as conn:
            # 总错误数
            total_errors = conn.execute("""
                SELECT COUNT(*) FROM error_records 
                WHERE timestamp BETWEEN ? AND ?
            """, (start_time.isoformat(), end_time.isoformat())).fetchone()[0]
            
            # 按分类统计
            category_stats = conn.execute("""
                SELECT category, COUNT(*) as count 
                FROM error_records 
                WHERE timestamp BETWEEN ? AND ?
                GROUP BY category
                ORDER BY count DESC
            """, (start_time.isoformat(), end_time.isoformat())).fetchall()
            
            # 按严重程度统计
            severity_stats = conn.execute("""
                SELECT severity, COUNT(*) as count 
                FROM error_records 
                WHERE timestamp BETWEEN ? AND ?
                GROUP BY severity
                ORDER BY count DESC
            """, (start_time.isoformat(), end_time.isoformat())).fetchall()
            
            # 按错误类型统计
            type_stats = conn.execute("""
                SELECT error_type, COUNT(*) as count 
                FROM error_records 
                WHERE timestamp BETWEEN ? AND ?
                GROUP BY error_type
                ORDER BY count DESC
                LIMIT 10
            """, (start_time.isoformat(), end_time.isoformat())).fetchall()
            
            # 处理状态统计
            handled_stats = conn.execute("""
                SELECT is_handled, COUNT(*) as count 
                FROM error_records 
                WHERE timestamp BETWEEN ? AND ?
                GROUP BY is_handled
            """, (start_time.isoformat(), end_time.isoformat())).fetchall()
            
            return {
                'total_errors': total_errors,
                'category_distribution': [{'category': row[0], 'count': row[1]} for row in category_stats],
                'severity_distribution': [{'severity': row[0], 'count': row[1]} for row in severity_stats],
                'top_error_types': [{'error_type': row[0], 'count': row[1]} for row in type_stats],
                'handling_status': [{'is_handled': bool(row[0]), 'count': row[1]} for row in handled_stats],
                'time_range': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat()
                }
            }
    
    def cleanup_old_errors(self, days: int = 30) -> int:
        """清理旧的错误记录"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)
        
        with self._get_connection() as conn:
            cursor = conn.execute("""
                DELETE FROM error_records 
                WHERE timestamp < ? AND is_handled = TRUE
            """, (cutoff_time.isoformat(),))
            conn.commit()
            
            deleted_count = cursor.rowcount
            self.logger.info(f"清理了 {deleted_count} 条旧的错误记录")
            return deleted_count
    
    def _row_to_record(self, row: sqlite3.Row) -> ErrorRecord:
        """将数据库行转换为ErrorRecord"""
        return ErrorRecord(
            id=row['id'],
            error_id=row['error_id'],
            timestamp=datetime.fromisoformat(row['timestamp']),
            category=row['category'],
            severity=row['severity'],
            error_type=row['error_type'],
            error_message=row['error_message'],
            error_code=row['error_code'],
            request_id=row['request_id'],
            user_id=row['user_id'],
            trace_id=row['trace_id'],
            module=row['module'],
            function=row['function'],
            line_number=row['line_number'],
            stack_trace=row['stack_trace'],
            environment=row['environment'],
            version=row['version'],
            business_context=row['business_context'],
            is_handled=bool(row['is_handled']),
            resolution_status=row['resolution_status'],
            resolution_notes=row['resolution_notes'],
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
        )


# 全局错误跟踪器实例
_error_tracker: Optional[ErrorTracker] = None


def get_error_tracker() -> ErrorTracker:
    """获取全局错误跟踪器"""
    global _error_tracker
    if _error_tracker is None:
        _error_tracker = ErrorTracker()
    return _error_tracker
