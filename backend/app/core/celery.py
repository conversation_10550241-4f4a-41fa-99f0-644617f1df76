"""Celery配置模块 - 异步任务处理配置."""

import os
from celery import Celery

# 创建Celery应用实例
celery_app = Celery("chaiguanjia")

# 从环境变量获取配置
broker_url = os.getenv(
    "CELERY_BROKER_URL",
    "amqp://chaiguanjia:rabbitmq123@rabbitmq:5672/chaiguanjia"
)

result_backend = os.getenv(
    "CELERY_RESULT_BACKEND",
    "redis://:redis123@redis:6379/1"
)

# 配置Celery
celery_app.conf.update(
    # 消息代理配置
    broker_url=broker_url,
    result_backend=result_backend,
    
    # 任务序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    
    # 任务路由
    task_routes={
        "app.modules.*.tasks.*": {"queue": "default"},
    },
    
    # 任务结果配置
    result_expires=3600,  # 1小时后过期
    
    # Worker配置
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    
    # 定时任务配置
    beat_schedule={
        # 示例定时任务
        "health-check": {
            "task": "app.core.tasks.health_check_task",
            "schedule": 300.0,  # 每5分钟执行一次
        },
    },
)

# 自动发现任务
celery_app.autodiscover_tasks([
    "app.core",
    "app.modules.user_management",
    "app.modules.message_processing",
    "app.modules.ai_services",
    "app.modules.channel_management",
    "app.modules.knowledge_management",
    "app.modules.workflow_engine",
])

# 导出celery应用实例
app = celery_app
