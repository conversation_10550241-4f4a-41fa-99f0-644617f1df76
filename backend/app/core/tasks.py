"""核心任务模块 - 定义系统级别的异步任务."""

import logging
from datetime import datetime, timezone
from celery import current_app as celery_app
from app.core.celery import celery_app as app

logger = logging.getLogger(__name__)


@app.task(bind=True)
def health_check_task(self):
    """健康检查任务 - 定期检查系统健康状态."""
    try:
        # 记录任务执行时间
        timestamp = datetime.now(timezone.utc).isoformat()
        
        # 执行基本的健康检查
        result = {
            "task_id": self.request.id,
            "timestamp": timestamp,
            "status": "healthy",
            "message": "System health check completed successfully",
            "worker_id": self.request.hostname,
        }
        
        logger.info(f"Health check task completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Health check task failed: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)


@app.task(bind=True)
def cleanup_task(self):
    """清理任务 - 定期清理过期数据."""
    try:
        timestamp = datetime.now(timezone.utc).isoformat()
        
        # 这里可以添加具体的清理逻辑
        # 例如：清理过期的会话、日志、临时文件等
        
        result = {
            "task_id": self.request.id,
            "timestamp": timestamp,
            "status": "completed",
            "message": "Cleanup task completed successfully",
            "items_cleaned": 0,  # 实际清理的项目数量
        }
        
        logger.info(f"Cleanup task completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Cleanup task failed: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=3)


@app.task
def test_task(message: str = "Hello from Celery!"):
    """测试任务 - 用于验证Celery是否正常工作."""
    timestamp = datetime.now(timezone.utc).isoformat()
    
    result = {
        "message": message,
        "timestamp": timestamp,
        "status": "success",
    }
    
    logger.info(f"Test task executed: {result}")
    return result
