"""柴管家 FastAPI 应用主入口文件."""

from typing import Any, Dict

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi.openapi.utils import get_openapi

# 创建 FastAPI 应用实例
app = FastAPI(
    title="柴管家 API",
    description="""
    ## 柴管家智能客服管理平台 API

    柴管家是一个现代化的智能客服管理平台，提供多渠道接入、AI智能辅助、实时监控等功能。

    ### 主要功能模块

    * **用户管理** - 用户注册、登录、权限管理
    * **渠道管理** - 多平台渠道接入和配置
    * **消息处理** - 实时消息收发和处理
    * **AI辅助** - 智能回复建议和情感分析
    * **统计分析** - 客服工作量和客户满意度统计

    ### 认证方式

    API 使用 JWT (JSON Web Token) 进行身份认证。获取令牌后，在请求头中添加：
    ```
    Authorization: Bearer <your_access_token>
    ```

    ### 响应格式

    所有 API 响应都遵循统一格式：
    ```json
    {
        "success": true,
        "data": {},
        "message": "操作成功",
        "timestamp": "2024-08-07T10:30:00Z"
    }
    ```

    ### 错误处理

    错误响应格式：
    ```json
    {
        "success": false,
        "error": {
            "code": "ERROR_CODE",
            "message": "错误描述",
            "details": []
        },
        "timestamp": "2024-08-07T10:30:00Z"
    }
    ```
    """,
    version="0.1.0",
    contact={
        "name": "柴管家开发团队",
        "url": "https://github.com/your-org/chaiguanjia",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def custom_openapi():
    """自定义 OpenAPI 配置."""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # 添加自定义标签
    openapi_schema["tags"] = [
        {
            "name": "认证",
            "description": "用户认证和授权相关接口",
        },
        {
            "name": "用户管理",
            "description": "用户信息管理接口",
        },
        {
            "name": "渠道管理",
            "description": "多渠道接入和配置接口",
        },
        {
            "name": "消息处理",
            "description": "消息收发和处理接口",
        },
        {
            "name": "统计分析",
            "description": "数据统计和分析接口",
        },
        {
            "name": "系统",
            "description": "系统状态和配置接口",
        },
    ]

    # 添加安全定义
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT 访问令牌认证",
        }
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


@app.get(
    "/",
    tags=["系统"],
    summary="API 根路径",
    description="返回 API 欢迎信息和基本状态",
    response_description="API 欢迎信息",
)
async def read_root() -> Dict[str, str]:
    """API 根路径处理函数.

    返回柴管家 API 的欢迎信息，用于验证 API 服务是否正常运行。

    Returns:
        Dict[str, str]: 包含欢迎消息的字典

    Example:
        ```json
        {
            "message": "欢迎使用柴管家 API",
            "version": "0.1.0",
            "docs": "/docs"
        }
        ```
    """
    return {
        "message": "欢迎使用柴管家 API",
        "version": "0.1.0",
        "docs": "/docs",
        "redoc": "/redoc",
    }


@app.get(
    "/health",
    tags=["系统"],
    summary="健康检查",
    description="检查 API 服务和相关依赖的健康状态",
    response_description="服务健康状态信息",
)
async def health_check() -> Dict[str, Any]:
    """健康检查端点.

    用于监控系统检查 API 服务是否正常运行，包括数据库连接、
    缓存服务等关键组件的状态。

    Returns:
        Dict[str, any]: 包含健康状态信息的字典

    Example:
        ```json
        {
            "status": "healthy",
            "timestamp": "2024-08-07T10:30:00Z",
            "version": "0.1.0",
            "checks": {
                "database": {
                    "status": "healthy",
                    "message": "Database connection successful",
                    "response_time": "15.23ms"
                },
                "redis": {
                    "status": "healthy",
                    "message": "Redis connection successful",
                    "response_time": "8.45ms"
                }
            }
        }
        ```
    """
    from app.shared.monitoring.health_check import get_health_status

    try:
        return await get_health_status(timeout=3.0)
    except Exception as e:
        from datetime import datetime, timezone
        return {
            "status": "unhealthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "version": "0.1.0",
            "error": f"Health check failed: {str(e)}",
            "checks": {}
        }
