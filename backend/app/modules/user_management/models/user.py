"""用户管理模块 - 用户模型."""

from sqlalchemy import Column, String, Boolean, Text, DateTime
from sqlalchemy.dialects.postgresql import UUID
from app.shared.database.base import Base


class User(Base):
    """用户模型."""
    
    __tablename__ = "users"
    __table_args__ = {"comment": "用户信息表"}
    
    # 基本信息
    username = Column(
        String(50), 
        unique=True, 
        nullable=False, 
        index=True,
        comment="用户名"
    )
    
    email = Column(
        String(255), 
        unique=True, 
        nullable=False, 
        index=True,
        comment="邮箱地址"
    )
    
    phone = Column(
        String(20), 
        unique=True, 
        nullable=True, 
        index=True,
        comment="手机号码"
    )
    
    # 认证信息
    password_hash = Column(
        String(255), 
        nullable=False,
        comment="密码哈希"
    )
    
    # 个人信息
    full_name = Column(
        String(100), 
        nullable=True,
        comment="真实姓名"
    )
    
    avatar_url = Column(
        String(500), 
        nullable=True,
        comment="头像URL"
    )
    
    bio = Column(
        Text, 
        nullable=True,
        comment="个人简介"
    )
    
    # 状态信息
    is_active = Column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="是否激活"
    )
    
    is_verified = Column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否已验证"
    )
    
    is_superuser = Column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否超级用户"
    )
    
    # 时间信息
    last_login_at = Column(
        DateTime(timezone=True), 
        nullable=True,
        comment="最后登录时间"
    )
    
    email_verified_at = Column(
        DateTime(timezone=True), 
        nullable=True,
        comment="邮箱验证时间"
    )
    
    phone_verified_at = Column(
        DateTime(timezone=True), 
        nullable=True,
        comment="手机验证时间"
    )
    
    def __repr__(self) -> str:
        """用户模型的字符串表示."""
        return f"<User(username={self.username}, email={self.email})>"
