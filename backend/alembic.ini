# Alembic 数据库迁移配置文件
# 柴管家项目数据库迁移管理

[alembic]
# 迁移脚本目录
script_location = alembic

# 数据库连接URL模板
# 实际URL将从环境变量中读取
sqlalchemy.url = postgresql://chaiguanjia:chaiguanjia123@localhost:5432/chaiguanjia

# 迁移文件命名模式
file_template = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s

# 时区设置
timezone = Asia/Shanghai

# 日志配置
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
