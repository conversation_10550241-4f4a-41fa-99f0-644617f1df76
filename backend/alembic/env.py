"""Alembic 环境配置文件 - 柴管家项目数据库迁移."""

import asyncio
import os
import sys
from logging.config import fileConfig
from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config
from alembic import context

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# 导入模型基类
from app.shared.database.base import Base

# Alembic配置对象
config = context.config

# 配置日志
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 设置目标元数据
target_metadata = Base.metadata


def get_database_url() -> str:
    """从环境变量获取数据库URL."""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        # 开发环境默认配置
        postgres_user = os.getenv("POSTGRES_USER", "chaiguanjia")
        postgres_password = os.getenv("POSTGRES_PASSWORD", "chaiguanjia123")
        postgres_host = os.getenv("POSTGRES_HOST", "postgres")
        postgres_port = os.getenv("POSTGRES_PORT", "5432")
        postgres_db = os.getenv("POSTGRES_DB", "chaiguanjia")
        
        database_url = f"postgresql://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/{postgres_db}"
    
    return database_url


def run_migrations_offline() -> None:
    """在离线模式下运行迁移.
    
    这种模式下不需要创建Engine，只需要提供数据库URL给context。
    """
    url = get_database_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """执行迁移操作."""
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """在异步模式下运行迁移."""
    # 获取数据库URL
    database_url = get_database_url()
    
    # 创建异步引擎配置
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = database_url
    
    # 创建异步引擎
    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """在在线模式下运行迁移.
    
    这种模式下需要创建Engine并关联connection到context。
    """
    asyncio.run(run_async_migrations())


# 根据context判断运行模式
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
