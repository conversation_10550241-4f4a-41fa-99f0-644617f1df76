# 故障排除指南

本文档提供柴管家项目开发和部署过程中常见问题的解决方案。

## 🚨 紧急问题处理

### 生产环境故障

如果遇到生产环境故障，请按以下步骤处理：

1. **立即评估影响范围**
2. **联系值班人员**
3. **查看监控和日志**
4. **执行应急预案**
5. **记录故障详情**

### 联系方式

- **技术负责人**: @tech-lead
- **运维团队**: @ops-team
- **紧急热线**: +86-xxx-xxxx-xxxx

## 🔧 开发环境问题

### 环境搭建问题

#### 1. Node.js 版本不兼容

**问题描述**: 项目要求 Node.js 18+，但系统安装的是旧版本。

**解决方案**:
```bash
# 使用 nvm 管理 Node.js 版本
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# 安装并使用 Node.js 18
nvm install 18
nvm use 18
nvm alias default 18

# 验证版本
node --version
```

#### 2. Python 版本问题

**问题描述**: 系统 Python 版本过低或存在多个版本冲突。

**解决方案**:
```bash
# Ubuntu/Debian 安装 Python 3.11
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-pip

# macOS 使用 Homebrew
brew install python@3.11

# 创建虚拟环境时指定版本
python3.11 -m venv .venv
```

#### 3. Docker 权限问题 (Linux)

**问题描述**: 执行 Docker 命令时提示权限不足。

**解决方案**:
```bash
# 将用户添加到 docker 组
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker

# 验证权限
docker run hello-world
```

### 依赖安装问题

#### 1. npm 安装失败

**问题描述**: npm install 失败或速度很慢。

**解决方案**:
```bash
# 清理 npm 缓存
npm cache clean --force

# 使用国内镜像源
npm config set registry https://registry.npmmirror.com

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install

# 如果仍有问题，尝试使用 yarn
npm install -g yarn
yarn install
```

#### 2. pip 安装失败

**问题描述**: Python 包安装失败或网络超时。

**解决方案**:
```bash
# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements-dev.txt

# 升级 pip
pip install --upgrade pip

# 清理缓存
pip cache purge

# 如果是权限问题，使用用户安装
pip install --user -r requirements-dev.txt
```

#### 3. 虚拟环境问题

**问题描述**: 虚拟环境创建失败或激活后仍使用系统 Python。

**解决方案**:
```bash
# 删除现有虚拟环境
rm -rf .venv

# 重新创建
python3 -m venv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows

# 验证 Python 路径
which python
python --version
```

### 代码质量工具问题

#### 1. ESLint 检查失败

**问题描述**: ESLint 报告大量错误或配置问题。

**解决方案**:
```bash
# 检查 ESLint 配置
cd frontend
npx eslint --print-config src/App.tsx

# 自动修复可修复的问题
npm run lint:fix

# 如果配置有问题，重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

#### 2. Black 格式化问题

**问题描述**: Black 格式化与其他工具冲突。

**解决方案**:
```bash
# 检查 Black 配置
cd backend
black --check --diff .

# 手动格式化
black .

# 检查 pyproject.toml 配置
cat pyproject.toml | grep -A 10 "\[tool.black\]"
```

#### 3. Pre-commit hooks 失败

**问题描述**: Git 提交时 pre-commit 检查失败。

**解决方案**:
```bash
# 重新安装 pre-commit hooks
pre-commit uninstall
pre-commit install

# 手动运行检查
pre-commit run --all-files

# 跳过 hooks 提交（紧急情况）
git commit --no-verify -m "emergency fix"

# 更新 hooks
pre-commit autoupdate
```

## 🐳 Docker 相关问题

### 容器启动问题

#### 1. 端口占用

**问题描述**: Docker 容器启动时提示端口已被占用。

**解决方案**:
```bash
# 查看端口占用
lsof -i :3000  # 前端端口
lsof -i :8000  # 后端端口
lsof -i :5432  # 数据库端口

# 杀死占用进程
kill -9 <PID>

# 或修改 docker-compose.yml 中的端口映射
ports:
  - "3001:3000"  # 改为其他端口
```

#### 2. 容器无法启动

**问题描述**: Docker 容器启动失败或立即退出。

**解决方案**:
```bash
# 查看容器日志
docker-compose logs <service_name>

# 查看所有服务状态
docker-compose ps

# 重新构建镜像
docker-compose build --no-cache

# 清理 Docker 资源
docker system prune -a
```

#### 3. 数据库连接失败

**问题描述**: 应用无法连接到 Docker 中的数据库。

**解决方案**:
```bash
# 检查数据库容器状态
docker-compose ps postgres

# 查看数据库日志
docker-compose logs postgres

# 进入数据库容器检查
docker-compose exec postgres psql -U chaiguanjia -d chaiguanjia_dev

# 检查网络连接
docker-compose exec backend ping postgres
```

### 性能问题

#### 1. 容器启动慢

**问题描述**: Docker 容器启动时间过长。

**解决方案**:
```bash
# 使用多阶段构建优化镜像
# 在 Dockerfile 中使用缓存层

# 预拉取基础镜像
docker pull node:18-alpine
docker pull python:3.11-slim

# 使用 Docker BuildKit
export DOCKER_BUILDKIT=1
docker-compose build
```

#### 2. 热重载不工作

**问题描述**: 代码修改后容器内应用没有自动重启。

**解决方案**:
```bash
# 检查 volume 挂载
docker-compose config

# 确保 docker-compose.yml 中有正确的 volume 配置
volumes:
  - ./frontend:/app
  - /app/node_modules  # 排除 node_modules

# 重启服务
docker-compose restart frontend
```

## 🗄️ 数据库问题

### 连接问题

#### 1. 数据库连接被拒绝

**问题描述**: 应用无法连接到数据库。

**解决方案**:
```bash
# 检查数据库服务状态
sudo systemctl status postgresql  # Linux
brew services list | grep postgres  # macOS

# 检查连接配置
psql -h localhost -U chaiguanjia -d chaiguanjia_dev

# 检查防火墙设置
sudo ufw status  # Ubuntu
```

#### 2. 认证失败

**问题描述**: 数据库用户名或密码错误。

**解决方案**:
```bash
# 重置数据库用户密码
sudo -u postgres psql
ALTER USER chaiguanjia PASSWORD 'new_password';

# 检查环境变量
echo $DATABASE_URL

# 更新 .env 文件
DATABASE_URL=postgresql://chaiguanjia:new_password@localhost:5432/chaiguanjia_dev
```

### 迁移问题

#### 1. Alembic 迁移失败

**问题描述**: 数据库迁移执行失败。

**解决方案**:
```bash
# 检查迁移状态
cd backend
alembic current

# 查看迁移历史
alembic history

# 回滚到上一个版本
alembic downgrade -1

# 重新执行迁移
alembic upgrade head

# 如果迁移文件有问题，重新生成
alembic revision --autogenerate -m "fix migration"
```

#### 2. 数据库表不存在

**问题描述**: 应用启动时提示数据库表不存在。

**解决方案**:
```bash
# 创建数据库
createdb chaiguanjia_dev

# 运行初始迁移
cd backend
alembic upgrade head

# 如果没有迁移文件，创建初始迁移
alembic revision --autogenerate -m "initial migration"
alembic upgrade head
```

## 🌐 网络和API问题

### CORS 问题

**问题描述**: 前端请求后端API时出现CORS错误。

**解决方案**:
```python
# 在 backend/app/main.py 中配置CORS
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### API 请求失败

**问题描述**: 前端无法请求后端API。

**解决方案**:
```bash
# 检查后端服务状态
curl http://localhost:8000/health

# 检查网络连接
ping localhost

# 查看浏览器开发者工具的网络面板
# 检查请求URL、状态码、错误信息

# 检查防火墙设置
sudo ufw status
```

## 📊 性能问题

### 前端性能

#### 1. 页面加载慢

**解决方案**:
```bash
# 分析包大小
cd frontend
npm run build
npm run analyze

# 启用生产模式构建
npm run build

# 检查网络请求
# 使用浏览器开发者工具的Performance面板
```

#### 2. 内存泄漏

**解决方案**:
```javascript
// 检查React组件是否正确清理
useEffect(() => {
  const timer = setInterval(() => {
    // 定时器逻辑
  }, 1000);
  
  return () => {
    clearInterval(timer); // 清理定时器
  };
}, []);
```

### 后端性能

#### 1. API响应慢

**解决方案**:
```bash
# 使用性能分析工具
cd backend
python -m cProfile -o profile.stats app/main.py

# 检查数据库查询
# 在SQL查询中添加EXPLAIN ANALYZE

# 添加缓存
# 使用Redis缓存频繁查询的数据
```

#### 2. 内存使用过高

**解决方案**:
```bash
# 监控内存使用
pip install memory-profiler
python -m memory_profiler app/main.py

# 检查是否有内存泄漏
# 使用objgraph等工具分析对象引用
```

## 🔍 调试技巧

### 前端调试

```javascript
// 使用console.log调试
console.log('Debug info:', data);

// 使用React DevTools
// 安装浏览器扩展：React Developer Tools

// 使用断点调试
debugger; // 在代码中添加断点
```

### 后端调试

```python
# 使用print调试
print(f"Debug info: {data}")

# 使用pdb调试器
import pdb; pdb.set_trace()

# 使用IPython
import IPython; IPython.embed()

# 使用logging
import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.debug("Debug message")
```

## 📞 获取帮助

### 内部资源

1. **查看日志文件**
   - 前端: 浏览器开发者工具Console
   - 后端: 应用日志文件
   - 数据库: PostgreSQL日志

2. **检查监控面板**
   - 系统资源使用情况
   - 应用性能指标
   - 错误率统计

3. **团队沟通**
   - 技术群组讨论
   - 代码审查反馈
   - 团队会议

### 外部资源

1. **官方文档**
   - [FastAPI文档](https://fastapi.tiangolo.com/)
   - [React文档](https://reactjs.org/docs/)
   - [Docker文档](https://docs.docker.com/)

2. **社区支持**
   - Stack Overflow
   - GitHub Issues
   - 技术论坛

3. **在线工具**
   - [JSON格式化](https://jsonformatter.org/)
   - [正则表达式测试](https://regex101.com/)
   - [API测试](https://httpbin.org/)

---

**文档版本**: v1.0  
**最后更新**: 2024-08-07  
**维护团队**: 柴管家开发团队
