# 柴管家项目日志使用指南

## 📋 概述

柴管家项目采用统一的日志管理体系，支持结构化日志记录、业务事件追踪和多环境配置。本指南将帮助开发者正确使用日志功能。

## 🏗️ 日志架构

### 核心组件

- **日志配置** (`config.py`): 环境相关的日志配置管理
- **格式化器** (`formatters.py`): JSON和结构化日志格式
- **处理器** (`handlers.py`): 文件和控制台日志处理
- **日志器** (`logger.py`): 统一的日志器管理
- **业务日志** (`business_logger.py`): 业务事件记录
- **模块日志** (`module_loggers.py`): 业务模块专用日志接口
- **中间件** (`middleware.py`): FastAPI请求日志中间件

### 日志类型

1. **应用日志** (`app.log`): 系统运行日志
2. **业务日志** (`business.log`): 业务事件日志
3. **错误日志**: 错误和异常信息
4. **性能日志**: API响应时间和性能指标

## 🚀 快速开始

### 基础使用

```python
from app.shared.logging import get_logger

# 获取日志器
logger = get_logger(__name__)

# 记录不同级别的日志
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")
```

### 带上下文的日志

```python
from app.shared.logging import get_logger, set_request_context

logger = get_logger(__name__)

# 设置请求上下文
set_request_context(
    request_id="req-123",
    user_id="user-456",
    trace_id="trace-789"
)

# 记录日志（自动包含上下文信息）
logger.info("用户操作完成")
```

### 业务事件日志

```python
from app.shared.logging import get_business_logger, EventType, EventStatus

business_logger = get_business_logger()

# 记录简单业务事件
business_logger.log_simple_event(
    event_type=EventType.USER_LOGIN,
    event_name="用户登录成功",
    request_id="req-123",
    user_id="user-456",
    business_data={"login_method": "email"},
    status=EventStatus.SUCCESS
)

# 使用上下文管理器记录复杂事件
with business_logger.event_context(
    event_type=EventType.MESSAGE_PROCESS,
    event_name="处理用户消息",
    request_id="req-123",
    user_id="user-456"
) as event_id:
    # 执行业务逻辑
    process_message()
    # 事件会自动记录执行时间和状态
```

## 📝 业务模块日志

### 用户管理模块

```python
from app.shared.logging import get_module_logger

user_logger = get_module_logger("user_management")

# 记录用户注册
user_logger.log_user_register(
    user_id="user-123",
    email="<EMAIL>",
    request_id="req-456",
    success=True
)

# 记录用户登录
user_logger.log_user_login(
    user_id="user-123",
    login_method="email",
    request_id="req-456",
    success=True
)
```

### 消息处理模块

```python
from app.shared.logging import get_module_logger

msg_logger = get_module_logger("message_processing")

# 记录消息流转
msg_logger.log_message_flow(
    operation="receive",
    message_id="msg-123",
    channel_id="channel-456",
    user_id="user-789",
    request_id="req-101",
    message_type="text",
    processing_time=0.15,
    success=True
)
```

### AI服务模块

```python
from app.shared.logging import get_module_logger

ai_logger = get_module_logger("ai_service")

# 记录AI交互
ai_logger.log_ai_interaction(
    operation="request",
    request_id="req-123",
    user_id="user-456",
    model_name="tongyi-qwen",
    confidence_score=0.85,
    response_time=1.2,
    success=True
)
```

## ⚙️ 配置管理

### 环境配置

日志系统会根据 `APP_ENV` 环境变量自动选择配置：

- **development**: 详细日志，控制台输出，结构化格式
- **testing**: 最小日志，无控制台输出，JSON格式
- **staging**: 标准日志，JSON格式，异步写入
- **production**: 优化日志，大文件，高性能

### 自定义配置

```python
from app.shared.logging import LogConfig, LogLevel, Environment

# 创建自定义配置
config = LogConfig(
    level=LogLevel.INFO,
    environment=Environment.PRODUCTION,
    log_dir=Path("/var/log/chaiguanjia"),
    max_file_size=50 * 1024 * 1024,  # 50MB
    backup_count=10,
    async_logging=True
)

# 应用配置
from app.shared.logging import update_config
update_config(config)
```

## 🔍 日志查询和分析

### 使用命令行工具

```bash
# 实时查看应用日志
./scripts/log-query.sh tail

# 搜索错误信息
./scripts/log-query.sh search "ERROR"

# 查看错误日志
./scripts/log-query.sh errors -n 100

# 查看业务日志
./scripts/log-query.sh business --follow

# 显示日志统计
./scripts/log-query.sh stats

# 运行详细分析
./scripts/log-query.sh analyze --start-time "2024-01-01 00:00:00"
```

### 使用Python分析工具

```bash
# 生成日志分析报告
python scripts/log-analyzer.py --command report

# 分析错误日志
python scripts/log-analyzer.py --command errors

# 分析性能指标
python scripts/log-analyzer.py --command performance

# 分析业务事件
python scripts/log-analyzer.py --command business --event-type "user.login"

# 搜索特定内容
python scripts/log-analyzer.py --command search --pattern "timeout" --level ERROR
```

## 📊 日志格式

### JSON格式 (生产环境)

```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "level": "INFO",
  "logger": "app.modules.user_management",
  "module": "user_service",
  "funcName": "create_user",
  "lineno": 45,
  "message": "用户创建成功",
  "request_id": "req-123-456",
  "user_id": "user-789",
  "trace_id": "trace-abc-def",
  "process_id": 1234,
  "thread_id": 5678,
  "extra_fields": {
    "operation": "create_user",
    "email": "<EMAIL>"
  }
}
```

### 业务事件格式

```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "event_type": "user.register",
  "event_name": "用户注册成功",
  "event_id": "evt-123-456",
  "request_id": "req-123-456",
  "user_id": "user-789",
  "trace_id": "trace-abc-def",
  "status": "success",
  "duration": 0.156,
  "business_data": {
    "email": "<EMAIL>",
    "registration_method": "email"
  }
}
```

## 🛠️ 最佳实践

### 1. 日志级别使用

- **DEBUG**: 详细的调试信息，仅开发环境
- **INFO**: 一般信息，正常业务流程
- **WARNING**: 警告信息，需要注意但不影响功能
- **ERROR**: 错误信息，功能异常但系统可继续运行
- **CRITICAL**: 严重错误，系统无法继续运行

### 2. 敏感信息处理

```python
# ❌ 错误：记录敏感信息
logger.info(f"用户登录: {username}, 密码: {password}")

# ✅ 正确：脱敏处理
logger.info(f"用户登录: {username}, 密码: ***")

# ✅ 更好：使用业务日志
business_logger.log_simple_event(
    event_type=EventType.USER_LOGIN,
    event_name="用户登录",
    user_id=user_id,
    business_data={"username": username}  # 不包含密码
)
```

### 3. 异常处理

```python
try:
    result = risky_operation()
    logger.info("操作成功完成")
except Exception as e:
    logger.error(f"操作失败: {e}", exc_info=True)
    # exc_info=True 会自动记录异常堆栈
```

### 4. 性能监控

```python
import time
from app.shared.logging import get_business_logger

business_logger = get_business_logger()

# 使用上下文管理器自动计算执行时间
with business_logger.event_context(
    event_type=EventType.AI_REQUEST,
    event_name="AI模型调用",
    request_id=request_id
) as event_id:
    result = call_ai_model(prompt)
```

## 🔧 故障排查

### 常见问题

1. **日志文件不存在**
   - 检查日志目录权限
   - 确认环境变量 `LOG_DIR` 设置正确

2. **日志格式错误**
   - 检查 `APP_ENV` 环境变量
   - 确认使用正确的格式化器

3. **性能问题**
   - 启用异步日志 (`async_logging=True`)
   - 调整日志级别
   - 增加缓冲区大小

### 调试技巧

```python
# 临时启用调试日志
import logging
logging.getLogger("app.modules.user_management").setLevel(logging.DEBUG)

# 检查日志配置
from app.shared.logging import get_current_config
config = get_current_config()
print(f"当前日志级别: {config.level}")
print(f"日志目录: {config.log_dir}")
```

## 📈 监控和告警

### 日志监控指标

- 错误日志数量和趋势
- API响应时间分布
- 业务事件成功率
- 系统资源使用情况

### 告警规则建议

- 错误日志数量超过阈值
- API响应时间超过SLA
- 业务事件失败率过高
- 日志文件大小异常

---

## 📚 相关文档

- [项目架构文档](../architecture/README.md)
- [开发环境搭建](./setup.md)
- [API文档](../api/README.md)
- [故障排除指南](../troubleshooting.md)
