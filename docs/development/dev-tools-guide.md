# 柴管家项目开发工具使用指南

本指南详细介绍了柴管家项目的开发工具集成和使用方法。

## 📋 目录

- [开发环境概览](#开发环境概览)
- [VSCode 开发容器](#vscode-开发容器)
- [调试配置](#调试配置)
- [数据库管理](#数据库管理)
- [日志管理](#日志管理)
- [性能监控](#性能监控)
- [常用命令](#常用命令)
- [故障排除](#故障排除)

## 🏗️ 开发环境概览

柴管家项目提供了完整的容器化开发环境，包括：

### 核心服务

- **Backend**: FastAPI 后端服务 (端口: 8000)
- **Frontend**: React + Vite 前端服务 (端口: 5173)
- **PostgreSQL**: 主数据库 (端口: 5432)
- **Redis**: 缓存和会话存储 (端口: 6379)
- **RabbitMQ**: 消息队列 (端口: 5672, 管理界面: 15672)

### 开发工具

- **PgAdmin**: 数据库管理界面 (端口: 5050)
- **Redis Commander**: Redis 管理界面 (端口: 8081)
- **Celery Flower**: 任务监控界面 (端口: 5555)

### 启动开发环境

```bash
# 启动完整开发环境
./scripts/docker-start.sh dev -d

# 或者启动基础服务
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up postgres redis rabbitmq pgadmin redis-commander -d
```

## 🐳 VSCode 开发容器

项目配置了 VSCode 开发容器，提供一致的开发环境。

### 使用开发容器

1. **安装扩展**

   ```
   ms-vscode-remote.remote-containers
   ```

2. **打开项目**

   - 在 VSCode 中打开项目
   - 按 `Ctrl+Shift+P` (或 `Cmd+Shift+P`)
   - 选择 "Remote-Containers: Reopen in Container"

3. **容器特性**
   - 预装所有开发依赖
   - 自动端口转发
   - 集成调试配置
   - 代码格式化和检查

### 开发容器配置

```json
{
  "name": "柴管家开发环境",
  "dockerComposeFile": [
    "../docker-compose.yml",
    "../docker-compose.dev.yml",
    "docker-compose.devcontainer.yml"
  ],
  "service": "backend",
  "workspaceFolder": "/app"
}
```

## 🐛 调试配置

项目提供了完整的调试配置，支持前后端调试。

### Python 后端调试

1. **本地调试**

   - 配置: "Python: FastAPI 应用"
   - 自动重载支持
   - 断点调试

2. **容器调试**

   - 配置: "Python: 容器内调试"
   - 远程调试支持
   - 路径映射

3. **测试调试**
   - 配置: "Python: Pytest 所有测试"
   - 单文件测试: "Python: Pytest 当前文件"

### 前端调试

1. **开发服务器调试**

   - 配置: "Node.js: 启动前端开发服务器"
   - 热重载支持

2. **浏览器调试**
   - 配置: "Chrome: 调试前端应用"
   - 源码映射支持

### 全栈调试

使用复合配置 "启动全栈应用" 同时调试前后端。

## 🗄️ 数据库管理

### PgAdmin 使用

1. **访问地址**: http://localhost:5050
2. **登录信息**:

   - 邮箱: <EMAIL>
   - 密码: admin123

3. **预配置连接**:
   - 开发数据库: chaiguanjia
   - 测试数据库: chaiguanjia_test

### 数据库管理脚本

```bash
# 查看帮助
./scripts/db-manager.sh --help

# 备份数据库
./scripts/db-manager.sh backup --env dev

# 恢复数据库
./scripts/db-manager.sh restore --file backup.sql

# 执行迁移
./scripts/db-manager.sh migrate

# 插入种子数据
./scripts/db-manager.sh seed

# 重置数据库
./scripts/db-manager.sh reset

# 性能监控
./scripts/db-manager.sh monitor

# 执行查询
./scripts/db-manager.sh query --sql "SELECT * FROM users LIMIT 5"

# 连接数据库
./scripts/db-manager.sh connect
```

## 📊 日志管理

### 日志查看

```bash
# 查看帮助
./scripts/log-manager.sh --help

# 查看服务日志
./scripts/log-manager.sh view --service backend --lines 100

# 搜索日志
./scripts/log-manager.sh search --keyword "error" --since 1h

# 过滤日志级别
./scripts/log-manager.sh filter --level ERROR --service backend

# 聚合多服务日志
./scripts/log-manager.sh aggregate --since 30m

# 导出日志
./scripts/log-manager.sh export --output logs/debug.log --service backend

# 实时监控
./scripts/log-manager.sh monitor --service backend --follow
```

### 日志级别

- **DEBUG**: 详细调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

## 📈 性能监控

### 性能监控脚本

```bash
# 查看帮助
./scripts/performance-monitor.sh --help

# 系统概览
./scripts/performance-monitor.sh overview

# 监控容器资源
./scripts/performance-monitor.sh containers --interval 10

# 监控特定服务
./scripts/performance-monitor.sh services --service backend

# 数据库性能
./scripts/performance-monitor.sh database

# 网络状态
./scripts/performance-monitor.sh network

# 磁盘使用
./scripts/performance-monitor.sh disk

# 告警检查
./scripts/performance-monitor.sh alerts --threshold 80

# 生成报告
./scripts/performance-monitor.sh report --output reports/perf.txt

# 实时监控
./scripts/performance-monitor.sh watch --duration 30
```

### 监控指标

- **CPU 使用率**: 容器和系统 CPU 使用情况
- **内存使用**: 内存占用和可用内存
- **磁盘 I/O**: 磁盘读写性能
- **网络流量**: 网络输入输出
- **数据库性能**: 连接数、查询性能、锁状态

## ⚡ 常用命令

### Docker 管理

```bash
# 启动开发环境
./scripts/docker-start.sh dev -d

# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart backend

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend

# 进入容器
docker-compose exec backend bash
```

### 代码质量

```bash
# Python 代码格式化
docker-compose exec backend black app/ tests/

# Python 导入排序
docker-compose exec backend isort app/ tests/

# Python 代码检查
docker-compose exec backend flake8 app/ tests/

# 前端代码格式化
docker-compose exec frontend npm run format

# 前端代码检查
docker-compose exec frontend npm run lint
```

### 测试运行

```bash
# 运行后端测试
docker-compose exec backend pytest tests/ -v

# 运行前端测试
docker-compose exec frontend npm test

# 运行测试覆盖率
docker-compose exec backend pytest tests/ --cov=app --cov-report=html
```

## 🔧 故障排除

### 常见问题

1. **容器启动失败**

   ```bash
   # 检查容器状态
   docker-compose ps

   # 查看错误日志
   docker-compose logs backend

   # 重新构建镜像
   docker-compose build --no-cache backend
   ```

2. **端口冲突**

   ```bash
   # 检查端口占用
   lsof -i :8000

   # 停止冲突服务
   docker-compose down
   ```

3. **数据库连接失败**

   ```bash
   # 检查数据库状态
   docker-compose exec postgres pg_isready

   # 重启数据库
   docker-compose restart postgres
   ```

4. **权限问题**

   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER .

   # 修复脚本权限
   chmod +x scripts/*.sh
   ```

### 性能问题

1. **容器资源不足**

   ```bash
   # 检查资源使用
   docker stats

   # 增加 Docker 内存限制
   # 在 Docker Desktop 设置中调整
   ```

2. **磁盘空间不足**

   ```bash
   # 清理 Docker 资源
   docker system prune -a

   # 清理旧日志
   ./scripts/log-manager.sh clean
   ```

### 调试技巧

1. **使用调试模式**

   ```bash
   # 设置调试环境变量
   export DEBUG=true
   export LOG_LEVEL=DEBUG
   ```

2. **查看详细日志**

   ```bash
   # 增加日志详细程度
   docker-compose logs -f --tail=100 backend
   ```

3. **进入容器调试**

   ```bash
   # 进入后端容器
   docker-compose exec backend bash

   # 手动运行服务
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

## 📚 相关文档

- [项目架构文档](../architecture/README.md)
- [API 文档](../api/README.md)
- [部署指南](../deployment/README.md)
- [贡献指南](../../CONTRIBUTING.md)

## 🎯 快速参考

### 一键命令

| 功能         | 命令                                        |
| ------------ | ------------------------------------------- |
| 启动开发环境 | `./scripts/docker-start.sh dev -d`          |
| 查看服务状态 | `docker-compose ps`                         |
| 查看所有日志 | `./scripts/log-manager.sh view`             |
| 系统概览     | `./scripts/performance-monitor.sh overview` |
| 数据库备份   | `./scripts/db-manager.sh backup`            |
| 健康检查     | `./scripts/health-check.sh`                 |

### 端口映射

| 服务            | 端口       | 用途              |
| --------------- | ---------- | ----------------- |
| Backend         | 8000       | FastAPI 后端 API  |
| Frontend        | 5173       | React 前端应用    |
| PostgreSQL      | 5432       | 主数据库          |
| Redis           | 6379       | 缓存服务          |
| RabbitMQ        | 5672/15672 | 消息队列/管理界面 |
| PgAdmin         | 5050       | 数据库管理        |
| Redis Commander | 8081       | Redis 管理        |
| Celery Flower   | 5555       | 任务监控          |

### VSCode 快捷键

| 功能         | 快捷键         |
| ------------ | -------------- |
| 打开命令面板 | `Ctrl+Shift+P` |
| 启动调试     | `F5`           |
| 切换断点     | `F9`           |
| 格式化代码   | `Shift+Alt+F`  |
| 查看问题     | `Ctrl+Shift+M` |
| 集成终端     | `Ctrl+``       |

---

如有问题，请查看 [FAQ](../FAQ.md) 或提交 [Issue](https://github.com/your-org/chaiguanjia/issues)。
