# 柴管家项目开发工具集成

本目录包含柴管家项目的开发工具集成文档和配置。

## 📁 目录结构

```
docs/development/
├── README.md                 # 本文件
├── dev-tools-guide.md       # 开发工具使用指南
├── debugging-guide.md       # 调试指南
├── testing-guide.md         # 测试指南
└── performance-guide.md     # 性能优化指南
```

## 🛠️ 开发工具概览

### 核心工具

1. **VSCode 开发容器**
   - 统一的开发环境
   - 预配置的扩展和设置
   - 自动化的调试配置

2. **数据库管理工具**
   - PgAdmin Web 界面
   - 命令行管理脚本
   - 自动化备份和恢复

3. **日志管理系统**
   - 集中化日志收集
   - 智能搜索和过滤
   - 实时监控功能

4. **性能监控工具**
   - 系统资源监控
   - 服务性能分析
   - 自动化告警系统

### 管理脚本

| 脚本 | 功能 | 位置 |
|------|------|------|
| `docker-start.sh` | 环境启动管理 | `scripts/` |
| `db-manager.sh` | 数据库管理 | `scripts/` |
| `log-manager.sh` | 日志管理 | `scripts/` |
| `performance-monitor.sh` | 性能监控 | `scripts/` |
| `health-check.sh` | 健康检查 | `scripts/` |

## 🚀 快速开始

### 1. 启动开发环境

```bash
# 克隆项目
git clone <repository-url>
cd chaiguanjia

# 启动开发环境
./scripts/docker-start.sh dev -d

# 验证服务状态
./scripts/health-check.sh
```

### 2. 配置 VSCode

```bash
# 安装推荐扩展
code --install-extension ms-vscode-remote.remote-containers
code --install-extension ms-python.python
code --install-extension ms-python.vscode-pylance

# 在容器中打开项目
code .
# 然后选择 "Reopen in Container"
```

### 3. 验证工具功能

```bash
# 检查数据库连接
./scripts/db-manager.sh connect

# 查看服务日志
./scripts/log-manager.sh view --service backend

# 系统性能概览
./scripts/performance-monitor.sh overview
```

## 🔧 配置文件

### VSCode 配置

- `.vscode/settings.json` - 编辑器设置
- `.vscode/launch.json` - 调试配置
- `.vscode/tasks.json` - 任务配置
- `.devcontainer/` - 开发容器配置

### Docker 配置

- `docker-compose.yml` - 基础服务配置
- `docker-compose.dev.yml` - 开发环境配置
- `.devcontainer/docker-compose.devcontainer.yml` - 开发容器配置

### 工具配置

- `infrastructure/docker/pgadmin/` - PgAdmin 配置
- `infrastructure/docker/logrotate/` - 日志轮转配置

## 📊 监控和管理

### 服务访问地址

| 服务 | 地址 | 用途 |
|------|------|------|
| 后端 API | http://localhost:8000 | FastAPI 接口 |
| 前端应用 | http://localhost:5173 | React 应用 |
| API 文档 | http://localhost:8000/docs | Swagger 文档 |
| PgAdmin | http://localhost:5050 | 数据库管理 |
| Redis Commander | http://localhost:8081 | Redis 管理 |
| RabbitMQ 管理 | http://localhost:15672 | 消息队列管理 |
| Celery Flower | http://localhost:5555 | 任务监控 |

### 默认登录信息

| 服务 | 用户名 | 密码 |
|------|--------|------|
| PgAdmin | <EMAIL> | admin123 |
| RabbitMQ | chaiguanjia | chaiguanjia123 |
| PostgreSQL | chaiguanjia | chaiguanjia123 |

## 🐛 调试和测试

### 调试配置

- **Python 后端**: 支持断点调试、热重载
- **React 前端**: 支持源码映射、浏览器调试
- **全栈调试**: 同时调试前后端

### 测试运行

```bash
# 后端测试
docker-compose exec backend pytest tests/ -v --cov=app

# 前端测试
docker-compose exec frontend npm test

# 集成测试
./scripts/run-tests.sh --integration
```

## 📈 性能优化

### 监控指标

- CPU 和内存使用率
- 数据库连接和查询性能
- API 响应时间
- 前端加载性能

### 优化建议

1. **数据库优化**
   - 使用索引优化查询
   - 定期分析查询计划
   - 监控慢查询

2. **缓存策略**
   - Redis 缓存热点数据
   - 前端资源缓存
   - API 响应缓存

3. **容器优化**
   - 合理分配资源限制
   - 使用多阶段构建
   - 优化镜像大小

## 🔒 安全考虑

### 开发环境安全

- 使用环境变量管理敏感信息
- 定期更新依赖包
- 启用安全扫描

### 数据保护

- 定期备份数据库
- 加密敏感数据
- 访问控制和审计

## 📚 相关文档

- [开发工具使用指南](dev-tools-guide.md)
- [调试指南](debugging-guide.md)
- [测试指南](testing-guide.md)
- [性能优化指南](performance-guide.md)

## 🤝 贡献指南

### 添加新工具

1. 在 `scripts/` 目录添加脚本
2. 更新相关配置文件
3. 编写使用文档
4. 添加测试用例

### 改进现有工具

1. 遵循现有代码风格
2. 保持向后兼容性
3. 更新相关文档
4. 提交 Pull Request

## ❓ 常见问题

### Q: 如何重置开发环境？

```bash
./scripts/docker-start.sh --reset
```

### Q: 如何查看详细错误日志？

```bash
./scripts/log-manager.sh search --keyword "error" --since 1h
```

### Q: 如何监控系统性能？

```bash
./scripts/performance-monitor.sh watch --duration 30
```

### Q: 如何备份和恢复数据？

```bash
# 备份
./scripts/db-manager.sh backup

# 恢复
./scripts/db-manager.sh restore --file backup.sql
```

---

如需更多帮助，请查看详细的 [开发工具使用指南](dev-tools-guide.md) 或提交 Issue。
