# 开发环境搭建指南

本文档将指导您完成柴管家项目的开发环境搭建，包括所有必需的工具和依赖。

## 📋 环境要求

### 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **内存**: 8GB+ (推荐 16GB+)
- **磁盘空间**: 10GB+ 可用空间
- **网络**: 稳定的互联网连接

### 必需软件

| 软件           | 版本要求 | 下载地址                              | 说明         |
| -------------- | -------- | ------------------------------------- | ------------ |
| Node.js        | 18.0+    | [nodejs.org](https://nodejs.org/)     | 前端开发环境 |
| Python         | 3.11+    | [python.org](https://www.python.org/) | 后端开发环境 |
| Git            | 2.30+    | [git-scm.com](https://git-scm.com/)   | 版本控制     |
| Docker         | 20.10+   | [docker.com](https://www.docker.com/) | 容器化环境   |
| Docker Compose | 2.0+     | 随 Docker 安装                        | 多容器编排   |

### 推荐工具

| 工具    | 说明           | 下载地址                                                |
| ------- | -------------- | ------------------------------------------------------- |
| VSCode  | 代码编辑器     | [code.visualstudio.com](https://code.visualstudio.com/) |
| PyCharm | Python IDE     | [jetbrains.com](https://www.jetbrains.com/pycharm/)     |
| Postman | API 测试工具   | [postman.com](https://www.postman.com/)                 |
| DBeaver | 数据库管理工具 | [dbeaver.io](https://dbeaver.io/)                       |

## 🚀 快速开始

### 方式一：Docker 环境（推荐）

这是最简单的方式，适合快速开始开发：

```bash
# 1. 克隆项目
git clone https://github.com/your-org/chaiguanjia.git
cd chaiguanjia

# 2. 启动开发环境
make dev

# 3. 等待服务启动完成
# 前端: http://localhost:3000
# 后端: http://localhost:8000
# API文档: http://localhost:8000/docs
```

### 方式二：本地环境

如果您希望在本地环境进行开发，请按照以下步骤操作。

## 🔧 详细安装步骤

### 1. 安装基础工具

#### Windows 系统

```powershell
# 使用 Chocolatey 包管理器（推荐）
# 首先安装 Chocolatey: https://chocolatey.org/install

choco install nodejs python git docker-desktop -y
```

#### macOS 系统

```bash
# 使用 Homebrew 包管理器（推荐）
# 首先安装 Homebrew: https://brew.sh/

brew install node python@3.11 git
brew install --cask docker
```

#### Ubuntu/Debian 系统

```bash
# 更新包列表
sudo apt update

# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 Python 3.11
sudo apt install python3.11 python3.11-venv python3.11-pip

# 安装 Git
sudo apt install git

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### 2. 验证安装

运行以下命令验证所有工具都已正确安装：

```bash
# 检查版本
node --version    # 应该显示 v18.x.x 或更高
python3 --version # 应该显示 3.11.x 或更高
git --version     # 应该显示 2.30.x 或更高
docker --version  # 应该显示 20.10.x 或更高
docker-compose --version # 应该显示 2.x.x 或更高
```

### 3. 克隆项目

```bash
# 克隆项目到本地
git clone https://github.com/your-org/chaiguanjia.git
cd chaiguanjia

# 查看项目结构
tree -L 2
```

### 4. 前端环境设置

```bash
# 进入前端目录
cd frontend

# 安装依赖（使用国内镜像源）
npm config set registry https://registry.npmmirror.com
npm install

# 验证安装
npm run lint
npm run type-check

# 启动开发服务器
npm run dev
```

前端服务启动后，访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 5. 后端环境设置

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python3 -m venv .venv

# 激活虚拟环境
# Linux/macOS:
source .venv/bin/activate
# Windows:
# .venv\Scripts\activate

# 升级 pip
pip install --upgrade pip

# 安装依赖（使用国内镜像源）
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements-dev.txt

# 验证安装
black --check .
flake8 .
mypy .

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

后端服务启动后，访问：

- API 服务: [http://localhost:8000](http://localhost:8000)
- API 文档: [http://localhost:8000/docs](http://localhost:8000/docs)

### 6. 数据库设置

#### 使用 Docker（推荐）

```bash
# 启动数据库服务
docker-compose up -d postgres redis rabbitmq

# 等待服务启动
sleep 10

# 运行数据库迁移
cd backend
alembic upgrade head
```

#### 本地安装

如果您希望在本地安装数据库：

```bash
# PostgreSQL 安装
# Ubuntu: sudo apt install postgresql postgresql-contrib
# macOS: brew install postgresql
# Windows: 下载安装包 https://www.postgresql.org/download/

# Redis 安装
# Ubuntu: sudo apt install redis-server
# macOS: brew install redis
# Windows: 下载安装包 https://redis.io/download

# RabbitMQ 安装
# Ubuntu: sudo apt install rabbitmq-server
# macOS: brew install rabbitmq
# Windows: 下载安装包 https://www.rabbitmq.com/download.html
```

### 7. 开发工具配置

#### VSCode 配置

```bash
# 安装推荐扩展
code --install-extension ms-python.python
code --install-extension ms-python.black-formatter
code --install-extension dbaeumer.vscode-eslint
code --install-extension esbenp.prettier-vscode

# 打开项目
code .
```

项目已包含 VSCode 配置文件（`.vscode/`），会自动应用推荐设置。

#### Git 配置

```bash
# 配置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 安装 pre-commit hooks
pip install pre-commit
pre-commit install
pre-commit install --hook-type commit-msg

# 测试 hooks
pre-commit run --all-files
```

## 🧪 验证环境

运行以下命令验证开发环境是否正确配置：

```bash
# 运行验证脚本
./scripts/verify-tools.sh

# 运行所有检查
make check-all

# 运行测试
make test
```

## 🔧 常用开发命令

### 项目级命令

```bash
# 启动完整开发环境
make dev

# 运行所有检查
make check-all

# 运行所有测试
make test

# 代码格式化
make format

# 清理环境
make clean
```

### 前端命令

```bash
cd frontend

# 开发服务器
npm run dev

# 代码检查
npm run lint
npm run type-check

# 代码格式化
npm run format

# 运行测试
npm run test

# 构建生产版本
npm run build
```

### 后端命令

```bash
cd backend

# 开发服务器
uvicorn app.main:app --reload

# 代码检查
flake8 .
mypy .

# 代码格式化
black .
isort .

# 运行测试
pytest

# 数据库迁移
alembic upgrade head
```

## 🐛 故障排除

### 常见问题

#### 1. Node.js 版本问题

```bash
# 使用 nvm 管理 Node.js 版本
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

#### 2. Python 虚拟环境问题

```bash
# 删除现有虚拟环境
rm -rf .venv

# 重新创建
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements-dev.txt
```

#### 3. Docker 权限问题（Linux）

```bash
# 将用户添加到 docker 组
sudo usermod -aG docker $USER

# 重新登录或重启
newgrp docker
```

#### 4. 端口占用问题

```bash
# 查看端口占用
lsof -i :3000  # 前端端口
lsof -i :8000  # 后端端口

# 杀死占用进程
kill -9 <PID>
```

### 获取帮助

如果遇到问题：

1. 查看 [故障排除文档](../troubleshooting.md)
2. 搜索 [GitHub Issues](https://github.com/your-org/chaiguanjia/issues)
3. 在团队群组中提问
4. 联系项目维护者

## 📚 下一步

环境搭建完成后，建议阅读：

- [编码规范](coding-standards.md) - 了解代码风格要求
- [贡献指南](../../CONTRIBUTING.md) - 学习如何贡献代码
- [API 文档](../api/README.md) - 了解接口设计
- [架构文档](../architecture/README.md) - 理解系统架构

## 🔐 环境变量配置

### 前端环境变量

在 `frontend/` 目录下创建 `.env.local` 文件：

```bash
# API 服务地址
VITE_API_BASE_URL=http://localhost:8000

# 应用配置
VITE_APP_TITLE=柴管家开发环境
VITE_APP_VERSION=0.1.0

# 调试模式
VITE_DEBUG=true
```

### 后端环境变量

在 `backend/` 目录下创建 `.env` 文件：

```bash
# 数据库配置
DATABASE_URL=postgresql://chaiguanjia:password@localhost:5432/chaiguanjia_dev

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# RabbitMQ 配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# JWT 配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
DEBUG=true
LOG_LEVEL=DEBUG
```

## 🧪 开发工作流

### 1. 功能开发流程

```bash
# 1. 创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/新功能名称

# 2. 开发和测试
# 编写代码...
make test
make lint

# 3. 提交代码
git add .
git commit -m "feat(scope): 添加新功能描述"

# 4. 推送分支
git push origin feature/新功能名称

# 5. 创建 Pull Request
# 在 GitHub 上创建 PR
```

### 2. 代码质量检查

```bash
# 运行所有检查
make check-all

# 单独运行检查
make lint          # 代码风格检查
make type-check    # 类型检查
make test          # 运行测试
make security      # 安全检查
```

### 3. 调试技巧

#### 前端调试

```bash
# 启动调试模式
npm run dev

# 在浏览器中：
# 1. 打开开发者工具 (F12)
# 2. 设置断点
# 3. 使用 React DevTools 扩展
```

#### 后端调试

```bash
# 使用 debugger
import pdb; pdb.set_trace()

# 或使用 IPython
import IPython; IPython.embed()

# VSCode 调试配置已包含在 .vscode/launch.json
```

## 📊 性能优化

### 前端性能

```bash
# 分析包大小
npm run build
npm run analyze

# 性能测试
npm run lighthouse
```

### 后端性能

```bash
# 性能分析
python -m cProfile -o profile.stats app/main.py

# 内存分析
pip install memory-profiler
python -m memory_profiler app/main.py
```

---

**文档版本**: v1.0
**最后更新**: 2024-08-07
**维护团队**: 柴管家开发团队
