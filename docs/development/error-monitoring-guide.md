# 柴管家项目错误监控使用指南

## 📋 概述

柴管家项目实现了完整的错误监控体系，包括错误捕获、分类、跟踪、工作流管理和分析报告。本指南将帮助开发者和运维人员正确使用错误监控功能。

## 🏗️ 错误监控架构

### 核心组件

- **错误监控器** (`error_monitor.py`): 自动捕获和分类异常
- **错误处理器** (`error_handler.py`): 全局异常处理和响应生成
- **错误跟踪器** (`error_tracker.py`): 错误记录的持久化存储
- **错误工作流** (`error_workflow.py`): 错误处理流程管理
- **分析工具**: 错误统计和趋势分析

### 错误分类体系

#### 错误分类 (ErrorCategory)
- **系统错误**: `system_error`, `database_error`, `network_error`, `timeout_error`
- **业务错误**: `business_logic_error`, `validation_error`, `authentication_error`, `authorization_error`
- **外部服务错误**: `external_api_error`, `ai_service_error`, `third_party_error`
- **用户错误**: `user_input_error`, `not_found_error`, `conflict_error`

#### 严重程度 (ErrorSeverity)
- **LOW**: 不影响核心功能，可延后处理
- **MEDIUM**: 影响部分功能，需要关注
- **HIGH**: 影响核心功能，需要尽快处理
- **CRITICAL**: 系统无法正常运行，需要立即处理

#### 处理优先级 (ErrorPriority)
- **IMMEDIATE**: 立即处理（系统无法运行）
- **URGENT**: 紧急处理（核心功能受影响）
- **HIGH**: 高优先级（重要功能受影响）
- **MEDIUM**: 中优先级（部分功能受影响）
- **LOW**: 低优先级（可延后处理）

## 🚀 快速开始

### 基础错误捕获

```python
from app.shared.monitoring import get_error_monitor

# 获取错误监控器
error_monitor = get_error_monitor()

try:
    # 可能出错的代码
    risky_operation()
except Exception as e:
    # 捕获并记录错误
    error_info = error_monitor.capture_exception(
        exception=e,
        request_id="req-123",
        user_id="user-456",
        business_context={
            "operation": "user_registration",
            "step": "email_validation"
        }
    )
    print(f"错误已记录: {error_info.error_id}")
```

### 使用装饰器自动处理错误

```python
from app.shared.monitoring import exception_handler

@exception_handler(include_details=True)
async def register_user(user_data: dict):
    """用户注册接口"""
    # 如果发生异常，装饰器会自动捕获并返回错误响应
    validate_user_data(user_data)
    create_user(user_data)
    return {"success": True}
```

### FastAPI集成

```python
from fastapi import FastAPI
from app.shared.monitoring import setup_error_handlers

app = FastAPI()

# 设置全局错误处理器
setup_error_handlers(app, include_details=True)  # 开发环境
# setup_error_handlers(app, include_details=False)  # 生产环境
```

## 🔧 错误工作流管理

### 处理新错误

```python
from app.shared.monitoring import get_error_workflow, ErrorStatus, ResolutionType

workflow = get_error_workflow()

# 错误会自动进入工作流
# 手动更新错误状态
success = workflow.update_error_status(
    error_id="error-123",
    new_status=ErrorStatus.INVESTIGATING,
    assignee="<EMAIL>",
    resolution_notes="开始调查数据库连接问题",
    updated_by="admin"
)
```

### 解决错误

```python
# 标记错误为已解决
success = workflow.update_error_status(
    error_id="error-123",
    new_status=ErrorStatus.RESOLVED,
    resolution_type=ResolutionType.FIXED,
    resolution_notes="修复了数据库连接池配置",
    updated_by="developer"
)
```

### 获取待处理错误

```python
# 获取所有待处理错误
pending_errors = workflow.get_pending_errors()

# 获取特定优先级的错误
urgent_errors = workflow.get_pending_errors(priority="urgent")

# 获取分配给特定人员的错误
my_errors = workflow.get_pending_errors(assignee="<EMAIL>")
```

## 📊 错误查询和分析

### 搜索错误

```python
from app.shared.monitoring import get_error_tracker
from datetime import datetime, timedelta

tracker = get_error_tracker()

# 搜索最近7天的错误
start_time = datetime.now() - timedelta(days=7)
errors = tracker.search_errors(
    category="database_error",
    severity="high",
    start_time=start_time,
    is_handled=False,
    limit=50
)
```

### 获取错误统计

```python
# 获取错误统计信息
stats = tracker.get_error_statistics()
print(f"总错误数: {stats['total_errors']}")
print(f"错误分类分布: {stats['category_distribution']}")

# 获取工作流统计
workflow_stats = workflow.get_workflow_statistics()
print(f"待处理错误: {workflow_stats['pending_errors']}")
print(f"超期错误: {workflow_stats['overdue_errors']}")
```

## 🛠️ 命令行工具

### 错误管理工具

```bash
# 查看错误列表
./scripts/error-manager.py list --days 7 --limit 20

# 查看特定分类的错误
./scripts/error-manager.py list --category database_error --severity high

# 查看错误详情
./scripts/error-manager.py show error-123-456

# 更新错误状态
./scripts/error-manager.py update error-123-456 \
  --status investigating \
  --assignee <EMAIL> \
  --notes "开始调查问题"

# 查看待处理错误
./scripts/error-manager.py pending --priority urgent

# 查看错误监控仪表板
./scripts/error-manager.py dashboard
```

### 错误分析工具

```bash
# 生成错误分析报告
./scripts/error-analyzer.py --command report

# 分析错误趋势
./scripts/error-analyzer.py --command trends \
  --start-time "2024-01-01 00:00:00" \
  --end-time "2024-01-07 23:59:59"

# 分析错误模式
./scripts/error-analyzer.py --command patterns --format json

# 分析严重错误
./scripts/error-analyzer.py --command critical

# 分析用户相关错误
./scripts/error-analyzer.py --command users
```

## ⚙️ 配置和自定义

### 自定义工作流规则

```python
from app.shared.monitoring import get_error_workflow, ErrorWorkflowRule, ErrorCategory, ErrorSeverity, ErrorPriority

workflow = get_error_workflow()

# 添加自定义规则
custom_rule = ErrorWorkflowRule(
    category=ErrorCategory.AI_SERVICE_ERROR,
    severity=ErrorSeverity.HIGH,
    priority=ErrorPriority.URGENT,
    auto_assign=True,
    assignee="<EMAIL>",
    escalation_hours=2,
    notification_channels=["email", "slack"]
)

workflow.workflow_rules[(ErrorCategory.AI_SERVICE_ERROR, ErrorSeverity.HIGH)] = custom_rule
```

### 添加通知处理器

```python
def email_notification_handler(error_info, rule, workflow_data):
    """邮件通知处理器"""
    send_email(
        to=rule.assignee,
        subject=f"错误告警: {error_info.error_type}",
        body=f"错误ID: {error_info.error_id}\n严重程度: {error_info.severity}\n..."
    )

def slack_notification_handler(error_info, rule, workflow_data):
    """Slack通知处理器"""
    send_slack_message(
        channel="#alerts",
        message=f"🚨 错误告警: {error_info.error_type} (ID: {error_info.error_id})"
    )

# 注册通知处理器
workflow.add_notification_handler("email", email_notification_handler)
workflow.add_notification_handler("slack", slack_notification_handler)
```

## 📈 监控和告警

### 关键指标监控

- **错误率**: 每小时/每天的错误数量
- **严重错误数**: CRITICAL和HIGH级别错误的数量
- **未处理错误数**: 待处理错误的积压情况
- **平均处理时间**: 从错误发生到解决的平均时间
- **超期错误数**: 超过处理时限的错误数量

### 告警规则建议

```python
# 示例告警规则
def check_error_alerts():
    """检查错误告警条件"""
    workflow = get_error_workflow()
    stats = workflow.get_workflow_statistics()
    
    # 严重错误告警
    if stats.get('critical_errors', 0) > 0:
        send_alert("发现严重错误，需要立即处理")
    
    # 超期错误告警
    if stats.get('overdue_errors', 0) > 5:
        send_alert(f"有{stats['overdue_errors']}个错误超期未处理")
    
    # 错误率告警
    if stats.get('total_errors', 0) > 100:  # 每小时超过100个错误
        send_alert("错误率异常，请检查系统状态")
```

## 🧪 测试和验证

### 运行测试

```bash
# 测试错误监控系统功能
./scripts/test-error-monitoring.py

# 测试日志系统功能
./scripts/test-logging.py
```

### 手动测试

```python
# 创建测试错误
from app.shared.monitoring import get_error_monitor

monitor = get_error_monitor()

try:
    raise ValueError("这是一个测试错误")
except Exception as e:
    error_info = monitor.capture_exception(e, request_id="test-123")
    print(f"测试错误已创建: {error_info.error_id}")
```

## 🔍 故障排查

### 常见问题

1. **错误数据库无法访问**
   - 检查 `logs/errors.db` 文件权限
   - 确认日志目录存在且可写

2. **错误未被捕获**
   - 检查是否正确设置了全局异常处理器
   - 确认错误监控器已初始化

3. **通知未发送**
   - 检查通知处理器是否正确注册
   - 确认通知渠道配置正确

### 调试技巧

```python
# 启用调试日志
import logging
logging.getLogger("error_monitor").setLevel(logging.DEBUG)
logging.getLogger("error_workflow").setLevel(logging.DEBUG)

# 检查错误监控器状态
monitor = get_error_monitor()
stats = monitor.get_error_statistics()
print(f"监控器统计: {stats}")

# 检查工作流状态
workflow = get_error_workflow()
workflow_stats = workflow.get_workflow_statistics()
print(f"工作流统计: {workflow_stats}")
```

## 📚 最佳实践

### 1. 错误分类
- 准确分类错误，便于统计和处理
- 为业务特定错误创建自定义分类
- 合理设置错误严重程度

### 2. 上下文信息
- 提供充分的业务上下文信息
- 包含请求ID、用户ID等关键标识
- 记录操作步骤和参数

### 3. 处理流程
- 及时处理高优先级错误
- 建立清晰的升级机制
- 记录详细的处理过程

### 4. 监控告警
- 设置合理的告警阈值
- 避免告警疲劳
- 定期回顾和调整规则

---

## 📚 相关文档

- [日志使用指南](./logging-guide.md)
- [项目架构文档](../architecture/README.md)
- [API文档](../api/README.md)
- [故障排除指南](../troubleshooting.md)
