# 柴管家项目提交信息规范 (Conventional Commits)

## 概述

本项目严格遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范，确保提交历史清晰、可读，并支持自动化工具（如自动生成 CHANGELOG、语义化版本控制等）。

## 提交信息格式

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 基本结构说明

- **type**: 提交类型（必需）
- **scope**: 影响范围（可选）
- **description**: 简短描述（必需）
- **body**: 详细描述（可选）
- **footer**: 脚注信息（可选）

## 提交类型 (Type)

### 主要类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `feat` | 新功能 | `feat(auth): 添加用户登录功能` |
| `fix` | 修复bug | `fix(api): 修复用户信息获取接口错误` |
| `docs` | 文档变更 | `docs(readme): 更新安装说明` |
| `style` | 代码格式调整（不影响功能） | `style(frontend): 统一代码缩进格式` |
| `refactor` | 重构代码（既不是新功能也不是修复） | `refactor(service): 优化用户服务代码结构` |
| `test` | 添加或修改测试 | `test(auth): 添加登录功能单元测试` |
| `chore` | 构建过程或辅助工具的变动 | `chore(deps): 更新依赖包版本` |

### 特殊类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `perf` | 性能优化 | `perf(api): 优化数据库查询性能` |
| `ci` | CI/CD配置变更 | `ci(github): 添加自动化测试流水线` |
| `build` | 构建系统或外部依赖变更 | `build(docker): 更新Docker配置` |
| `revert` | 回滚之前的提交 | `revert: 回滚feat(auth): 添加用户登录功能` |

## 作用域 (Scope)

作用域应该是受影响的模块或组件名称，使用kebab-case格式：

### 后端作用域
- `auth` - 认证模块
- `user` - 用户管理
- `api` - API接口
- `database` - 数据库相关
- `config` - 配置文件
- `middleware` - 中间件

### 前端作用域
- `ui` - 用户界面
- `components` - 组件
- `pages` - 页面
- `hooks` - React Hooks
- `services` - 服务层
- `utils` - 工具函数

### 通用作用域
- `docs` - 文档
- `tests` - 测试
- `deps` - 依赖管理
- `docker` - 容器化
- `ci` - 持续集成

## 描述 (Description)

- 使用中文描述
- 使用现在时态："添加功能" 而不是 "添加了功能"
- 首字母小写
- 结尾不加句号
- 限制在50个字符以内

## 正文 (Body)

- 详细解释变更的动机和实现方式
- 与之前行为的对比
- 使用中文描述
- 每行限制在72个字符以内

## 脚注 (Footer)

### Breaking Changes
如果提交包含破坏性变更，必须在脚注中说明：

```
BREAKING CHANGE: 用户API接口返回格式发生变化，需要更新前端调用代码
```

### 关联Issue
关联相关的Issue或任务：

```
Closes #123
Fixes #456
Refs #789
```

## 提交信息示例

### 简单提交
```
feat(auth): 添加用户登录功能
```

### 带作用域的提交
```
fix(api): 修复用户信息获取接口返回空数据的问题
```

### 完整格式提交
```
feat(user): 添加用户头像上传功能

支持用户上传和更新个人头像，包括：
- 图片格式验证（支持jpg、png、gif）
- 文件大小限制（最大2MB）
- 自动生成缩略图
- 头像历史记录

Closes #234
```

### 破坏性变更提交
```
feat(api): 重构用户API接口结构

统一API返回格式，提高接口一致性和可维护性

BREAKING CHANGE: 所有用户相关API的返回格式发生变化，
需要更新前端调用代码以适配新的数据结构
```

## 验证工具

项目将配置以下工具来强制执行提交规范：

1. **commitlint**: 验证提交信息格式
2. **husky**: Git hooks管理
3. **pre-commit**: 提交前检查

## 常见错误示例

### ❌ 错误示例
```
# 类型错误
update: 更新用户功能

# 描述不清晰
feat: 修改

# 格式错误
feat:添加登录功能

# 使用过去时
feat(auth): 添加了用户登录功能

# 描述过长
feat(auth): 添加用户登录功能包括用户名密码验证、记住登录状态、自动跳转等完整的登录流程实现
```

### ✅ 正确示例
```
feat(auth): 添加用户登录功能

fix(api): 修复用户信息获取接口错误

docs(readme): 更新项目安装说明

refactor(user): 优化用户服务代码结构
```

## 自动化集成

提交规范将与以下自动化工具集成：

- **自动生成CHANGELOG**: 基于提交类型自动分类变更
- **语义化版本**: 根据提交类型自动确定版本号
- **发布说明**: 自动生成发布说明文档
- **CI/CD触发**: 根据提交类型触发不同的构建流程

---

**遵循此规范是强制性的，所有不符合规范的提交将被拒绝。**
