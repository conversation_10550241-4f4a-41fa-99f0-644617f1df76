# GitHub Secrets 配置指南

## 📋 概述

本文档指导如何为柴管家项目配置必要的GitHub Secrets，以支持完整的CI/CD流水线功能。

## 🔑 必需的Secrets

### 1. CODECOV_TOKEN (必需)
用于上传测试覆盖率报告到Codecov。

**获取方式:**
1. 访问 [Codecov](https://codecov.io/)
2. 使用GitHub账号登录
3. 添加仓库 `Amoresdk/chaiguanjia_8.4`
4. 在仓库设置中找到Upload Token
5. 复制Token值

**设置步骤:**
1. 进入GitHub仓库 → Settings → Secrets and variables → Actions
2. 点击 "New repository secret"
3. Name: `CODECOV_TOKEN`
4. Secret: 粘贴从Codecov获取的Token
5. 点击 "Add secret"

### 2. SLACK_WEBHOOK_URL (可选)
用于发送CI/CD失败通知到Slack频道。

**获取方式:**
1. 在Slack工作区中创建一个应用
2. 启用Incoming Webhooks功能
3. 创建Webhook URL
4. 复制Webhook URL

**设置步骤:**
1. 进入GitHub仓库 → Settings → Secrets and variables → Actions
2. 点击 "New repository secret"
3. Name: `SLACK_WEBHOOK_URL`
4. Secret: 粘贴Slack Webhook URL
5. 点击 "Add secret"

### 3. SNYK_TOKEN (可选)
用于Snyk安全扫描功能。

**获取方式:**
1. 访问 [Snyk](https://snyk.io/)
2. 注册或登录账号
3. 进入Account Settings → API Token
4. 复制API Token

**设置步骤:**
1. 进入GitHub仓库 → Settings → Secrets and variables → Actions
2. 点击 "New repository secret"
3. Name: `SNYK_TOKEN`
4. Secret: 粘贴Snyk API Token
5. 点击 "Add secret"

### 4. GITLEAKS_LICENSE (可选)
用于GitLeaks Pro功能（如果使用）。

**获取方式:**
1. 购买GitLeaks Pro许可证
2. 获取许可证密钥

**设置步骤:**
1. 进入GitHub仓库 → Settings → Secrets and variables → Actions
2. 点击 "New repository secret"
3. Name: `GITLEAKS_LICENSE`
4. Secret: 粘贴许可证密钥
5. 点击 "Add secret"

## 🛠️ 设置验证

### 验证Secrets是否正确设置

1. **检查Secrets列表:**
   - 进入GitHub仓库 → Settings → Secrets and variables → Actions
   - 确认所有必需的Secrets都已添加
   - 注意：出于安全考虑，Secret的值不会显示

2. **通过CI运行验证:**
   - 创建一个测试PR
   - 观察CI工作流是否能正常访问Secrets
   - 检查覆盖率报告是否正常上传

### 常见问题排查

**问题1: Codecov上传失败**
```
Error: Codecov token not found
```
**解决方案:**
- 确认CODECOV_TOKEN已正确设置
- 检查Token是否有效
- 确认仓库在Codecov中已正确配置

**问题2: Slack通知不工作**
```
Error: Webhook URL invalid
```
**解决方案:**
- 确认SLACK_WEBHOOK_URL格式正确
- 测试Webhook URL是否可访问
- 检查Slack应用权限设置

**问题3: Snyk扫描失败**
```
Error: Authentication failed
```
**解决方案:**
- 确认SNYK_TOKEN有效
- 检查Snyk账号权限
- 验证Token是否过期

## 📚 相关文档

- [GitHub Secrets 官方文档](https://docs.github.com/en/actions/security-guides/encrypted-secrets)
- [Codecov 集成指南](https://docs.codecov.com/docs/quick-start)
- [Slack Webhooks 文档](https://api.slack.com/messaging/webhooks)
- [Snyk API 文档](https://snyk.docs.apiary.io/)

## 🔒 安全注意事项

1. **Secret管理:**
   - 定期轮换API Token
   - 使用最小权限原则
   - 不要在代码中硬编码敏感信息

2. **访问控制:**
   - 限制仓库访问权限
   - 定期审查协作者权限
   - 监控Secret使用情况

3. **备份和恢复:**
   - 记录所有Secret的来源
   - 建立Token轮换流程
   - 准备应急恢复方案

## ✅ 设置检查清单

- [ ] CODECOV_TOKEN 已设置并验证
- [ ] SLACK_WEBHOOK_URL 已设置（如需要）
- [ ] SNYK_TOKEN 已设置（如需要）
- [ ] GITLEAKS_LICENSE 已设置（如需要）
- [ ] 通过测试PR验证所有功能正常
- [ ] 文档已更新，团队成员已知晓

---

**注意**: 设置完Secrets后，建议创建一个测试PR来验证CI/CD流水线是否能正常工作。
