# 柴管家项目容器化架构设计

> 📋 **文档版本**: v1.0
> 📅 **创建日期**: 2024-08-06
> 👥 **维护团队**: 柴管家开发团队

## 🎯 设计目标

基于项目初始化方案中的**一致性、自动化、快速反馈**三大支柱，设计完整的容器化架构，实现：

- ✅ **环境一致性**: 开发、测试、生产环境完全一致
- ✅ **一键启动**: 所有服务自动编排和配置
- ✅ **水平扩展**: 支持服务副本扩展
- ✅ **故障隔离**: 服务间独立运行，故障不传播
- ✅ **资源优化**: 合理的资源分配和限制

## 🏗️ 架构概览

### 核心服务组件

| 服务名称          | 技术栈            | 端口       | 职责描述                         |
| ----------------- | ----------------- | ---------- | -------------------------------- |
| **nginx**         | Nginx Alpine      | 80/443     | 反向代理、负载均衡、静态资源服务 |
| **frontend**      | React + Vite      | 5173       | 前端 SPA 应用，用户界面          |
| **backend**       | FastAPI + uvicorn | 8000       | 后端 API 服务，业务逻辑处理      |
| **postgres**      | PostgreSQL 15     | 5432       | 主数据库，持久化存储             |
| **redis**         | Redis 7           | 6379       | 缓存服务，会话存储               |
| **rabbitmq**      | RabbitMQ 3.12     | 5672/15672 | 消息队列，异步任务调度           |
| **celery-worker** | Celery + Python   | -          | 异步任务处理器                   |
| **celery-beat**   | Celery + Python   | -          | 定时任务调度器                   |
| **celery-flower** | Flower            | 5555       | Celery 任务监控界面              |

### 开发工具 (仅开发环境)

| 工具名称                | 端口  | 用途                  |
| ----------------------- | ----- | --------------------- |
| **pgadmin**             | 5050  | PostgreSQL 数据库管理 |
| **redis-commander**     | 8081  | Redis 缓存管理        |
| **rabbitmq-management** | 15672 | RabbitMQ 队列管理     |

## 🌐 网络架构

### 网络拓扑

```
外部网络 (Internet)
    ↓
🌐 Nginx (172.20.0.40)
    ├── → 🎨 Frontend (172.20.0.21)
    └── → ⚡ Backend (172.20.0.20)
            ├── → 🗄️ PostgreSQL (172.20.0.10)
            ├── → ⚡ Redis (172.20.0.11)
            └── → 🐰 RabbitMQ (172.20.0.12)
                    ├── → 🔧 Worker-1 (172.20.0.30)
                    ├── → 🔧 Worker-2 (172.20.0.31)
                    └── → ⏰ Beat (172.20.0.32)
```

### 网络配置

- **网络名称**: `chaiguanjia-network`
- **网络类型**: Bridge
- **子网范围**: `172.20.0.0/16`
- **网关地址**: `172.20.0.1`

## 💾 数据持久化策略

### 数据卷配置

| 数据卷名称      | 挂载路径                   | 用途         | 备份策略     |
| --------------- | -------------------------- | ------------ | ------------ |
| `postgres-data` | `/var/lib/postgresql/data` | 数据库数据   | 每日自动备份 |
| `redis-data`    | `/data`                    | Redis 持久化 | AOF + RDB    |
| `rabbitmq-data` | `/var/lib/rabbitmq`        | 消息队列数据 | 集群同步     |
| `nginx-logs`    | `/var/log/nginx`           | 访问日志     | 日志轮转     |

### 备份策略

- **数据库**: 每日凌晨 2 点自动备份，保留 30 天
- **配置文件**: Git 版本控制
- **日志文件**: 按大小轮转，保留 7 天

## 🔧 容器配置详解

### 1. 前端容器 (frontend)

**多阶段构建**:

- `development`: 开发环境，支持热重载
- `production`: 生产环境，Nginx 静态服务

**关键配置**:

```yaml
environment:
  VITE_API_BASE_URL: http://localhost:8000
  VITE_APP_ENV: development
volumes:
  - ./frontend:/app:cached # 代码热重载
  - /app/node_modules # 依赖缓存
```

### 2. 后端容器 (backend)

**多阶段构建**:

- `base`: 基础 Python 环境
- `dependencies`: 依赖安装
- `development`: 开发环境
- `production`: 生产环境

**关键配置**:

```yaml
environment:
  DATABASE_URL: ************************************/db
  REDIS_URL: redis://:pass@redis:6379/0
  CELERY_BROKER_URL: amqp://user:pass@rabbitmq:5672/vhost
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
```

### 3. 数据库容器 (postgres)

**配置特点**:

- 使用 Alpine 镜像，体积小
- 自动初始化脚本
- 性能优化配置
- 健康检查

**初始化脚本**:

- 创建应用数据库和用户
- 安装必要扩展 (uuid-ossp, pg_trgm)
- 创建日志和监控表
- 设置性能参数

### 4. 异步任务系统

**Celery Worker**:

- 多进程并发处理
- 自动重启机制
- 内存限制保护
- 任务超时控制

**Celery Beat**:

- 单实例运行
- 定时任务调度
- 持久化调度状态

**Celery Flower**:

- 实时任务监控
- 性能指标展示
- 基础认证保护

## 🚀 部署指南

### 环境准备

1. **系统要求**:

   - Docker 20.10+
   - Docker Compose 2.0+
   - 最少 4GB RAM
   - 最少 20GB 磁盘空间

2. **环境配置**:

   ```bash
   # 复制环境变量文件
   cp .env.example .env

   # 编辑环境变量
   vim .env
   ```

### 开发环境启动

```bash
# 一键启动开发环境
./scripts/docker-start.sh dev

# 或使用docker-compose
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

### 生产环境部署

```bash
# 创建密钥文件
mkdir -p secrets
echo "your-postgres-password" > secrets/postgres_password.txt
echo "your-redis-password" > secrets/redis_password.txt
echo "your-rabbitmq-password" > secrets/rabbitmq_password.txt

# 启动生产环境
./scripts/docker-start.sh prod -d
```

### 服务扩展

```bash
# 扩展后端服务到3个副本
docker-compose up --scale backend=3

# 扩展Celery Worker到5个副本
docker-compose up --scale celery-worker=5
```

## 📊 监控和健康检查

### 健康检查端点

| 服务       | 健康检查 URL                   | 检查间隔 |
| ---------- | ------------------------------ | -------- |
| Frontend   | `http://localhost:5173`        | 30s      |
| Backend    | `http://localhost:8000/health` | 30s      |
| PostgreSQL | `pg_isready`                   | 10s      |
| Redis      | `redis-cli ping`               | 10s      |
| RabbitMQ   | `rabbitmq-diagnostics ping`    | 30s      |

### 监控指标

- **应用指标**: API 响应时间、错误率、吞吐量
- **系统指标**: CPU、内存、磁盘、网络使用率
- **业务指标**: 用户活跃度、任务处理量、消息队列长度

## 🔒 安全配置

### 网络安全

- 内部服务仅在私有网络通信
- 敏感端口不对外暴露
- Nginx 反向代理统一入口

### 认证安全

- 数据库密码通过环境变量配置
- 生产环境使用 Docker Secrets
- API 接口 JWT 认证
- 管理界面基础认证

### 容器安全

- 非 root 用户运行
- 最小权限原则
- 定期更新基础镜像
- 漏洞扫描检查

## 🛠️ 故障排除

### 常见问题

1. **容器启动失败**:

   ```bash
   # 查看容器日志
   docker-compose logs [service-name]

   # 检查容器状态
   docker-compose ps
   ```

2. **数据库连接失败**:

   ```bash
   # 检查数据库健康状态
   docker-compose exec postgres pg_isready

   # 查看数据库日志
   docker-compose logs postgres
   ```

3. **服务间通信问题**:

   ```bash
   # 检查网络连接
   docker network inspect chaiguanjia_chaiguanjia-network

   # 测试服务连通性
   docker-compose exec backend ping postgres
   ```

### 性能优化

1. **资源限制**:

   - 根据实际负载调整 CPU 和内存限制
   - 监控资源使用情况
   - 合理设置副本数量

2. **缓存优化**:
   - 启用 Docker 层缓存
   - 使用多阶段构建
   - 优化镜像大小

## 📈 扩展规划

### 水平扩展

- **无状态服务**: Frontend、Backend、Celery Worker
- **有状态服务**: PostgreSQL (主从复制)、Redis (集群模式)

### 云原生迁移

- **Kubernetes**: 容器编排平台
- **Helm Charts**: 应用包管理
- **Istio**: 服务网格
- **Prometheus + Grafana**: 监控告警

## 🚀 快速启动指南

### 5 分钟快速体验

```bash
# 1. 克隆项目
git clone <repository-url>
cd chaiguanjia

# 2. 复制环境配置
cp .env.example .env

# 3. 一键启动开发环境
chmod +x scripts/docker-start.sh
./scripts/docker-start.sh dev

# 4. 等待服务启动完成，访问应用
# 前端: http://localhost:5173
# 后端API: http://localhost:8000/docs
```

### 验证部署

```bash
# 检查所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f

# 测试API健康检查
curl http://localhost:8000/health

# 测试前端访问
curl http://localhost:5173
```

---

**📝 更新记录**:

- v1.0 (2024-08-06): 初始版本，完整容器化架构设计
