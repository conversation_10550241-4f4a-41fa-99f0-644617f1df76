# 部署文档

柴管家平台部署和运维相关文档，涵盖开发、测试、生产环境的部署方案。

## 🚀 部署概览

柴管家支持多种部署方式，从本地开发到生产环境，提供灵活的部署选择。

### 部署环境

- **开发环境** - 本地开发和调试
- **测试环境** - 功能测试和集成测试
- **预生产环境** - 生产前验证
- **生产环境** - 正式服务环境

### 部署方式

- **Docker Compose** - 适用于开发和小规模部署
- **Kubernetes** - 适用于生产环境和大规模部署
- **传统部署** - 直接在服务器上部署

## 📋 文档目录

### 🐳 容器化部署

- [容器化架构设计](容器化架构设计.md) - 完整的容器化架构方案 ⭐
- [Docker 部署](docker.md) - 使用 Docker 和 Docker Compose 部署
- [Kubernetes 部署](kubernetes.md) - 在 K8s 集群中部署应用
- [镜像构建](image-building.md) - 应用镜像构建和优化

### 🏭 生产环境

- [生产部署指南](production.md) - 生产环境部署完整流程
- [环境配置](environment-config.md) - 各环境配置管理
- [负载均衡](load-balancer.md) - 负载均衡器配置
- [SSL 证书](ssl-setup.md) - HTTPS 证书配置

### 📊 监控运维

- [监控配置](monitoring.md) - 系统监控和告警设置
- [日志管理](logging.md) - 日志收集和分析
- [性能监控](performance-monitoring.md) - 应用性能监控
- [健康检查](health-checks.md) - 服务健康状态检查

### 💾 数据管理

- [数据库部署](database-deployment.md) - 数据库集群部署
- [备份恢复](backup-recovery.md) - 数据备份和灾难恢复
- [数据迁移](data-migration.md) - 数据迁移和升级

### 🔧 运维工具

- [CI/CD 流水线](cicd-pipeline.md) - 持续集成和部署
- [自动化脚本](automation-scripts.md) - 运维自动化工具
- [故障处理](incident-response.md) - 故障响应和处理流程

## ⚡ 快速部署

### 开发环境（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd chaiguanjia

# 复制环境配置
cp .env.example .env

# 一键启动容器化环境
chmod +x scripts/docker-start.sh
./scripts/docker-start.sh dev

# 访问应用
# 前端: http://localhost:5173
# 后端API: http://localhost:8000/docs
# Celery监控: http://localhost:5555
# 数据库管理: http://localhost:5050
```

### 生产环境

```bash
# 1. 准备密钥文件
mkdir -p secrets
echo "your-postgres-password" > secrets/postgres_password.txt
echo "your-redis-password" > secrets/redis_password.txt
echo "your-rabbitmq-password" > secrets/rabbitmq_password.txt

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置生产环境配置

# 3. 启动生产环境
./scripts/docker-start.sh prod -d

# 4. 验证部署
docker-compose ps
curl http://localhost/health
```

## 🏗️ 架构图

### 生产环境架构

```
                    ┌─────────────┐
                    │   用户访问   │
                    └─────────────┘
                           │
                    ┌─────────────┐
                    │ 负载均衡器   │
                    │ (Nginx)     │
                    └─────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
 ┌─────────────┐   ┌─────────────┐   ┌─────────────┐
 │  前端服务1   │   │  前端服务2   │   │  前端服务N   │
 └─────────────┘   └─────────────┘   └─────────────┘
        │                  │                  │
        └──────────────────┼──────────────────┘
                           │
                    ┌─────────────┐
                    │ API网关     │
                    └─────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
 ┌─────────────┐   ┌─────────────┐   ┌─────────────┐
 │  后端服务1   │   │  后端服务2   │   │  后端服务N   │
 └─────────────┘   └─────────────┘   └─────────────┘
        │                  │                  │
        └──────────────────┼──────────────────┘
                           │
    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
    │ PostgreSQL  │ │    Redis    │ │  RabbitMQ   │
    │   集群       │ │    集群      │ │    集群      │
    └─────────────┘ └─────────────┘ └─────────────┘
```

### 监控架构

```
┌─────────────────────────────────────────────────────────┐
│                    监控数据收集                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │ 应用指标     │  │ 系统指标     │  │ 业务指标     │      │
│  │(Prometheus) │  │(Node Exp.)  │  │(Custom)     │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    数据存储和处理                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │ Prometheus  │  │ InfluxDB    │  │ Elasticsearch│      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    可视化和告警                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   Grafana   │  │ AlertManager│  │    Kibana   │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

## 🔧 部署清单

### 部署前检查

- [ ] 服务器资源充足（CPU、内存、磁盘）
- [ ] 网络连通性正常
- [ ] 域名和 SSL 证书准备就绪
- [ ] 数据库和缓存服务可用
- [ ] 环境变量配置正确
- [ ] 备份策略已制定

### 部署后验证

- [ ] 所有服务正常启动
- [ ] 健康检查通过
- [ ] API 接口响应正常
- [ ] 前端页面加载正常
- [ ] 数据库连接正常
- [ ] 监控指标正常
- [ ] 日志输出正常

## 📞 获取支持

### 部署问题

- 查看 [故障排除](../troubleshooting.md) 文档
- 检查 [常见问题](../user-guide/faq.md)
- 联系运维团队

### 紧急情况

- 生产环境问题：立即联系值班人员
- 数据安全问题：联系安全团队
- 重大故障：启动应急响应流程

---

**文档版本**: v1.0
**最后更新**: 2024-08-07
**维护团队**: 柴管家运维团队
