# 柴管家项目分支保护规则配置指南

## 概述

本文档详细说明了柴管家项目的分支保护规则配置要求，确保代码质量和团队协作的规范性。

## 分支策略

### 主要分支

- **main**: 生产分支，包含稳定的、经过测试的代码
- **develop**: 开发分支，用于集成新功能
- **feature/***: 功能分支，用于开发具体功能

### 分支保护目标

1. 防止直接推送到main分支
2. 确保所有代码变更都经过代码审查
3. 强制执行自动化质量检查
4. 维护清晰的提交历史

## GitHub分支保护规则配置

### 配置路径
```
GitHub仓库 → Settings → Branches → Add rule
```

### main分支保护配置

#### 基本设置
- **Branch name pattern**: `main`
- **Apply rule to**: `main` 分支

#### 必需配置项

##### 1. Require a pull request before merging ✅
```
☑ Require a pull request before merging
  ☑ Require approvals (设置为: 1)
  ☑ Dismiss stale PR approvals when new commits are pushed
  ☑ Require review from code owners
  ☑ Restrict approvals to users with write access
  ☑ Allow specified actors to bypass required pull requests (仅限紧急情况)
```

**说明**: 确保所有代码变更都必须通过Pull Request，并且需要至少1个审查者的批准。

##### 2. Require status checks to pass before merging ✅
```
☑ Require status checks to pass before merging
  ☑ Require branches to be up to date before merging
  
  Status checks (将在CI/CD配置后添加):
  - ci/github-actions (持续集成检查)
  - code-quality/lint (代码质量检查)
  - security/scan (安全扫描)
  - test/unit (单元测试)
  - test/integration (集成测试)
```

**说明**: 确保所有自动化检查都通过后才能合并代码。

##### 3. Require conversation resolution before merging ✅
```
☑ Require conversation resolution before merging
```

**说明**: 确保所有代码审查意见都得到解决。

##### 4. Require signed commits ✅
```
☑ Require signed commits
```

**说明**: 确保提交的真实性和完整性。

##### 5. Require linear history ✅
```
☑ Require linear history
```

**说明**: 保持清晰的提交历史，避免复杂的合并图。

##### 6. Include administrators ✅
```
☑ Include administrators
```

**说明**: 管理员也必须遵循相同的规则。

##### 7. Restrict pushes that create files ✅
```
☑ Restrict pushes that create files
```

**说明**: 防止意外的大文件推送。

### develop分支保护配置

#### 基本设置
- **Branch name pattern**: `develop`

#### 配置项
```
☑ Require a pull request before merging
  ☑ Require approvals (设置为: 1)
  ☑ Dismiss stale PR approvals when new commits are pushed
☑ Require status checks to pass before merging
  ☑ Require branches to be up to date before merging
☑ Require conversation resolution before merging
```

## 工作流程

### 功能开发流程

1. **创建功能分支**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/功能名称
   ```

2. **开发和提交**
   ```bash
   # 遵循Conventional Commits规范
   git commit -m "feat(scope): 功能描述"
   ```

3. **推送分支**
   ```bash
   git push origin feature/功能名称
   ```

4. **创建Pull Request**
   - 目标分支: `develop`
   - 填写PR模板
   - 等待自动化检查通过
   - 请求代码审查

5. **代码审查和合并**
   - 审查者检查代码质量
   - 解决所有审查意见
   - 自动化检查通过后合并

### 发布流程

1. **创建发布分支**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b release/版本号
   ```

2. **发布准备**
   - 更新版本号
   - 生成CHANGELOG
   - 最终测试

3. **合并到main**
   - 创建PR: `release/版本号` → `main`
   - 完整的CI/CD检查
   - 代码审查
   - 合并并打标签

4. **回合并到develop**
   ```bash
   git checkout develop
   git merge main
   git push origin develop
   ```

## 紧急修复流程

### hotfix分支处理

1. **创建hotfix分支**
   ```bash
   git checkout main
   git pull origin main
   git checkout -b hotfix/修复描述
   ```

2. **修复和测试**
   ```bash
   git commit -m "fix(scope): 修复描述"
   ```

3. **同时合并到main和develop**
   - 创建PR到main分支
   - 创建PR到develop分支
   - 加急审查流程

## 状态检查配置

### CI/CD检查项目

以下检查项目将在GitHub Actions配置后启用：

#### 代码质量检查
- **ESLint**: 前端代码规范检查
- **Black**: 后端代码格式检查
- **mypy**: Python类型检查
- **Prettier**: 代码格式化检查

#### 测试检查
- **Unit Tests**: 单元测试覆盖率 ≥ 80%
- **Integration Tests**: 集成测试通过
- **E2E Tests**: 端到端测试通过

#### 安全检查
- **CodeQL**: 代码安全分析
- **Dependency Scan**: 依赖漏洞扫描
- **Container Scan**: 容器镜像安全扫描

#### 构建检查
- **Frontend Build**: 前端构建成功
- **Backend Build**: 后端构建成功
- **Docker Build**: 容器镜像构建成功

## 异常处理

### 绕过保护规则

仅在以下紧急情况下可以考虑临时绕过规则：

1. **生产环境紧急修复**
2. **安全漏洞紧急修复**
3. **系统故障紧急恢复**

**绕过流程**:
1. 获得项目负责人批准
2. 记录绕过原因和时间
3. 事后补充完整的审查流程
4. 更新相关文档

### 规则调整

分支保护规则的调整需要：

1. **团队讨论**: 在团队会议中讨论必要性
2. **文档更新**: 更新本配置文档
3. **通知团队**: 提前通知所有开发者
4. **逐步实施**: 给团队适应时间

## 监控和度量

### 关键指标

- **PR合并时间**: 平均审查和合并时间
- **审查质量**: 发现问题的数量和类型
- **规则遵循率**: 规则违反的频率
- **自动化检查通过率**: 各项检查的通过率

### 定期审查

每月审查分支保护规则的有效性：

1. 分析违规情况
2. 评估规则合理性
3. 收集团队反馈
4. 优化配置参数

---

**注意**: 本配置需要在GitHub仓库创建后手动配置，无法通过代码自动化设置。
