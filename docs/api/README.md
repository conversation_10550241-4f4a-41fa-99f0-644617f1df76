# API 文档

柴管家平台 RESTful API 完整文档。

## 🚀 快速开始

### 基础信息

- **Base URL**: `http://localhost:8000` (开发环境)
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8

### 认证方式

API 使用 JWT (JSON Web Token) 进行身份认证：

```bash
# 获取访问令牌
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'

# 使用令牌访问受保护的接口
curl -X GET "http://localhost:8000/users/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 📚 接口分类

### 🔐 认证接口
- [用户认证](auth.md) - 登录、注册、令牌刷新等

### 👥 用户管理
- [用户管理](users.md) - 用户信息的增删改查

### 📡 渠道管理
- [渠道管理](channels.md) - 多渠道接入和配置

### 💬 消息处理
- [消息处理](messages.md) - 消息收发和处理

### 📊 统计分析
- [统计分析](analytics.md) - 数据统计和报表

## 🔧 在线文档

访问 [http://localhost:8000/docs](http://localhost:8000/docs) 查看交互式 API 文档（Swagger UI）。

## 📝 通用规范

### 请求格式

所有 POST/PUT 请求都应使用 JSON 格式：

```json
{
  "field1": "value1",
  "field2": "value2"
}
```

### 响应格式

API 统一返回格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-08-07T10:30:00Z"
}
```

### 错误处理

错误响应格式：

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": ["用户名不能为空"]
  },
  "timestamp": "2024-08-07T10:30:00Z"
}
```

### 状态码

- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `500` - 服务器内部错误
