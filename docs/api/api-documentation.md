# API 文档自动生成机制

柴管家项目使用 FastAPI 的内置功能自动生成 API 文档，提供交互式的 API 文档界面。

## 📚 文档访问地址

### 开发环境

- **Swagger UI**: [http://localhost:8000/docs](http://localhost:8000/docs)
- **ReDoc**: [http://localhost:8000/redoc](http://localhost:8000/redoc)
- **OpenAPI JSON**: [http://localhost:8000/openapi.json](http://localhost:8000/openapi.json)

### 生产环境

- **Swagger UI**: `https://your-domain.com/docs`
- **ReDoc**: `https://your-domain.com/redoc`
- **OpenAPI JSON**: `https://your-domain.com/openapi.json`

## 🔧 配置说明

### FastAPI 应用配置

在 `backend/app/main.py` 中配置了详细的 API 文档信息：

```python
app = FastAPI(
    title="柴管家 API",
    description="详细的 API 描述...",
    version="0.1.0",
    contact={
        "name": "柴管家开发团队",
        "url": "https://github.com/your-org/chaiguanjia",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    docs_url="/docs",        # Swagger UI 路径
    redoc_url="/redoc",      # ReDoc 路径
    openapi_url="/openapi.json",  # OpenAPI JSON 路径
)
```

### 自定义 OpenAPI 配置

项目包含自定义的 OpenAPI 配置，增强了文档的可读性：

```python
def custom_openapi():
    """自定义 OpenAPI 配置."""
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # 添加标签分类
    openapi_schema["tags"] = [
        {"name": "认证", "description": "用户认证和授权相关接口"},
        {"name": "用户管理", "description": "用户信息管理接口"},
        # ... 更多标签
    ]
    
    # 添加安全定义
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
        }
    }
    
    return openapi_schema
```

## 📝 编写 API 文档

### 基本接口文档

使用 FastAPI 的装饰器参数添加接口文档：

```python
@app.get(
    "/users/{user_id}",
    tags=["用户管理"],
    summary="获取用户信息",
    description="根据用户ID获取用户的详细信息",
    response_description="用户信息",
    responses={
        200: {"description": "成功获取用户信息"},
        404: {"description": "用户不存在"},
        401: {"description": "未授权访问"},
    }
)
async def get_user(user_id: int) -> UserResponse:
    """获取用户信息接口.
    
    根据提供的用户ID获取用户的详细信息，包括基本资料、权限等。
    
    Args:
        user_id: 用户唯一标识符
        
    Returns:
        UserResponse: 用户信息响应对象
        
    Raises:
        HTTPException: 当用户不存在时抛出404错误
        
    Example:
        ```python
        # 获取ID为123的用户信息
        response = await get_user(123)
        ```
    """
    # 接口实现...
```

### 使用 Pydantic 模型

定义清晰的请求和响应模型：

```python
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class UserCreateRequest(BaseModel):
    """用户创建请求模型."""
    
    username: str = Field(
        ..., 
        min_length=3, 
        max_length=50,
        description="用户名，3-50个字符",
        example="zhangsan"
    )
    email: str = Field(
        ..., 
        description="邮箱地址",
        example="<EMAIL>"
    )
    password: str = Field(
        ..., 
        min_length=8,
        description="密码，至少8个字符",
        example="password123"
    )
    full_name: Optional[str] = Field(
        None,
        description="真实姓名",
        example="张三"
    )

class UserResponse(BaseModel):
    """用户信息响应模型."""
    
    id: int = Field(..., description="用户ID", example=123)
    username: str = Field(..., description="用户名", example="zhangsan")
    email: str = Field(..., description="邮箱", example="<EMAIL>")
    full_name: Optional[str] = Field(None, description="真实姓名", example="张三")
    is_active: bool = Field(..., description="是否激活", example=True)
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        """Pydantic 配置."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
```

### 错误响应文档

定义统一的错误响应格式：

```python
class ErrorResponse(BaseModel):
    """错误响应模型."""
    
    success: bool = Field(False, description="操作是否成功")
    error: ErrorDetail = Field(..., description="错误详情")
    timestamp: datetime = Field(..., description="错误发生时间")

class ErrorDetail(BaseModel):
    """错误详情模型."""
    
    code: str = Field(..., description="错误代码", example="VALIDATION_ERROR")
    message: str = Field(..., description="错误消息", example="请求参数验证失败")
    details: List[str] = Field([], description="详细错误信息")
```

## 🏷️ 标签和分组

### 标签定义

API 接口按功能模块分组：

- **认证** - 用户登录、注册、令牌管理
- **用户管理** - 用户信息的增删改查
- **渠道管理** - 多渠道接入和配置
- **消息处理** - 消息收发和处理
- **统计分析** - 数据统计和报表
- **系统** - 系统状态和配置

### 使用标签

```python
@app.post("/auth/login", tags=["认证"])
async def login(credentials: LoginRequest) -> TokenResponse:
    """用户登录接口."""
    pass

@app.get("/users/me", tags=["用户管理"])
async def get_current_user() -> UserResponse:
    """获取当前用户信息."""
    pass
```

## 🔐 安全文档

### JWT 认证配置

在 OpenAPI 中配置 JWT 认证：

```python
# 在自定义 OpenAPI 配置中添加
openapi_schema["components"]["securitySchemes"] = {
    "BearerAuth": {
        "type": "http",
        "scheme": "bearer",
        "bearerFormat": "JWT",
        "description": "JWT 访问令牌认证",
    }
}
```

### 接口安全要求

为需要认证的接口添加安全要求：

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()

@app.get(
    "/users/me",
    tags=["用户管理"],
    dependencies=[Depends(security)],
    summary="获取当前用户信息"
)
async def get_current_user(token: str = Depends(security)) -> UserResponse:
    """获取当前用户信息."""
    # 验证令牌并返回用户信息
    pass
```

## 📊 示例和测试

### 添加请求示例

在 Pydantic 模型中添加示例数据：

```python
class LoginRequest(BaseModel):
    """登录请求模型."""
    
    username: str = Field(..., example="admin")
    password: str = Field(..., example="password123")
    
    class Config:
        schema_extra = {
            "example": {
                "username": "admin",
                "password": "password123"
            }
        }
```

### 响应示例

```python
@app.post(
    "/auth/login",
    tags=["认证"],
    responses={
        200: {
            "description": "登录成功",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "data": {
                            "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                            "token_type": "bearer",
                            "expires_in": 3600
                        },
                        "message": "登录成功",
                        "timestamp": "2024-08-07T10:30:00Z"
                    }
                }
            }
        },
        401: {
            "description": "认证失败",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": {
                            "code": "INVALID_CREDENTIALS",
                            "message": "用户名或密码错误",
                            "details": []
                        },
                        "timestamp": "2024-08-07T10:30:00Z"
                    }
                }
            }
        }
    }
)
async def login(credentials: LoginRequest) -> TokenResponse:
    """用户登录接口."""
    pass
```

## 🚀 文档部署

### 生产环境配置

在生产环境中，可以选择性地禁用或保护文档访问：

```python
import os

# 根据环境变量决定是否启用文档
docs_url = "/docs" if os.getenv("ENABLE_DOCS", "false").lower() == "true" else None
redoc_url = "/redoc" if os.getenv("ENABLE_DOCS", "false").lower() == "true" else None

app = FastAPI(
    title="柴管家 API",
    docs_url=docs_url,
    redoc_url=redoc_url,
    # ... 其他配置
)
```

### 文档访问控制

为文档添加访问控制：

```python
from fastapi import Request, HTTPException
from fastapi.responses import HTMLResponse

@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html(request: Request):
    """自定义 Swagger UI 页面."""
    # 添加访问控制逻辑
    if not is_authorized(request):
        raise HTTPException(status_code=401, detail="Unauthorized")
    
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
    )
```

## 📖 最佳实践

### 1. 文档编写规范

- 使用清晰、简洁的描述
- 提供完整的请求和响应示例
- 为所有参数添加说明和示例
- 使用统一的错误响应格式

### 2. 模型设计

- 使用 Pydantic 模型定义清晰的数据结构
- 添加字段验证和约束
- 提供有意义的示例数据
- 使用描述性的字段名称

### 3. 接口设计

- 遵循 RESTful 设计原则
- 使用合适的 HTTP 状态码
- 提供详细的错误信息
- 支持分页和过滤

### 4. 安全考虑

- 不在文档中暴露敏感信息
- 为需要认证的接口添加安全标记
- 在生产环境中考虑文档访问控制

---

**文档版本**: v1.0  
**最后更新**: 2024-08-07  
**维护团队**: 柴管家开发团队
