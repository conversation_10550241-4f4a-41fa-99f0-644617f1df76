import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],

  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/pages': resolve(__dirname, 'src/pages'),
      '@/hooks': resolve(__dirname, 'src/hooks'),
      '@/services': resolve(__dirname, 'src/services'),
      '@/stores': resolve(__dirname, 'src/stores'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/assets': resolve(__dirname, 'src/assets'),
      '@/layouts': resolve(__dirname, 'src/layouts'),
      '@/contexts': resolve(__dirname, 'src/contexts'),
      '@/constants': resolve(__dirname, 'src/constants'),
    },
  },

  // 开发服务器配置
  server: {
    port: 5173,
    host: '0.0.0.0', // 允许外部访问，支持容器环境
    open: false, // 容器环境不自动打开浏览器
    cors: true,
    hmr: {
      port: 5173, // HMR端口
      host: 'localhost' // HMR主机
    },
    watch: {
      usePolling: true, // 在容器环境中使用轮询监听文件变化
      interval: 1000, // 轮询间隔
    },
    proxy: {
      '/api': {
        target: 'http://backend:8000', // 容器内部通信
        changeOrigin: true,
        secure: false,
      },
    },
  },

  // 构建配置
  build: {
    outDir: 'dist',
    sourcemap: true,
    minify: 'terser',
    target: 'es2020',
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
      },
    },
  },

  // 预览服务器配置
  preview: {
    port: 4173,
    host: true,
  },

  // 环境变量前缀
  envPrefix: 'VITE_',
});
