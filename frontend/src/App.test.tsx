import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from './App';

describe('App Component', () => {
  test('renders without crashing', () => {
    render(<App />);
    expect(document.body).toBeInTheDocument();
  });

  test('renders app title', () => {
    render(<App />);
    const titleElement = screen.getByRole('heading', { name: /柴管家/i });
    expect(titleElement).toBeInTheDocument();
  });

  test('renders welcome message', () => {
    render(<App />);
    const welcomeMessage = screen.getByText(/欢迎使用柴管家前端应用/i);
    expect(welcomeMessage).toBeInTheDocument();
  });

  test('renders test button', () => {
    render(<App />);
    const testButton = screen.getByRole('button', { name: /点击测试/i });
    expect(testButton).toBeInTheDocument();
  });

  test('button click logs message', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    const user = userEvent.setup();

    render(<App />);
    const testButton = screen.getByRole('button', { name: /点击测试/i });

    await user.click(testButton);

    expect(consoleSpy).toHaveBeenCalledWith('欢迎使用柴管家！');

    consoleSpy.mockRestore();
  });

  test('renders with custom title', () => {
    const customTitle = '自定义标题';
    render(<App title={customTitle} />);
    const titleElement = screen.getByRole('heading', { name: customTitle });
    expect(titleElement).toBeInTheDocument();
  });
});
