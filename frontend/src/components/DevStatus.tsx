/**
 * 开发状态组件 - 用于测试热重载功能
 */

import React, { useState, useEffect } from 'react';

interface ApiStatus {
  status: string;
  timestamp: string;
  version: string;
  checks?: {
    database?: {
      status: string;
      message: string;
      response_time?: string;
    };
    redis?: {
      status: string;
      message: string;
      response_time?: string;
    };
    rabbitmq?: {
      status: string;
      message: string;
      response_time?: string;
    };
  };
}

const DevStatus: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<ApiStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // 获取API状态
  const fetchApiStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/health');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setApiStatus(data);
      setError(null);
      setLastUpdate(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
      setApiStatus(null);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取状态
  useEffect(() => {
    fetchApiStatus();
    
    // 每30秒自动刷新
    const interval = setInterval(fetchApiStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // 状态指示器组件
  const StatusIndicator: React.FC<{ status: string }> = ({ status }) => {
    const getStatusColor = (status: string) => {
      switch (status.toLowerCase()) {
        case 'healthy':
          return '#10b981'; // green
        case 'unhealthy':
          return '#ef4444'; // red
        default:
          return '#f59e0b'; // yellow
      }
    };

    return (
      <div
        style={{
          width: '12px',
          height: '12px',
          borderRadius: '50%',
          backgroundColor: getStatusColor(status),
          display: 'inline-block',
          marginRight: '8px',
        }}
      />
    );
  };

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: 'white',
      border: '1px solid #e5e7eb',
      borderRadius: '8px',
      padding: '16px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      minWidth: '300px',
      fontSize: '14px',
      zIndex: 1000,
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '12px',
      }}>
        <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 'bold' }}>
          🚀 开发环境状态
        </h3>
        <button
          onClick={fetchApiStatus}
          disabled={loading}
          style={{
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '4px 8px',
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '12px',
          }}
        >
          {loading ? '刷新中...' : '刷新'}
        </button>
      </div>

      {error ? (
        <div style={{ color: '#ef4444', marginBottom: '8px' }}>
          ❌ 连接失败: {error}
        </div>
      ) : apiStatus ? (
        <div>
          <div style={{ marginBottom: '8px' }}>
            <StatusIndicator status={apiStatus.status} />
            <strong>API状态:</strong> {apiStatus.status}
          </div>
          
          <div style={{ marginBottom: '8px' }}>
            <strong>版本:</strong> {apiStatus.version}
          </div>
          
          {apiStatus.checks && (
            <div style={{ marginBottom: '8px' }}>
              <strong>服务检查:</strong>
              <div style={{ marginLeft: '16px', marginTop: '4px' }}>
                {Object.entries(apiStatus.checks).map(([service, check]) => (
                  <div key={service} style={{ marginBottom: '4px' }}>
                    <StatusIndicator status={check.status} />
                    <span style={{ textTransform: 'capitalize' }}>{service}:</span>
                    <span style={{ marginLeft: '4px', color: check.status === 'healthy' ? '#10b981' : '#ef4444' }}>
                      {check.status}
                    </span>
                    {check.response_time && (
                      <span style={{ marginLeft: '8px', color: '#6b7280', fontSize: '12px' }}>
                        ({check.response_time})
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div style={{ fontSize: '12px', color: '#6b7280' }}>
            最后更新: {lastUpdate.toLocaleTimeString()}
          </div>
        </div>
      ) : loading ? (
        <div>⏳ 加载中...</div>
      ) : null}
      
      <div style={{
        marginTop: '12px',
        paddingTop: '8px',
        borderTop: '1px solid #e5e7eb',
        fontSize: '12px',
        color: '#6b7280',
      }}>
        💡 修改此组件测试热重载功能
      </div>
    </div>
  );
};

export default DevStatus;
