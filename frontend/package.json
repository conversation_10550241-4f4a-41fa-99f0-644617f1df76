{"name": "chaiguanjia-frontend", "version": "0.1.0", "private": true, "description": "柴管家前端应用 - React + TypeScript + Vite", "keywords": ["react", "typescript", "vite", "chaiguanjia"], "author": "柴管家开发团队", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "pre-commit": "npm run type-check && npm run lint && npm run format:check && npm run test"}, "dependencies": {"axios": "^1.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "zustand": "^4.3.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.4.0", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^3.1.0", "eslint": "^8.35.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "jest": "^29.4.0", "jest-environment-jsdom": "^29.4.0", "prettier": "^2.8.4", "terser": "^5.43.1", "typescript": "^4.9.4", "vite": "^4.1.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}