module.exports = {
  // 基本格式化选项
  printWidth: 80,                    // 每行最大字符数
  tabWidth: 2,                       // 缩进空格数
  useTabs: false,                    // 使用空格而不是制表符
  semi: true,                        // 语句末尾添加分号
  singleQuote: true,                 // 使用单引号
  quoteProps: 'as-needed',           // 仅在需要时为对象属性添加引号
  
  // JSX 相关
  jsxSingleQuote: true,              // JSX 中使用单引号
  
  // 尾随逗号
  trailingComma: 'es5',              // 在 ES5 有效的地方添加尾随逗号
  
  // 空格相关
  bracketSpacing: true,              // 对象字面量的大括号间添加空格
  bracketSameLine: false,            // 将多行 JSX 元素的 > 放在下一行
  
  // 箭头函数参数
  arrowParens: 'avoid',              // 单参数箭头函数省略括号
  
  // 换行符
  endOfLine: 'lf',                   // 使用 LF 换行符
  
  // HTML 相关
  htmlWhitespaceSensitivity: 'css',  // HTML 空白敏感性
  
  // 嵌入式语言格式化
  embeddedLanguageFormatting: 'auto',
  
  // 文件覆盖配置
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 120,
        tabWidth: 2,
      },
    },
    {
      files: '*.md',
      options: {
        printWidth: 100,
        proseWrap: 'always',
      },
    },
    {
      files: '*.{css,scss,less}',
      options: {
        singleQuote: false,
      },
    },
  ],
};
