# 开发容器专用配置
# 覆盖主配置以支持VSCode开发容器

version: '3.8'

services:
  backend:
    # 开发容器特定配置
    build:
      context: ../backend
      dockerfile: Dockerfile
      target: development
    volumes:
      # 代码挂载
      - ../backend:/app:cached
      - /app/__pycache__
      # VSCode配置挂载
      - ../.vscode:/app/.vscode:cached
      # Git配置挂载
      - ~/.gitconfig:/home/<USER>/.gitconfig:ro
      - ~/.ssh:/home/<USER>/.ssh:ro
    environment:
      # 开发环境变量
      PYTHONPATH: /app
      DEVELOPMENT: "true"
      DEBUG: "true"
      # 调试配置
      PYTHONDONTWRITEBYTECODE: "1"
      PYTHONUNBUFFERED: "1"
    # 保持容器运行
    command: sleep infinity
    # 特权模式以支持调试
    cap_add:
      - SYS_PTRACE
    security_opt:
      - seccomp:unconfined
    
  # 前端开发服务
  frontend-dev:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: development
    volumes:
      - ../frontend:/app:cached
      - /app/node_modules
      - ../.vscode:/app/.vscode:cached
    environment:
      NODE_ENV: development
      VITE_API_BASE_URL: http://backend:8000
    ports:
      - "5173:5173"
    command: npm run dev -- --host 0.0.0.0
    depends_on:
      - backend
