{"name": "柴管家开发环境", "dockerComposeFile": ["../docker-compose.yml", "../docker-compose.dev.yml", "docker-compose.devcontainer.yml"], "service": "backend", "workspaceFolder": "/app", "shutdownAction": "stopCompose", "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.vscode-pylance", "ms-python.black-formatter", "ms-python.isort", "ms-python.flake8", "ms-toolsai.jupyter", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-typescript-next", "ms-azuretools.vscode-docker", "ms-vscode-remote.remote-containers", "ms-mssql.mssql", "cweijan.vscode-postgresql-client2", "eamodio.gitlens", "github.vscode-pull-request-github", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.makefile-tools", "streetsidesoftware.code-spell-checker", "humao.rest-client", "42crunch.vscode-openapi", "ms-python.pytest", "hbenl.vscode-test-explorer"], "settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.sortImports.args": ["--profile", "black"], "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["tests"], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}, "files.associations": {"*.env*": "dotenv", "docker-compose*.yml": "dockercompose", "Dockerfile*": "dockerfile"}, "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.cwd": "/app", "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic"}}}, "forwardPorts": [8000, 5173, 5432, 6379, 5672, 15672, 5555, 5050, 8081], "portsAttributes": {"8000": {"label": "FastAPI Backend", "onAutoForward": "notify"}, "5173": {"label": "Vite Frontend", "onAutoForward": "openBrowser"}, "5050": {"label": "PgAdmin", "onAutoForward": "silent"}, "5555": {"label": "Celery Flower", "onAutoForward": "silent"}, "8081": {"label": "Redis Commander", "onAutoForward": "silent"}, "15672": {"label": "RabbitMQ Management", "onAutoForward": "silent"}}, "postCreateCommand": "pip install -e . && pre-commit install", "mounts": ["source=${localWorkspaceFolder}/.vscode,target=/app/.vscode,type=bind,consistency=cached", "source=${localWorkspaceFolder}/backend,target=/app,type=bind,consistency=cached"], "containerEnv": {"PYTHONPATH": "/app", "DEVELOPMENT": "true"}, "remoteUser": "chaiguanjia", "updateRemoteUserUID": true}